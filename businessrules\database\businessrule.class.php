<?php
session_start();
include_once("../../connection.php");
include_once("../../common_functions.php");
class BusinessRuleClass extends CommonClass {
	public $responseParameters;
	public $connectionlink;
	public function __construct(){
		$this->connectionlink = Connection::DBConnect();
	}

	public function GetDispositions ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}
			//$query = "select * from disposition where status = 'Active' order by disposition";
			//$query = "select * from disposition where status = 'Active' and (isnull(parent_disposition_id) or parent_disposition_id = 0) order by disposition";
			$query = "select * from disposition where status = 'Active' and sub_disposition = 0 order by disposition";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Dispositions Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetWorkflows ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}
			$query = "select * from workflow where status = 'Active' order by workflow";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Workflows Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetInputResults ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}

			// If workflow_id is 'all', get all input results with workflow names
			if (isset($data['workflow_id']) && $data['workflow_id'] === 'all') {
				$query = "select wi.*, w.workflow as workflow_name from workflow_input wi
						  left join workflow w on wi.workflow_id = w.workflow_id
						  order by wi.input";
			} else {
				$query = "select wi.*, w.workflow as workflow_name from workflow_input wi
						  left join workflow w on wi.workflow_id = w.workflow_id
						  where wi.workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."'
						  order by wi.input";
			}

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Input Results Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetBusinessRuleAttributes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}
			$query = "select * from business_rule_attributes where status = 'Active' order by attribute_name";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Attributes Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetAttributeValues ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}
			if($data['attribute_id'] == '15') {// If Manufacturer
				$query = "select idManufacturer,ManufacturerName from manufacturer order by ManufacturerName";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$manufacturers = array();
				$i = 0;
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($row = mysqli_fetch_assoc($q)){
						$manufacturers[$i] = $row;
						$i = $i + 1;
					}
					$json['Success'] = true;
					$json['Result'] = $manufacturers;
				} else {
					$json['Success'] = false;
					$json['Result'] = "No Values Available";
				}
				return json_encode($json);
			}

			if($data['attribute_id'] == '27' || $data['attribute_id'] == '28') {// If Capability
				//$query = "select idManufacturer,ManufacturerName from manufacturer order by ManufacturerName";
				$query = "select distinct(capability) as value from catlog_creation order by capability";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$capabilities = array();
				$i = 0;
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($row = mysqli_fetch_assoc($q)){
						$value_arr = explode("$",$row['value']);
						for($j=0;$j<count($value_arr);$j++) {
							//if(! array_column($capabilities,$value_arr[$j])) {
							if(! in_array($value_arr[$j], array_column($capabilities, 'value'))) {
								$capabilities[$i]['value'] = $value_arr[$j];
								$i = $i + 1;
							}
						}
						//$capabilities[$i] = $row;

					}
					$json['Success'] = true;
					$json['Result'] = $capabilities;
				} else {
					$json['Success'] = false;
					$json['Result'] = "No Values Available";
				}
				return json_encode($json);
			}


			if($data['attribute_id'] == '35' ) {// If attribute_id
				//$query = "select idManufacturer,ManufacturerName from manufacturer order by ManufacturerName";
				$query = "select distinct(attribute_id) as value from catlog_creation order by attribute_id";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$capabilities = array();
				$i = 0;
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($row = mysqli_fetch_assoc($q)){
						$value_arr = explode("$",$row['value']);
						for($j=0;$j<count($value_arr);$j++) {
							//if(! array_column($capabilities,$value_arr[$j])) {
							if(! in_array($value_arr[$j], array_column($capabilities, 'value'))) {
								$capabilities[$i]['value'] = $value_arr[$j];
								$i = $i + 1;
							}
						}
						//$capabilities[$i] = $row;

					}
					$json['Success'] = true;
					$json['Result'] = $capabilities;
				} else {
					$json['Success'] = false;
					$json['Result'] = "No Values Available";
				}
				return json_encode($json);
			}


			if($data['attribute_id'] == '34') {// If Cluster Eligibility
				$query = "select distinct(cluster_eligibility) as value from catlog_creation order by cluster_eligibility";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$capabilities = array();
				$i = 0;
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($row = mysqli_fetch_assoc($q)){
						// $value_arr = explode("$",$row['value']);
						// for($j=0;$j<count($value_arr);$j++) {
						// 	if(! in_array($value_arr[$j], array_column($capabilities, 'value'))) {
						// 		$capabilities[$i]['value'] = $value_arr[$j];
						// 		$i = $i + 1;
						// 	}
						// }
						$capabilities[$i] = $row;
						$i = $i + 1;

					}
					$json['Success'] = true;
					$json['Result'] = $capabilities;
				} else {
					$json['Success'] = false;
					$json['Result'] = "No Values Available";
				}
				return json_encode($json);
			}

			if($data['attribute_id'] == '17') {// If Source
				$query = "select CustomerShotCode as value from customer order by CustomerShotCode";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$customers = array();
				$i = 0;
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($row = mysqli_fetch_assoc($q)){
						$customers[$i] = $row;
						$i = $i + 1;
					}
					$json['Success'] = true;
					$json['Result'] = $customers;
				} else {
					$json['Success'] = false;
					$json['Result'] = "No Values Available";
				}
				return json_encode($json);
			}

			if($data['attribute_id'] == '24') {// If Source Type
				$query = "select Cumstomertype as value from customertype order by Cumstomertype";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$customers = array();
				$i = 0;
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($row = mysqli_fetch_assoc($q)){
						$customers[$i] = $row;
						$i = $i + 1;
					}
					$json['Success'] = true;
					$json['Result'] = $customers;
				} else {
					$json['Success'] = false;
					$json['Result'] = "No Values Available";
				}
				return json_encode($json);
			}

			if($data['attribute_id'] == '32') {// If Material Type
				$query = "select MaterialType as value from material_types order by MaterialType";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$customers = array();
				$i = 0;
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($row = mysqli_fetch_assoc($q)){
						$customers[$i] = $row;
						$i = $i + 1;
					}
					$json['Success'] = true;
					$json['Result'] = $customers;
				} else {
					$json['Success'] = false;
					$json['Result'] = "No Values Available";
				}
				return json_encode($json);
			}

			if($data['attribute_id'] == '33') {// If Customer ID
				$query = "select Customer as value from aws_customers order by Customer";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$customers = array();
				$i = 0;
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($row = mysqli_fetch_assoc($q)){
						$customers[$i] = $row;
						$i = $i + 1;
					}
					$json['Success'] = true;
					$json['Result'] = $customers;
				} else {
					$json['Success'] = false;
					$json['Result'] = "No Values Available";
				}
				return json_encode($json);
			}

			if($data['attribute_id'] == '39') {// If COO
				$query = "select COOID,COO from COO order by COO";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$manufacturers = array();
				$i = 0;
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($row = mysqli_fetch_assoc($q)){
						$manufacturers[$i] = $row;
						$i = $i + 1;
					}
					$json['Success'] = true;
					$json['Result'] = $manufacturers;
				} else {
					$json['Success'] = false;
					$json['Result'] = "No Values Available";
				}
				return json_encode($json);
			}

			$query = "select * from business_rule_attribute_values where attribute_id = '".mysqli_real_escape_string($this->connectionlink,$data['attribute_id'])."' order by value";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Values Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	// Sequential Priority Management Function
	private function manageSequentialPriority($version_id, $desired_priority, $exclude_rule_id = null) {
		try {
			$version_id = mysqli_real_escape_string($this->connectionlink, $version_id);
			$desired_priority = (int)$desired_priority;

			// Build exclusion condition for updates (when editing existing rule)
			$exclude_condition = '';
			if ($exclude_rule_id !== null) {
				$exclude_condition = " AND rule_id != '" . mysqli_real_escape_string($this->connectionlink, $exclude_rule_id) . "'";
			}

			// Get current max priority for this version (only consider active rules, exclude archived)
			$max_query = "SELECT COALESCE(MAX(priority), 0) as max_priority FROM business_rule
						  WHERE version_id = '$version_id' AND status = 'Active' AND status != 'Archived'$exclude_condition";
			$max_result = mysqli_query($this->connectionlink, $max_query);

			if(mysqli_error($this->connectionlink)) {
				return array('success' => false, 'error' => mysqli_error($this->connectionlink));
			}

			$max_row = mysqli_fetch_assoc($max_result);
			$current_max = (int)$max_row['max_priority'];

			// If desired priority is beyond current max, just use next sequential number
			if ($desired_priority > $current_max + 1) {
				$desired_priority = $current_max + 1;
			}

			// If desired priority is less than 1, set it to 1
			if ($desired_priority < 1) {
				$desired_priority = 1;
			}

			// Shift existing ACTIVE rules to make room for the new priority (exclude archived)
			$shift_query = "UPDATE business_rule SET priority = priority + 1
							WHERE version_id = '$version_id'
							AND status = 'Active' AND status != 'Archived'
							AND priority >= $desired_priority$exclude_condition
							ORDER BY priority DESC";

			$shift_result = mysqli_query($this->connectionlink, $shift_query);

			if(mysqli_error($this->connectionlink)) {
				return array('success' => false, 'error' => mysqli_error($this->connectionlink));
			}

			return array('success' => true, 'final_priority' => $desired_priority);

		} catch (Exception $e) {
			return array('success' => false, 'error' => $e->getMessage());
		}
	}

	// Clean up priority gaps to ensure sequential priorities (1,2,3,4...)
	private function cleanupPriorityGaps($version_id) {
		try {
			$version_id = mysqli_real_escape_string($this->connectionlink, $version_id);

			// Get all ACTIVE rules ordered by priority (only consider active rules for sequencing, exclude archived)
			$query = "SELECT rule_id, priority FROM business_rule
					  WHERE version_id = '$version_id' AND status = 'Active' AND status != 'Archived'
					  ORDER BY priority ASC";
			$result = mysqli_query($this->connectionlink, $query);

			if(mysqli_error($this->connectionlink)) {
				return array('success' => false, 'error' => mysqli_error($this->connectionlink));
			}

			$rules = array();
			while($row = mysqli_fetch_assoc($result)) {
				$rules[] = $row;
			}

			// Reassign sequential priorities starting from 1 (only for active rules)
			$new_priority = 1;
			$updates_made = 0;

			foreach($rules as $rule) {
				if((int)$rule['priority'] != $new_priority) {
					$update_query = "UPDATE business_rule SET priority = $new_priority
									WHERE rule_id = '" . mysqli_real_escape_string($this->connectionlink, $rule['rule_id']) . "'";
					$update_result = mysqli_query($this->connectionlink, $update_query);

					if(mysqli_error($this->connectionlink)) {
						return array('success' => false, 'error' => mysqli_error($this->connectionlink));
					}
					$updates_made++;
				}
				$new_priority++;
			}

			// Log the cleanup activity for debugging
			if($updates_made > 0) {
				error_log("Priority gap cleanup: Updated $updates_made rules in version $version_id to ensure sequential priorities");
			}

			return array('success' => true, 'updates_made' => $updates_made);

		} catch (Exception $e) {
			return array('success' => false, 'error' => $e->getMessage());
		}
	}

	public function CreateBusinessRule ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Business Rule Page';
				return json_encode($json);
			}
			if(!$data['rule_id']) { //Create New Rule

				//Start check If rule is capable to edit
				$query32 = "select * from business_rule_versions where version_id = '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."' ";
				$q32 = mysqli_query($this->connectionlink,$query32);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row32 = mysqli_fetch_assoc($q32);
					if($row32['status'] != 'Pending') {
						$json['Success'] = false;
						$json['Result'] = 'New Business Rules can be created only in Pending Versions';
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Business Rule Version';
					return json_encode($json);
				}
				//End check If rule is capable to edit


				//Start check IF Rule Name exists with in the version
				$query22 = "select count(*) from business_rule where version_id = '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."' and rule_name = '".mysqli_real_escape_string($this->connectionlink,$data['rule_name'])."'";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row22 = mysqli_fetch_assoc($q22);
					if($row22['count(*)'] > 0) {
						$json['Success'] = false;
						$json['Result'] = 'Rule Name already exists in selected Version';
						return json_encode($json);
					}
				}
				//End check IF Rule Name exists with in the version



				$rule_summary = '';
				//Start get input name
				if(isset($data['input_id']) && !empty($data['input_id'])) {
					// Check if input_id is "all"
					if($data['input_id'] === 'all' || (is_array($data['input_id']) && in_array('all', $data['input_id']))) {
						$rule_summary = 'input = "All" ,';
					} else {
						// Convert input_id to array if it's a string
						$inputIds = is_array($data['input_id']) ? $data['input_id'] : explode(',', $data['input_id']);

						// Get all input names
						$inputNames = [];
						foreach ($inputIds as $inputId) {
							$query2 = "select input from workflow_input where input_id = '".mysqli_real_escape_string($this->connectionlink, $inputId)."'";
							$q2 = mysqli_query($this->connectionlink, $query2);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row2 = mysqli_fetch_assoc($q2);
								$inputNames[] = $row2['input'];
							}
						}

						// Join input names with OR
						if (!empty($inputNames)) {
							$rule_summary = 'input = "' . implode(' OR ', $inputNames) . '" ,';
						}
					}
				}
				//End get input name

				// Check if conditions exist and is an array
				if(isset($data['conditions']) && is_array($data['conditions']) && count($data['conditions']) > 0) {
					for($i=0;$i<count($data['conditions']);$i++) {
						if($i == 0) {
							$rule_summary = $rule_summary . ' '.$data['conditions'][$i]['attribute_name'];
						} else {
							$rule_summary = $rule_summary . ', '.$data['conditions'][$i]['attribute_name'];
						}

						if($data['conditions'][$i]['operator'] == '==') {
							$rule_summary = $rule_summary .' =';
						}
						if($data['conditions'][$i]['operator'] == '!=') {
							$rule_summary = $rule_summary .' <>';
						}

						if($data['conditions'][$i]['operator'] == '<') {
							$rule_summary = $rule_summary .' <';
						}

						if($data['conditions'][$i]['operator'] == '>') {
							$rule_summary = $rule_summary .' >';
						}

						if($data['conditions'][$i]['operator'] == 'contains') {
							$rule_summary = $rule_summary .' Contains';
						}

						if($data['conditions'][$i]['operator'] == 'not_contains') {
							$rule_summary = $rule_summary .' Not Contains';
						}

						$rule_summary = $rule_summary . ' "'.$data['conditions'][$i]['value'].'" ';
					}
				} else {
					// No conditions -
				}

				//Start inserting facility in rule summary
				if($data['FacilityID'] == 'all' || $data['FacilityID'] == 'All') {
					$rule_summary = $rule_summary . ' , Facility = "All" ';
				} else if($data['FacilityID'] > 0) {
					//Get facility name and add to rule summary
					$query15 = "select FacilityName from facility where FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."'";
					$q15 = mysqli_query($this->connectionlink,$query15);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row15 = mysqli_fetch_assoc($q15);
						$rule_summary = $rule_summary . ' , Facility = "'.$row15['FacilityName'].'"';
					}

				}
				//End inserting facility in rule summary

				//Start check for duplicate rule (check all business rule fields + actual conditions, not rule_summary)
				$duplicate_conditions = array();

				// Build conditions for duplicate check - all business rule fields
				$duplicate_conditions[] = "version_id = '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."'";

				// Check Customer ID (AWSCustomerID)
				if(isset($data['AWSCustomerID']) && !empty($data['AWSCustomerID'])) {
					// Handle array or string for Customer ID
					if(is_array($data['AWSCustomerID'])) {
						if(in_array('All', $data['AWSCustomerID']) || in_array('all', $data['AWSCustomerID'])) {
							$duplicate_conditions[] = "AWSCustomerID = 'All'";
						} else {
							$customerStr = implode(',', $data['AWSCustomerID']);
							$duplicate_conditions[] = "AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$customerStr)."'";
						}
					} else {
						$duplicate_conditions[] = "AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['AWSCustomerID'])."'";
					}
				} else {
					$duplicate_conditions[] = "(AWSCustomerID IS NULL OR AWSCustomerID = '' OR AWSCustomerID = '0')";
				}

				// Check Workflow ID
				if($data['workflow_id'] != '' && $data['workflow_id'] != '0') {
					$duplicate_conditions[] = "workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."'";
				} else {
					$duplicate_conditions[] = "(workflow_id IS NULL OR workflow_id = '' OR workflow_id = '0')";
				}

				// Check Facility ID
				if($data['FacilityID'] != '' && $data['FacilityID'] != '0') {
					$duplicate_conditions[] = "FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."'";
				} else {
					$duplicate_conditions[] = "(FacilityID IS NULL OR FacilityID = '' OR FacilityID = '0')";
				}

				// Check Part Types
				if(isset($data['part_types']) && !empty($data['part_types']) && $data['part_types'] != '0') {
					// Convert array to comma-separated string if needed
					if(is_array($data['part_types'])) {
						if(in_array('All', $data['part_types']) || in_array('all', $data['part_types'])) {
							$partTypesStr = 'All';
						} else {
							$partTypesStr = implode(',', $data['part_types']);
						}
					} else {
						$partTypesStr = $data['part_types'];
					}
					$duplicate_conditions[] = "part_types = '".mysqli_real_escape_string($this->connectionlink,$partTypesStr)."'";
				} else {
					$duplicate_conditions[] = "(part_types IS NULL OR part_types = '' OR part_types = '0')";
				}

				// Check Source Type (idCustomertype)
				if($data['idCustomertype'] != '' && $data['idCustomertype'] != '0') {
					$duplicate_conditions[] = "idCustomertype = '".mysqli_real_escape_string($this->connectionlink,$data['idCustomertype'])."'";
				} else {
					$duplicate_conditions[] = "(idCustomertype IS NULL OR idCustomertype = '' OR idCustomertype = '0')";
				}

				// Check Material Type
				if($data['MaterialType'] != '' && $data['MaterialType'] != '0') {
					$duplicate_conditions[] = "MaterialType = '".mysqli_real_escape_string($this->connectionlink,$data['MaterialType'])."'";
				} else {
					$duplicate_conditions[] = "(MaterialType IS NULL OR MaterialType = '' OR MaterialType = '0')";
				}

				// Check Input ID
				if($data['input_id'] != '' && $data['input_id'] != '0') {
					// Convert array to comma-separated string if needed
					if(is_array($data['input_id'])) {
						$inputIdStr = implode(',', $data['input_id']);
					} else {
						$inputIdStr = $data['input_id'];
					}
					$duplicate_conditions[] = "input_id = '".mysqli_real_escape_string($this->connectionlink,$inputIdStr)."'";
				} else {
					$duplicate_conditions[] = "(input_id IS NULL OR input_id = '' OR input_id = '0')";
				}

				// Get potential duplicate rules based on business rule fields only (not rule_summary)
				$potential_duplicates_query = "SELECT rule_id, rule_name FROM business_rule WHERE " . implode(' AND ', $duplicate_conditions);

				// Debug: Log the potential duplicates query
				error_log("POTENTIAL DUPLICATES QUERY (CREATE): " . $potential_duplicates_query);

				$potential_duplicates_result = mysqli_query($this->connectionlink, $potential_duplicates_query);

				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				// Check if any potential duplicates have the same conditions
				if(mysqli_num_rows($potential_duplicates_result) > 0) {
					while($potential_duplicate = mysqli_fetch_assoc($potential_duplicates_result)) {
						$existing_rule_id = $potential_duplicate['rule_id'];

						// Get conditions for the existing rule
						$existing_conditions_query = "SELECT attribute_id, operator, value FROM business_rule_condition WHERE rule_id = '".mysqli_real_escape_string($this->connectionlink, $existing_rule_id)."' ORDER BY attribute_id, operator, value";
						$existing_conditions_result = mysqli_query($this->connectionlink, $existing_conditions_query);

						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						// Build array of existing conditions
						$existing_conditions = array();
						while($existing_condition = mysqli_fetch_assoc($existing_conditions_result)) {
							$existing_conditions[] = $existing_condition['attribute_id'] . '|' . $existing_condition['operator'] . '|' . $existing_condition['value'];
						}

						// Build array of new conditions
						$new_conditions = array();
						if(isset($data['conditions']) && is_array($data['conditions']) && count($data['conditions']) > 0) {
							foreach($data['conditions'] as $condition) {
								$value = '';
								if(isset($condition['value_array']) && is_array($condition['value_array'])) {
									for($j=0; $j<count($condition['value_array']); $j++) {
										if($j > 0) {
											$value = $value . '@#$';
										}
										$value = $value . $condition['value_array'][$j];
									}
								} else {
									$value = $condition['value'];
								}
								$new_conditions[] = $condition['attribute_id'] . '|' . $condition['operator'] . '|' . $value;
							}
						}

						// Sort both arrays for comparison
						sort($existing_conditions);
						sort($new_conditions);

						// Compare conditions
						if($existing_conditions === $new_conditions) {
							$json['Success'] = false;
							$json['Result'] = 'A rule with the same conditions already exists: ' . $potential_duplicate['rule_name'] ;
							return json_encode($json);
						}
					}
				}
				//End check for duplicate rule

				//Start Sequential Priority Management for CREATE
				$result = $this->manageSequentialPriority($data['version_id'], $data['priority'], null);
				if(!$result['success']) {
					$json['Success'] = false;
					$json['Result'] = $result['error'];
					return json_encode($json);
				}
				//End Sequential Priority Management for CREATE

				//$query = "insert into business_rule (rule_name,rule_description,priority,disposition_id,status,created_date,created_by,rule_summary,FacilityID";
				$query = "insert into business_rule (rule_name,rule_description,priority,disposition_id,status,created_date,created_by,rule_summary,version_id";

				// Add AWSCustomerID to the query if it exists
				if($data['AWSCustomerID'] != '') {
					$query = $query . ",AWSCustomerID";
				}

				// Add workflow_id to the query if it exists
				if($data['workflow_id'] != '') {
					$query = $query . ",workflow_id";
				}

				// Add part_types to the query if it exists
				if(isset($data['part_types']) && !empty($data['part_types'])) {
					$query = $query . ",part_types";
				}

				// Add idCustomertype to the query if it exists
				if(isset($data['idCustomertype']) && !empty($data['idCustomertype']) && $data['idCustomertype'] != '0') {
					$query = $query . ",idCustomertype";
				}

				// Add Facility to the query if it exists
				if(isset($data['FacilityID']) && !empty($data['FacilityID']) && $data['FacilityID'] != '0') {
					$query = $query . ",FacilityID";
				}

				// Add MaterialType to the query if it exists
				if(isset($data['MaterialType']) && !empty($data['MaterialType']) && $data['MaterialType'] != '0') {
					$query = $query . ",MaterialType";
				}

				// Only add workflow_id if it wasn't already added
				if(empty($data['workflow_id'])) {
					$query = $query . ",workflow_id";
				}

				if(isset($data['input_id']) && !empty($data['input_id'])) {
					$query = $query . ",input_id";
				}
				if($data['business_SubDispositionExists'] && $data['sub_disposition_id'] > 0) {
					$query = $query . ",sub_disposition_id";
				}
				//$query = $query . ") values ('".mysqli_real_escape_string($this->connectionlink,$data['rule_name'])."','".mysqli_real_escape_string($this->connectionlink,$data['rule_description'])."','".mysqli_real_escape_string($this->connectionlink,$data['priority'])."','".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','Active',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$rule_summary)."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."'";
				$query = $query . ") values ('".mysqli_real_escape_string($this->connectionlink,$data['rule_name'])."','".mysqli_real_escape_string($this->connectionlink,$data['rule_description'])."','".mysqli_real_escape_string($this->connectionlink,$data['priority'])."','".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','Active',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$rule_summary)."','".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."'";

				// Add AWSCustomerID value to the query if it exists
				if(isset($data['AWSCustomerID']) && !empty($data['AWSCustomerID'])) {
					// Check if "All" is selected
					if(is_array($data['AWSCustomerID']) && (in_array('All', $data['AWSCustomerID']) || in_array('all', $data['AWSCustomerID']))) {
						$query = $query .",'All'";
					} else {
						// Convert array to comma-separated string if needed
						if(is_array($data['AWSCustomerID'])) {
							$customerStr = implode(',', $data['AWSCustomerID']);
						} else {
							$customerStr = $data['AWSCustomerID'];
						}
						$query = $query .",'".mysqli_real_escape_string($this->connectionlink, $customerStr)."'";
					}
				}

				// Add workflow_id value to the query if it exists
				if($data['workflow_id'] != '') {
					$query = $query .",'".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."'";
				}

				// Add part_types value to the query if it exists
				if(isset($data['part_types']) && !empty($data['part_types'])) {
					// Check if "All" is selected
					if(is_array($data['part_types']) && (in_array('All', $data['part_types']) || in_array('all', $data['part_types']))) {
						$query = $query .",'All'";
					} else {
						// Convert array to comma-separated string if needed
						if(is_array($data['part_types'])) {
							$partTypesStr = implode(',', $data['part_types']);
						} else {
							$partTypesStr = $data['part_types'];
						}
						$query = $query .",'".mysqli_real_escape_string($this->connectionlink, $partTypesStr)."'";
					}
				}

				// Add idCustomertype value to the query if it exists
				if(isset($data['idCustomertype']) && !empty($data['idCustomertype']) && $data['idCustomertype'] != '0') {
					$query = $query .",'".mysqli_real_escape_string($this->connectionlink, $data['idCustomertype'])."'";
				}

				// Add facility value to the query if it exists
				if(isset($data['FacilityID']) && !empty($data['FacilityID']) && $data['FacilityID'] != '0') {
					$query = $query .",'".mysqli_real_escape_string($this->connectionlink, $data['FacilityID'])."'";
				}

				// Add MaterialType value to the query if it exists
				if(isset($data['MaterialType']) && !empty($data['MaterialType']) && $data['MaterialType'] != '0') {
					$query = $query .",'".mysqli_real_escape_string($this->connectionlink, $data['MaterialType'])."'";
				}

				// Only add workflow_id value if it wasn't already added
				if(empty($data['workflow_id'])) {
					$query = $query .",'".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."'";
				}

				if(isset($data['input_id']) && !empty($data['input_id'])) {
					// Convert array to comma-separated string if needed
					if(is_array($data['input_id'])) {
						$inputIdStr = implode(',', $data['input_id']);
					} else {
						$inputIdStr = $data['input_id'];
					}
					$query = $query .",'".mysqli_real_escape_string($this->connectionlink,$inputIdStr)."'";
				}
				if($data['business_SubDispositionExists'] && $data['sub_disposition_id'] > 0) {
					$query = $query .",'".mysqli_real_escape_string($this->connectionlink,$data['sub_disposition_id'])."'";
				}
				$query = $query .")";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$rule_id = mysqli_insert_id($this->connectionlink);

				// Only insert conditions if they exist
				if(isset($data['conditions']) && is_array($data['conditions']) && count($data['conditions']) > 0) {
					for($i=0;$i<count($data['conditions']);$i++) {

						$value = '';
						$multiple_values = 0;
	                    for($j=0;$j<count($data['conditions'][$i]['value_array']);$j++) {
	                        if($j > 0) {
	                            $value = $value . '@#$';
								$multiple_values = 1;
	                        }
	                        $value = $value . $data['conditions'][$i]['value_array'][$j];
	                    }

						//$query1 = "insert into business_rule_condition (rule_id,attribute_id,operator,value,created_date,created_by) values ('".mysqli_real_escape_string($this->connectionlink,$rule_id)."','".mysqli_real_escape_string($this->connectionlink,$data['conditions'][$i]['attribute_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['conditions'][$i]['operator'])."','".mysqli_real_escape_string($this->connectionlink,$data['conditions'][$i]['value'])."',NOW(),'".$_SESSION['user']['UserId']."')";
						$query1 = "insert into business_rule_condition (rule_id,attribute_id,operator,value,created_date,created_by,multiple_values) values ('".mysqli_real_escape_string($this->connectionlink,$rule_id)."','".mysqli_real_escape_string($this->connectionlink,$data['conditions'][$i]['attribute_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['conditions'][$i]['operator'])."','".mysqli_real_escape_string($this->connectionlink,$value)."',NOW(),'".$_SESSION['user']['UserId']."','".$multiple_values."')";
						$q1 = mysqli_query($this->connectionlink,$query1);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
					}
				}

				//Start inserting rule facilities
				// for($a=0;$a<count($data['FacilityID']);$a++) {
				// 	$query2 = "insert into business_rule_facilities (rule_id,FacilityID,CreatedDate,CreatedBy) values ('".$rule_id."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'][$a])."',NOW(),'".$_SESSION['user']['UserId']."')";
				// 	$q2 = mysqli_query($this->connectionlink,$query2);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);
				// 	}
				// }
				//End inserting rule facilities

				//Start insert rule_id_text
				$query33 = "select version_name from business_rule_versions where version_id = '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."'";
				$q33 = mysqli_query($this->connectionlink,$query33);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row33 = mysqli_fetch_assoc($q33);
					$rule_id_text = $row33['version_name'].'-'.$rule_id;
					$query34 = "update business_rule set rule_id_text = '".mysqli_real_escape_string($this->connectionlink,$rule_id_text)."' where rule_id = '".$rule_id."'";
					$q34 = mysqli_query($this->connectionlink,$query34);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				}
				//End insert rule_id_text

				//Start cleanup priority gaps after rule creation
				$cleanup_result = $this->cleanupPriorityGaps($data['version_id']);
				if(!$cleanup_result['success']) {
					// Log the error but don't fail the rule creation
					error_log("Priority gap cleanup failed after rule creation: " . $cleanup_result['error']);
				}
				//End cleanup priority gaps after rule creation

				$json['Success'] = true;
				$json['Result'] = 'Rule Created';
				$json['rule_id'] = $rule_id;
				return json_encode($json);
			} else { //Update existing Rule

				//Start check If rule is capable to edit
				$query32 = "select r.*,v.version_name,v.current_version,v.status as VersionStatus from business_rule r
				left join business_rule_versions v on r.version_id = v.version_id where r.rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."' ";
				$q32 = mysqli_query($this->connectionlink,$query32);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row32 = mysqli_fetch_assoc($q32);
					if($row32['VersionStatus'] != 'Pending') {
						$json['Success'] = false;
						$json['Result'] = 'Rules with Pending Version Status are only allowed to Modify';
						return json_encode($json);
					}
					$current_version_id = $row32['version_id'];
				}
				//End check If rule is capable to edit

				$rule_summary = '';
				//Start get input name
				if(isset($data['input_id']) && !empty($data['input_id'])) {
					// Check if input_id is "all"
					if($data['input_id'] === 'all' || (is_array($data['input_id']) && in_array('all', $data['input_id']))) {
						$rule_summary = 'input = "All" ,';
					} else {
						// Convert input_id to array if it's a string
						$inputIds = is_array($data['input_id']) ? $data['input_id'] : explode(',', $data['input_id']);

						// Get all input names
						$inputNames = [];
						foreach ($inputIds as $inputId) {
							$query2 = "select input from workflow_input where input_id = '".mysqli_real_escape_string($this->connectionlink, $inputId)."'";
							$q2 = mysqli_query($this->connectionlink, $query2);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row2 = mysqli_fetch_assoc($q2);
								$inputNames[] = $row2['input'];
							}
						}

						// Join input names with OR
						if (!empty($inputNames)) {
							$rule_summary = 'input = "' . implode(' OR ', $inputNames) . '" ,';
						}
					}
				}
				//End get input name

				// Check if conditions exist and is an array
				if(isset($data['conditions']) && is_array($data['conditions']) && count($data['conditions']) > 0) {
					for($i=0;$i<count($data['conditions']);$i++) {
						if($i == 0) {
							$rule_summary = $rule_summary . ' '.$data['conditions'][$i]['attribute_name'];
						} else {
							$rule_summary = $rule_summary . ', '.$data['conditions'][$i]['attribute_name'];
						}

						if($data['conditions'][$i]['operator'] == '==') {
							$rule_summary = $rule_summary .' =';
						}
						if($data['conditions'][$i]['operator'] == '!=') {
							$rule_summary = $rule_summary .' <>';
						}

						if($data['conditions'][$i]['operator'] == '<') {
							$rule_summary = $rule_summary .' <';
						}

						if($data['conditions'][$i]['operator'] == '>') {
							$rule_summary = $rule_summary .' >';
						}

						if($data['conditions'][$i]['operator'] == 'contains') {
							$rule_summary = $rule_summary .' Contains';
						}

						if($data['conditions'][$i]['operator'] == 'not_contains') {
							$rule_summary = $rule_summary .' Not Contains';
						}
						$rule_summary = $rule_summary . ' "'.$data['conditions'][$i]['value'].'" ';
					}
				} else {
					// No conditions
				}


				//Start inserting facility in rule summary
				if($data['FacilityID'] == 'all' || $data['FacilityID'] == 'All') {
					$rule_summary = $rule_summary . ' , Facility = "All" ';
				} else if($data['FacilityID'] > 0) {
					//Get facility name and add to rule summary
					$query15 = "select FacilityName from facility where FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."'";
					$q15 = mysqli_query($this->connectionlink,$query15);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row15 = mysqli_fetch_assoc($q15);
						$rule_summary = $rule_summary . ' , Facility = "'.$row15['FacilityName'].'"';
					}
				}
				//End inserting facility in rule summary

				//Start check for duplicate rule (check all business rule fields + actual conditions, not rule_summary) - excluding current rule
				$duplicate_conditions = array();

				// Build conditions for duplicate check - all business rule fields
				// Use the target version_id from the form data, not the current rule's version
				$duplicate_conditions[] = "version_id = '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."'";
				$duplicate_conditions[] = "rule_id != '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."'"; // Exclude current rule

				// Check Customer ID (AWSCustomerID)
				if(isset($data['AWSCustomerID']) && !empty($data['AWSCustomerID'])) {
					// Handle array or string for Customer ID
					if(is_array($data['AWSCustomerID'])) {
						if(in_array('All', $data['AWSCustomerID']) || in_array('all', $data['AWSCustomerID'])) {
							$duplicate_conditions[] = "AWSCustomerID = 'All'";
						} else {
							$customerStr = implode(',', $data['AWSCustomerID']);
							$duplicate_conditions[] = "AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$customerStr)."'";
						}
					} else {
						$duplicate_conditions[] = "AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['AWSCustomerID'])."'";
					}
				} else {
					$duplicate_conditions[] = "(AWSCustomerID IS NULL OR AWSCustomerID = '' OR AWSCustomerID = '0')";
				}

				// Check Workflow ID
				if($data['workflow_id'] != '' && $data['workflow_id'] != '0') {
					$duplicate_conditions[] = "workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."'";
				} else {
					$duplicate_conditions[] = "(workflow_id IS NULL OR workflow_id = '' OR workflow_id = '0')";
				}

				// Check Facility ID
				if($data['FacilityID'] != '' && $data['FacilityID'] != '0') {
					$duplicate_conditions[] = "FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."'";
				} else {
					$duplicate_conditions[] = "(FacilityID IS NULL OR FacilityID = '' OR FacilityID = '0')";
				}

				// Check Part Types
				if(isset($data['part_types']) && !empty($data['part_types']) && $data['part_types'] != '0') {
					// Convert array to comma-separated string if needed
					if(is_array($data['part_types'])) {
						if(in_array('All', $data['part_types']) || in_array('all', $data['part_types'])) {
							$partTypesStr = 'All';
						} else {
							$partTypesStr = implode(',', $data['part_types']);
						}
					} else {
						$partTypesStr = $data['part_types'];
					}
					$duplicate_conditions[] = "part_types = '".mysqli_real_escape_string($this->connectionlink,$partTypesStr)."'";
				} else {
					$duplicate_conditions[] = "(part_types IS NULL OR part_types = '' OR part_types = '0')";
				}

				// Check Source Type (idCustomertype)
				if($data['idCustomertype'] != '' && $data['idCustomertype'] != '0') {
					$duplicate_conditions[] = "idCustomertype = '".mysqli_real_escape_string($this->connectionlink,$data['idCustomertype'])."'";
				} else {
					$duplicate_conditions[] = "(idCustomertype IS NULL OR idCustomertype = '' OR idCustomertype = '0')";
				}

				// Check Material Type
				if($data['MaterialType'] != '' && $data['MaterialType'] != '0') {
					$duplicate_conditions[] = "MaterialType = '".mysqli_real_escape_string($this->connectionlink,$data['MaterialType'])."'";
				} else {
					$duplicate_conditions[] = "(MaterialType IS NULL OR MaterialType = '' OR MaterialType = '0')";
				}

				// Check Input ID
				if($data['input_id'] != '' && $data['input_id'] != '0') {
					// Convert array to comma-separated string if needed
					if(is_array($data['input_id'])) {
						$inputIdStr = implode(',', $data['input_id']);
					} else {
						$inputIdStr = $data['input_id'];
					}
					$duplicate_conditions[] = "input_id = '".mysqli_real_escape_string($this->connectionlink,$inputIdStr)."'";
				} else {
					$duplicate_conditions[] = "(input_id IS NULL OR input_id = '' OR input_id = '0')";
				}

				// Get potential duplicate rules based on business rule fields only (not rule_summary)
				$potential_duplicates_query = "SELECT rule_id, rule_name FROM business_rule WHERE " . implode(' AND ', $duplicate_conditions);

				// Debug: Log the potential duplicates query
				error_log("POTENTIAL DUPLICATES QUERY (UPDATE): " . $potential_duplicates_query);

				$potential_duplicates_result = mysqli_query($this->connectionlink, $potential_duplicates_query);

				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				// Check if any potential duplicates have the same conditions
				if(mysqli_num_rows($potential_duplicates_result) > 0) {
					while($potential_duplicate = mysqli_fetch_assoc($potential_duplicates_result)) {
						$existing_rule_id = $potential_duplicate['rule_id'];

						// Get conditions for the existing rule
						$existing_conditions_query = "SELECT attribute_id, operator, value FROM business_rule_condition WHERE rule_id = '".mysqli_real_escape_string($this->connectionlink, $existing_rule_id)."' ORDER BY attribute_id, operator, value";
						$existing_conditions_result = mysqli_query($this->connectionlink, $existing_conditions_query);

						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						// Build array of existing conditions
						$existing_conditions = array();
						while($existing_condition = mysqli_fetch_assoc($existing_conditions_result)) {
							$existing_conditions[] = $existing_condition['attribute_id'] . '|' . $existing_condition['operator'] . '|' . $existing_condition['value'];
						}

						// Build array of new conditions
						$new_conditions = array();
						if(isset($data['conditions']) && is_array($data['conditions']) && count($data['conditions']) > 0) {
							foreach($data['conditions'] as $condition) {
								$value = '';
								if(isset($condition['value_array']) && is_array($condition['value_array'])) {
									for($j=0; $j<count($condition['value_array']); $j++) {
										if($j > 0) {
											$value = $value . '@#$';
										}
										$value = $value . $condition['value_array'][$j];
									}
								} else {
									$value = $condition['value'];
								}
								$new_conditions[] = $condition['attribute_id'] . '|' . $condition['operator'] . '|' . $value;
							}
						}

						// Sort both arrays for comparison
						sort($existing_conditions);
						sort($new_conditions);

						// Compare conditions
						if($existing_conditions === $new_conditions) {
							$json['Success'] = false;
							$json['Result'] = 'A rule with the same conditions already exists: ' . $potential_duplicate['rule_name'];
							return json_encode($json);
						}
					}
				}
				//End check for duplicate rule

				//Start Sequential Priority Management for UPDATE
				$result = $this->manageSequentialPriority($data['version_id'], $data['priority'], $data['rule_id']);
				if(!$result['success']) {
					$json['Success'] = false;
					$json['Result'] = $result['error'];
					return json_encode($json);
				}
				//End Sequential Priority Management for UPDATE

				$query = "update business_rule set rule_description = '".mysqli_real_escape_string($this->connectionlink,$data['rule_description'])."',priority = '".mysqli_real_escape_string($this->connectionlink,$data['priority'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',rule_summary = '".mysqli_real_escape_string($this->connectionlink,$rule_summary)."',updated_date = NOW(),updated_by = '".$_SESSION['user']['UserId']."' ";

				// Update AWSCustomerID if it exists
				if(isset($data['AWSCustomerID']) && !empty($data['AWSCustomerID'])) {
					// Check if "All" is selected
					if(is_array($data['AWSCustomerID']) && (in_array('All', $data['AWSCustomerID']) || in_array('all', $data['AWSCustomerID']))) {
						$query = $query . ",AWSCustomerID = 'All' ";
					} else {
						// Convert array to comma-separated string if needed
						if(is_array($data['AWSCustomerID'])) {
							$customerStr = implode(',', $data['AWSCustomerID']);
						} else {
							$customerStr = $data['AWSCustomerID'];
						}
						$query = $query . ",AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink, $customerStr)."' ";
					}
				} else {
					$query = $query . ",AWSCustomerID = NULL ";
				}

				// Update workflow_id if it exists
				if($data['workflow_id'] != '') {
					$query = $query . ",workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."' ";
				} else {
					$query = $query . ",workflow_id = NULL ";
				}

				// Update part_types if it exists
				if(isset($data['part_types']) && !empty($data['part_types'])) {
					// Check if "All" is selected
					if(is_array($data['part_types']) && (in_array('All', $data['part_types']) || in_array('all', $data['part_types']))) {
						$query = $query . ",part_types = 'All' ";
					} else {
						// Convert array to comma-separated string if needed
						if(is_array($data['part_types'])) {
							$partTypesStr = implode(',', $data['part_types']);
						} else {
							$partTypesStr = $data['part_types'];
						}
						$query = $query . ",part_types = '".mysqli_real_escape_string($this->connectionlink, $partTypesStr)."' ";
					}
				} else {
					$query = $query . ",part_types = NULL ";
				}

				// Update idCustomertype if it exists
				if(isset($data['idCustomertype']) && !empty($data['idCustomertype']) && $data['idCustomertype'] != '0') {
					$query = $query . ",idCustomertype = '".mysqli_real_escape_string($this->connectionlink, $data['idCustomertype'])."' ";
				} else {
					$query = $query . ",idCustomertype = NULL ";
				}

				// Update MaterialType if it exists
				if(isset($data['MaterialType']) && !empty($data['MaterialType']) && $data['MaterialType'] != '0') {
					$query = $query . ",MaterialType = '".mysqli_real_escape_string($this->connectionlink, $data['MaterialType'])."' ";
				} else {
					$query = $query . ",MaterialType = NULL ";
				}

				// Update FacilityID if it exists
				if(isset($data['FacilityID']) && !empty($data['FacilityID']) && $data['FacilityID'] != '0') {
					$query = $query . ",FacilityID = '".mysqli_real_escape_string($this->connectionlink, $data['FacilityID'])."' ";
				} else {
					$query = $query . ",FacilityID = NULL ";
				}

				if(isset($data['input_id']) && !empty($data['input_id'])) {
					// Only set workflow_id if it wasn't already set
					if(empty($data['workflow_id'])) {
						$query = $query . ",workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."'";
					}
					// Convert array to comma-separated string if needed
					if(is_array($data['input_id'])) {
						$inputIdStr = implode(',', $data['input_id']);
					} else {
						$inputIdStr = $data['input_id'];
					}
					$query = $query . ",input_id = '".mysqli_real_escape_string($this->connectionlink,$inputIdStr)."' ";
				} else {
					$query = $query . ",workflow_id = NULL,input_id = NULL ";
				}
				if($data['business_SubDispositionExists'] && $data['sub_disposition_id'] > 0) {
					$query = $query . ",sub_disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['sub_disposition_id'])."'";
				} else {
					$query = $query . ",sub_disposition_id = NULL ";
				}

				$query = $query . " where rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."' ";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}


				$rule_id = $data['rule_id'];
				//Start delete existing rule_conditions
				$query5 = "delete from business_rule_condition where rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."'";
				$q5 = mysqli_query($this->connectionlink,$query5);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End delete existing rule_conditions

				// Only insert conditions if they exist
				if(isset($data['conditions']) && is_array($data['conditions']) && count($data['conditions']) > 0) {
					for($i=0;$i<count($data['conditions']);$i++) {

						$value = '';
						$multiple_values = 0;
	                    for($j=0;$j<count($data['conditions'][$i]['value_array']);$j++) {
	                        if($j > 0) {
	                            $value = $value . '@#$';
								$multiple_values = 1;
	                        }
	                        $value = $value . $data['conditions'][$i]['value_array'][$j];
	                    }

						//$query1 = "insert into business_rule_condition (rule_id,attribute_id,operator,value,created_date,created_by) values ('".mysqli_real_escape_string($this->connectionlink,$rule_id)."','".mysqli_real_escape_string($this->connectionlink,$data['conditions'][$i]['attribute_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['conditions'][$i]['operator'])."','".mysqli_real_escape_string($this->connectionlink,$data['conditions'][$i]['value'])."',NOW(),'".$_SESSION['user']['UserId']."')";
						$query1 = "insert into business_rule_condition (rule_id,attribute_id,operator,value,created_date,created_by,multiple_values) values ('".mysqli_real_escape_string($this->connectionlink,$rule_id)."','".mysqli_real_escape_string($this->connectionlink,$data['conditions'][$i]['attribute_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['conditions'][$i]['operator'])."','".mysqli_real_escape_string($this->connectionlink,$value)."',NOW(),'".$_SESSION['user']['UserId']."','".$multiple_values."')";
						$q1 = mysqli_query($this->connectionlink,$query1);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
					}
				}

				//Start delete existing facilities
				// $query6 = "delete from business_rule_facilities where rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."'";
				// $q6 = mysqli_query($this->connectionlink,$query6);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				//End delete existing facilities

				//Start inserting rule facilities
				// for($a=0;$a<count($data['FacilityID']);$a++) {
				// 	$query2 = "insert into business_rule_facilities (rule_id,FacilityID,CreatedDate,CreatedBy) values ('".$rule_id."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'][$a])."',NOW(),'".$_SESSION['user']['UserId']."')";
				// 	$q2 = mysqli_query($this->connectionlink,$query2);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);
				// 	}
				// }
				//End inserting rule facilities

				//Start cleanup priority gaps after rule update
				$cleanup_result = $this->cleanupPriorityGaps($data['version_id']);
				if(!$cleanup_result['success']) {
					// Log the error but don't fail the rule update
					error_log("Priority gap cleanup failed after rule update: " . $cleanup_result['error']);
				}
				//End cleanup priority gaps after rule update

				$json['Success'] = true;
				$json['Result'] = 'Rule Modified';
				return json_encode($json);
			}

			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	/*public function GetRulesList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}
			$query = "select r.*,w.workflow,d.disposition,cu.FirstName as CreatedFirst,cu.LastName as CreatedLast,uu.FirstName as UpdatedFirst,uu.LastName as UpdatedLast from business_rule r
			left join workflow w on r.workflow_id = w.workflow_id
			left join disposition d on r.disposition_id = d.disposition_id
			left join users cu on r.created_by = cu.UserId
			left join users uu on r.updated_by = uu.UserId
			order by rule_name";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Rules Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}*/

	/*public function GenerateBusinessRulesListXLS($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['rule_id']
		);
		$_SESSION['BusinessRuleListxlsid'] = $data['rule_id'];
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}
*/

	public function GenerateBusinessRulesListXLS($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$transaction = 'Administration ---> Business Rules ---> Business Rule List';
		$description = 'Business Rules List Exported';
		$this->RecordUserTransaction($transaction,$description);

		// Handle multiple version IDs
		if(isset($data['version_ids']) && !empty($data['version_ids'])) {
			$data['version_ids_array'] = explode(',', $data['version_ids']);
		} else if(isset($data['version_id']) && !empty($data['version_id'])) {
			// Backward compatibility - single version
			$data['version_ids_array'] = array($data['version_id']);
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No version selected for export';
			return json_encode($json);
		}

		$_SESSION['BusinessRuleListxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $data;
		return json_encode($json);
	}

	public function GetRulesList($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['rule_id']
			);
			$query = "select r.*,f.FacilityName,d.disposition,sd.disposition as sub_disposition,v.version_name,v.current_version,v.status as VersionStatus,w.workflow,ct.Cumstomertype as SourceTypeName,cu.FirstName as CreatedByFirstName,cu.LastName as CreatedByLastName,uu.FirstName as UpdatedByFirstName,uu.LastName as UpdatedByLastName from business_rule r
			left join facility f on r.FacilityID = f.FacilityID
			left join disposition d on d.disposition_id = r.disposition_id
			left join disposition sd on r.sub_disposition_id = sd.disposition_id
			left join business_rule_versions v on r.version_id = v.version_id
			left join workflow w on r.workflow_id = w.workflow_id
			left join customertype ct on r.idCustomertype = ct.idCustomertype
			left join users cu on r.created_by = cu.UserId
			left join users uu on r.updated_by = uu.UserId
			WHERE 1";

			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'rule_id_text') {
							$query = $query . " AND r.rule_id_text like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'rule_name') {
							$query = $query . " AND r.rule_name like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'rule_description') {
							$query = $query . " AND r.rule_description like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'FacilityName') {
							if(strtolower($value) == 'all') {
								$query = $query . " AND (f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.FacilityID = 'all' OR r.FacilityID = 'All') ";
							} else {
								$query = $query . " AND (f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.FacilityID = 'all' OR r.FacilityID = 'All') ";
							}
						}
						if($key == 'priority') {
							$query = $query . " AND r.priority like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'disposition') {
							$query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'rule_summary') {
							$query = $query . " AND r.rule_summary like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'sub_disposition') {
							$query = $query . " AND sd.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'status') {
							$query = $query . " AND r.status like '".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'workflow') {
							if(strtolower($value) == 'all') {
								$query = $query . " AND (w.workflow like '".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.workflow_id = 'all' OR r.workflow_id = 'All') ";
							} else {
								$query = $query . " AND (w.workflow like '".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.workflow_id = 'all' OR r.workflow_id = 'All') ";
							}
						}
						if($key == 'CustomerName') {
							if(strtolower($value) == 'all') {
								$query = $query . " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All') ";
							} else {
								// Handle Customer filtering for both single and comma-separated Customer IDs
								// First, get Customer IDs that match the customer name
								$customerIdQuery = "SELECT GROUP_CONCAT(AWSCustomerID) as customer_ids FROM aws_customers WHERE Customer LIKE '%".mysqli_real_escape_string($this->connectionlink,$value)."%'";
								$customerIdResult = mysqli_query($this->connectionlink, $customerIdQuery);
								$customerIds = '';
								if($customerIdResult && mysqli_num_rows($customerIdResult) > 0) {
									$customerIdRow = mysqli_fetch_assoc($customerIdResult);
									$customerIds = $customerIdRow['customer_ids'];
								}

								if(!empty($customerIds)) {
									$customerIdArray = explode(',', $customerIds);
									$customerConditions = array();
									$customerConditions[] = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";

									foreach($customerIdArray as $customerId) {
										$customerId = trim($customerId);
										if(!empty($customerId)) {
											$customerConditions[] = "r.AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink, $customerId)."'";
											$customerConditions[] = "FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $customerId)."', r.AWSCustomerID) > 0";
										}
									}
									$query = $query . " AND (" . implode(' OR ', $customerConditions) . ") ";
								} else {
									// No matching customer found, only show global rules
									$query = $query . " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All') ";
								}
							}
						}
						if($key == 'part_types') {
							if(strtolower($value) == 'all') {
								$query = $query . " AND (r.part_types = 'all' OR r.part_types = 'All') ";
							} else {
								$query = $query . " AND (r.part_types like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.part_types = 'all' OR r.part_types = 'All') ";
							}
						}
						if($key == 'SourceTypeName') {
							if(strtolower($value) == 'all') {
								$query = $query . " AND (ct.Cumstomertype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.idCustomertype = 'all' OR r.idCustomertype = 'All') ";
							} else {
								$query = $query . " AND (ct.Cumstomertype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.idCustomertype = 'all' OR r.idCustomertype = 'All') ";
							}
						}
						if($key == 'MaterialType') {
							if(strtolower($value) == 'all') {
								$query = $query . " AND (r.MaterialType = 'all' OR r.MaterialType = 'All') ";
							} else {
								$query = $query . " AND (r.MaterialType like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.MaterialType = 'all' OR r.MaterialType = 'All') ";
							}
						}
						if($key == 'created_date') {
							$query = $query . " AND DATE(r.created_date) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'created_by') {
							$query = $query . " AND (cu.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR cu.LastName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR CONCAT(cu.FirstName, ' ', cu.LastName) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%') ";
						}
						if($key == 'updated_date') {
							$query = $query . " AND DATE(r.updated_date) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'updated_by') {
							$query = $query . " AND (uu.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR uu.LastName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR CONCAT(uu.FirstName, ' ', uu.LastName) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%') ";
						}
					}
				}
			}

			if($data['version_id'] > 0) {
				$query = $query . " AND r.version_id = '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."' ";
			}

			// Exclude archived rules from the list
			$query = $query . " AND r.status != 'Archived' ";

			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if($data['OrderBy'] == 'rule_id_text') {
					$query = $query . " order by r.rule_id_text ".$order_by_type." ";
				} else if($data['OrderBy'] == 'rule_name') {
					$query = $query . " order by r.rule_name ".$order_by_type." ";
				} else if($data['OrderBy'] == 'rule_description') {
					$query = $query . " order by r.rule_description ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by f.FacilityName ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'priority') {
					$query = $query . " order by r.priority ".$order_by_type." ";
				} else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition ".$order_by_type." ";
				} else if($data['OrderBy'] == 'rule_summary') {
					$query = $query . " order by r.rule_summary ".$order_by_type." ";
				} else if($data['OrderBy'] == 'sub_disposition') {
					$query = $query . " order by sd.disposition ".$order_by_type." ";
				} else if($data['OrderBy'] == 'status') {
					$query = $query . " order by r.status ".$order_by_type." ";
				} else if($data['OrderBy'] == 'workflow') {
					$query = $query . " order by w.workflow ".$order_by_type." ";
				} else if($data['OrderBy'] == 'CustomerName') {
					$query = $query . " order by r.AWSCustomerID ".$order_by_type." ";
				} else if($data['OrderBy'] == 'part_types') {
					$query = $query . " order by r.part_types ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SourceTypeName') {
					$query = $query . " order by ct.Cumstomertype ".$order_by_type." ";
				} else if($data['OrderBy'] == 'MaterialType') {
					$query = $query . " order by r.MaterialType ".$order_by_type." ";
				} else if($data['OrderBy'] == 'created_date') {
					$query = $query . " order by r.created_date ".$order_by_type." ";
				} else if($data['OrderBy'] == 'created_by') {
					$query = $query . " order by CONCAT(cu.FirstName, ' ', cu.LastName) ".$order_by_type." ";
				} else if($data['OrderBy'] == 'updated_date') {
					$query = $query . " order by r.updated_date ".$order_by_type." ";
				} else if($data['OrderBy'] == 'updated_by') {
					$query = $query . " order by CONCAT(uu.FirstName, ' ', uu.LastName) ".$order_by_type." ";
				}
			} else {
				$query = $query . " order by r.priority ";
			}

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));

		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Rules Available";
		}

		if($data['skip'] == 0) {
				$query1 = "select count(*) from business_rule r
				left join facility f on r.FacilityID = f.FacilityID
				left join disposition d on d.disposition_id = r.disposition_id
				left join disposition sd on r.sub_disposition_id = sd.disposition_id
				left join business_rule_versions v on r.version_id = v.version_id
				left join workflow w on r.workflow_id = w.workflow_id
				left join customertype ct on r.idCustomertype = ct.idCustomertype
				left join users cu on r.created_by = cu.UserId
				left join users uu on r.updated_by = uu.UserId
				WHERE 1 ";
				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {

							if($key == 'rule_id_text') {
								$query1 = $query1 . " AND r.rule_id_text like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'rule_name') {
								$query1 = $query1 . " AND r.rule_name like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'rule_description') {
								$query1 = $query1 . " AND r.rule_description like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'FacilityName') {
								if(strtolower($value) == 'all') {
									$query1 = $query1 . " AND (f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.FacilityID = 'all' OR r.FacilityID = 'All') ";
								} else {
									$query1 = $query1 . " AND (f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.FacilityID = 'all' OR r.FacilityID = 'All') ";
								}
							}
							if($key == 'priority') {
								$query1 = $query1 . " AND r.priority like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'disposition') {
								$query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'rule_summary') {
								$query1 = $query1 . " AND r.rule_summary like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'sub_disposition') {
								$query1 = $query1 . " AND sd.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'status') {
								$query1 = $query1 . " AND r.status like '".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'workflow') {
								if(strtolower($value) == 'all') {
									$query1 = $query1 . " AND (w.workflow like '".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.workflow_id = 'all' OR r.workflow_id = 'All') ";
								} else {
									$query1 = $query1 . " AND (w.workflow like '".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.workflow_id = 'all' OR r.workflow_id = 'All') ";
								}
							}
							if($key == 'CustomerName') {
								if(strtolower($value) == 'all') {
									$query1 = $query1 . " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All') ";
								} else {
									// Handle Customer filtering for both single and comma-separated Customer IDs
									// First, get Customer IDs that match the customer name
									$customerIdQuery = "SELECT GROUP_CONCAT(AWSCustomerID) as customer_ids FROM aws_customers WHERE Customer LIKE '%".mysqli_real_escape_string($this->connectionlink,$value)."%'";
									$customerIdResult = mysqli_query($this->connectionlink, $customerIdQuery);
									$customerIds = '';
									if($customerIdResult && mysqli_num_rows($customerIdResult) > 0) {
										$customerIdRow = mysqli_fetch_assoc($customerIdResult);
										$customerIds = $customerIdRow['customer_ids'];
									}

									if(!empty($customerIds)) {
										$customerIdArray = explode(',', $customerIds);
										$customerConditions = array();
										$customerConditions[] = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";

										foreach($customerIdArray as $customerId) {
											$customerId = trim($customerId);
											if(!empty($customerId)) {
												$customerConditions[] = "r.AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink, $customerId)."'";
												$customerConditions[] = "FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $customerId)."', r.AWSCustomerID) > 0";
											}
										}
										$query1 = $query1 . " AND (" . implode(' OR ', $customerConditions) . ") ";
									} else {
										// No matching customer found, only show global rules
										$query1 = $query1 . " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All') ";
									}
								}
							}
							if($key == 'part_types') {
								if(strtolower($value) == 'all') {
									$query1 = $query1 . " AND (r.part_types = 'all' OR r.part_types = 'All') ";
								} else {
									$query1 = $query1 . " AND (r.part_types like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.part_types = 'all' OR r.part_types = 'All') ";
								}
							}
							if($key == 'SourceTypeName') {
								if(strtolower($value) == 'all') {
									$query1 = $query1 . " AND (ct.Cumstomertype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.idCustomertype = 'all' OR r.idCustomertype = 'All') ";
								} else {
									$query1 = $query1 . " AND (ct.Cumstomertype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.idCustomertype = 'all' OR r.idCustomertype = 'All') ";
								}
							}
							if($key == 'MaterialType') {
								if(strtolower($value) == 'all') {
									$query1 = $query1 . " AND (r.MaterialType = 'all' OR r.MaterialType = 'All') ";
								} else {
									$query1 = $query1 . " AND (r.MaterialType like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR r.MaterialType = 'all' OR r.MaterialType = 'All') ";
								}
							}
							if($key == 'created_date') {
								$query1 = $query1 . " AND DATE(r.created_date) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'created_by') {
								$query1 = $query1 . " AND (cu.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR cu.LastName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR CONCAT(cu.FirstName, ' ', cu.LastName) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%') ";
							}
							if($key == 'updated_date') {
								$query1 = $query1 . " AND DATE(r.updated_date) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'updated_by') {
								$query1 = $query1 . " AND (uu.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR uu.LastName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR CONCAT(uu.FirstName, ' ', uu.LastName) like '%".mysqli_real_escape_string($this->connectionlink,$value)."%') ";
							}
						}
					}
				}

				if($data['version_id'] > 0) {
					$query1 = $query1 . " AND r.version_id = '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."' ";
				}

				// Exclude archived rules from the count
				$query1 = $query1 . " AND r.status != 'Archived' ";

				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		}
		 catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


	public function GetSubDispositions ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}
			//$query = "select * from disposition where status = 'Active' and parent_disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' order by disposition";
			$query = "select d.* from sub_disposition_mapping m,disposition d where m.parent_disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' and m.sub_disposition_id = d.disposition_id and d.status = 'Active'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Sub Dispositions Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ChangeRuleStatus ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule List Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Business Rule List Page';
				return json_encode($json);
			}

			// Get the version_id before updating status for priority cleanup
			$version_query = "SELECT version_id FROM business_rule WHERE rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."'";
			$version_result = mysqli_query($this->connectionlink, $version_query);
			$version_id = null;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$version_row = mysqli_fetch_assoc($version_result);
				$version_id = $version_row['version_id'];
			}

			$query = "update business_rule set status = '".mysqli_real_escape_string($this->connectionlink,$data['status'])."' where rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Clean up priority gaps if rule was deactivated and we have version_id
			if($data['status'] == 'InActive' && $version_id) {
				$cleanup_result = $this->cleanupPriorityGaps($version_id);
				if(!$cleanup_result['success']) {
					// Log the error but don't fail the status change
					error_log("Priority cleanup failed after rule deactivation: " . $cleanup_result['error']);
				}
			}

			$json['Success'] = true;
			$json['Result'] = 'Business Rule Status Changed';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	/**
	 * Archive a business rule (only for Pending versions)
	 */
	public function ArchiveRule($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'Archive failed'
		);

		try {
			if(!$this->isPermitted($_SESSION['user']['ProfileID'], 'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule List Page';
				return json_encode($json);
			}

			if(!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Business Rule List Page';
				return json_encode($json);
			}

			// Get rule details and version status
			$rule_query = "SELECT r.rule_id, r.rule_name, r.version_id, r.priority, v.status as version_status
						   FROM business_rule r
						   LEFT JOIN business_rule_versions v ON r.version_id = v.version_id
						   WHERE r.rule_id = '".mysqli_real_escape_string($this->connectionlink, $data['rule_id'])."'";

			$rule_result = mysqli_query($this->connectionlink, $rule_query);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if(mysqli_num_rows($rule_result) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'Rule not found';
				return json_encode($json);
			}

			$rule_data = mysqli_fetch_assoc($rule_result);

			// Check if version is Pending
			if($rule_data['version_status'] !== 'Pending') {
				$json['Success'] = false;
				$json['Result'] = 'Rules can only be archived from Pending versions. Current version status: ' . $rule_data['version_status'];
				return json_encode($json);
			}

			// Archive the rule
			$archive_query = "UPDATE business_rule
							  SET status = 'Archived',
								  updated_by = '".mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['UserId'])."',
								  updated_date = NOW()
							  WHERE rule_id = '".mysqli_real_escape_string($this->connectionlink, $data['rule_id'])."'";

			$archive_result = mysqli_query($this->connectionlink, $archive_query);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Clean up priority gaps after archiving
			$cleanup_result = $this->cleanupPriorityGaps($rule_data['version_id']);
			if(!$cleanup_result['success']) {
				// Log the error but don't fail the archive operation
				error_log("Priority cleanup failed after rule archiving: " . $cleanup_result['error']);
			}

			$json['Success'] = true;
			$json['Result'] = 'Rule "' . $rule_data['rule_name'] . '" has been archived successfully';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	/**
	 * Get list of archived business rules
	 */
	public function GetArchivedRulesList($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			if(!$this->isPermitted($_SESSION['user']['ProfileID'], 'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule List Page';
				return json_encode($json);
			}

			// Base query for archived rules with all necessary joins
			$query = "SELECT r.*, f.FacilityName, d.disposition, v.version_name, v.current_version, v.status as VersionStatus,
					  w.workflow, ct.Cumstomertype as SourceTypeName, mt.MaterialType as MaterialTypeName,
					  cu.FirstName as CreatedByFirstName, cu.LastName as CreatedByLastName,
					  uu.FirstName as UpdatedByFirstName, uu.LastName as UpdatedByLastName
					  FROM business_rule r
					  LEFT JOIN facility f ON r.FacilityID = f.FacilityID
					  LEFT JOIN disposition d ON d.disposition_id = r.disposition_id
					  LEFT JOIN business_rule_versions v ON r.version_id = v.version_id
					  LEFT JOIN workflow w ON r.workflow_id = w.workflow_id
					  LEFT JOIN customertype ct ON r.idCustomertype = ct.idCustomertype
					  LEFT JOIN material_types mt ON r.MaterialType = mt.MaterialTypeID
					  LEFT JOIN users cu ON r.created_by = cu.UserId
					  LEFT JOIN users uu ON r.updated_by = uu.UserId
					  WHERE r.status = 'Archived'";

			// Apply filters if provided
			if(isset($data[0]) && is_array($data[0]) && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						switch($key) {
							case 'rule_name':
								$query .= " AND r.rule_name LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'rule_description':
								$query .= " AND r.rule_description LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'priority':
								$query .= " AND r.priority LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'FacilityName':
								if(strtolower($value) == 'all') {
									$query .= " AND (f.FacilityName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.FacilityID = 'all' OR r.FacilityID = 'All')";
								} else {
									$query .= " AND (f.FacilityName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.FacilityID = 'all' OR r.FacilityID = 'All')";
								}
								break;
							case 'disposition':
								$query .= " AND d.disposition LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'rule_summary':
								$query .= " AND r.rule_summary LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'version_name':
								$query .= " AND v.version_name LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'workflow':
								if(strtolower($value) == 'all') {
									$query .= " AND (w.workflow LIKE '".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.workflow_id = 'all' OR r.workflow_id = 'All')";
								} else {
									$query .= " AND (w.workflow LIKE '".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.workflow_id = 'all' OR r.workflow_id = 'All')";
								}
								break;
							case 'part_types':
								if(strtolower($value) == 'all') {
									$query .= " AND (r.part_types = 'all' OR r.part_types = 'All')";
								} else {
									$query .= " AND (r.part_types LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.part_types = 'all' OR r.part_types = 'All')";
								}
								break;
							case 'SourceTypeName':
								if(strtolower($value) == 'all') {
									$query .= " AND (ct.Cumstomertype LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.idCustomertype = 'all' OR r.idCustomertype = 'All')";
								} else {
									$query .= " AND (ct.Cumstomertype LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.idCustomertype = 'all' OR r.idCustomertype = 'All')";
								}
								break;
							case 'MaterialTypeName':
								if(strtolower($value) == 'all') {
									$query .= " AND (r.MaterialType = 'all' OR r.MaterialType = 'All')";
								} else {
									$query .= " AND (r.MaterialType LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.MaterialType = 'all' OR r.MaterialType = 'All')";
								}
								break;
							case 'CustomerName':
								if(strtolower($value) == 'all') {
									$query .= " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";
								} else {
									// Handle Customer filtering for both single and comma-separated Customer IDs
									// First, get Customer IDs that match the customer name
									$customerIdQuery = "SELECT GROUP_CONCAT(AWSCustomerID) as customer_ids FROM aws_customers WHERE Customer LIKE '%".mysqli_real_escape_string($this->connectionlink,$value)."%'";
									$customerIdResult = mysqli_query($this->connectionlink, $customerIdQuery);
									$customerIds = '';
									if($customerIdResult && mysqli_num_rows($customerIdResult) > 0) {
										$customerIdRow = mysqli_fetch_assoc($customerIdResult);
										$customerIds = $customerIdRow['customer_ids'];
									}

									if(!empty($customerIds)) {
										$customerIdArray = explode(',', $customerIds);
										$customerConditions = array();
										$customerConditions[] = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";

										foreach($customerIdArray as $customerId) {
											$customerId = trim($customerId);
											if(!empty($customerId)) {
												$customerConditions[] = "r.AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink, $customerId)."'";
												$customerConditions[] = "FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $customerId)."', r.AWSCustomerID) > 0";
											}
										}
										$query .= " AND (" . implode(' OR ', $customerConditions) . ")";
									} else {
										// No matching customer found, only show global rules
										$query .= " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";
									}
								}
								break;
							case 'rule_id_text':
								$query .= " AND r.rule_id LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'created_date':
								$query .= " AND DATE(r.created_date) LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'created_by':
								$query .= " AND (CONCAT(cu.FirstName, ' ', cu.LastName) LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR cu.FirstName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR cu.LastName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%')";
								break;
							case 'updated_date':
								$query .= " AND DATE(r.updated_date) LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'updated_by':
								$query .= " AND (CONCAT(uu.FirstName, ' ', uu.LastName) LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR uu.FirstName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR uu.LastName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%')";
								break;
						}
					}
				}
			}

			// Count query for pagination
			$query1 = "SELECT COUNT(*) as total FROM business_rule r
					   LEFT JOIN facility f ON r.FacilityID = f.FacilityID
					   LEFT JOIN disposition d ON d.disposition_id = r.disposition_id
					   LEFT JOIN business_rule_versions v ON r.version_id = v.version_id
					   LEFT JOIN workflow w ON r.workflow_id = w.workflow_id
					   LEFT JOIN customertype ct ON r.idCustomertype = ct.idCustomertype
					   LEFT JOIN material_types mt ON r.MaterialType = mt.MaterialTypeID
					   LEFT JOIN users cu ON r.created_by = cu.UserId
					   LEFT JOIN users uu ON r.updated_by = uu.UserId
					   WHERE r.status = 'Archived'";

			// Apply same filters to count query
			if(isset($data[0]) && is_array($data[0]) && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						switch($key) {
							case 'rule_name':
								$query1 .= " AND r.rule_name LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'rule_description':
								$query1 .= " AND r.rule_description LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'priority':
								$query1 .= " AND r.priority LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'FacilityName':
								if(strtolower($value) == 'all') {
									$query1 .= " AND (f.FacilityName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.FacilityID = 'all' OR r.FacilityID = 'All')";
								} else {
									$query1 .= " AND (f.FacilityName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.FacilityID = 'all' OR r.FacilityID = 'All')";
								}
								break;
							case 'disposition':
								$query1 .= " AND d.disposition LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'rule_summary':
								$query1 .= " AND r.rule_summary LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'version_name':
								$query1 .= " AND v.version_name LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'workflow':
								if(strtolower($value) == 'all') {
									$query1 .= " AND (w.workflow LIKE '".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.workflow_id = 'all' OR r.workflow_id = 'All')";
								} else {
									$query1 .= " AND (w.workflow LIKE '".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.workflow_id = 'all' OR r.workflow_id = 'All')";
								}
								break;
							case 'part_types':
								if(strtolower($value) == 'all') {
									$query1 .= " AND (r.part_types = 'all' OR r.part_types = 'All')";
								} else {
									$query1 .= " AND (r.part_types LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.part_types = 'all' OR r.part_types = 'All')";
								}
								break;
							case 'SourceTypeName':
								if(strtolower($value) == 'all') {
									$query1 .= " AND (ct.Cumstomertype LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.idCustomertype = 'all' OR r.idCustomertype = 'All')";
								} else {
									$query1 .= " AND (ct.Cumstomertype LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.idCustomertype = 'all' OR r.idCustomertype = 'All')";
								}
								break;
							case 'MaterialTypeName':
								if(strtolower($value) == 'all') {
									$query1 .= " AND (r.MaterialType = 'all' OR r.MaterialType = 'All')";
								} else {
									$query1 .= " AND (r.MaterialType LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR r.MaterialType = 'all' OR r.MaterialType = 'All')";
								}
								break;
							case 'CustomerName':
								if(strtolower($value) == 'all') {
									$query1 .= " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";
								} else {
									// Handle Customer filtering for both single and comma-separated Customer IDs
									// First, get Customer IDs that match the customer name
									$customerIdQuery = "SELECT GROUP_CONCAT(AWSCustomerID) as customer_ids FROM aws_customers WHERE Customer LIKE '%".mysqli_real_escape_string($this->connectionlink,$value)."%'";
									$customerIdResult = mysqli_query($this->connectionlink, $customerIdQuery);
									$customerIds = '';
									if($customerIdResult && mysqli_num_rows($customerIdResult) > 0) {
										$customerIdRow = mysqli_fetch_assoc($customerIdResult);
										$customerIds = $customerIdRow['customer_ids'];
									}

									if(!empty($customerIds)) {
										$customerIdArray = explode(',', $customerIds);
										$customerConditions = array();
										$customerConditions[] = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";

										foreach($customerIdArray as $customerId) {
											$customerId = trim($customerId);
											if(!empty($customerId)) {
												$customerConditions[] = "r.AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink, $customerId)."'";
												$customerConditions[] = "FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $customerId)."', r.AWSCustomerID) > 0";
											}
										}
										$query1 .= " AND (" . implode(' OR ', $customerConditions) . ")";
									} else {
										// No matching customer found, only show global rules
										$query1 .= " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";
									}
								}
								break;
							case 'rule_id_text':
								$query1 .= " AND r.rule_id LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'created_date':
								$query1 .= " AND DATE(r.created_date) LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'created_by':
								$query1 .= " AND (CONCAT(cu.FirstName, ' ', cu.LastName) LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR cu.FirstName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR cu.LastName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%')";
								break;
							case 'updated_date':
								$query1 .= " AND DATE(r.updated_date) LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%'";
								break;
							case 'updated_by':
								$query1 .= " AND (CONCAT(uu.FirstName, ' ', uu.LastName) LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR uu.FirstName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%' OR uu.LastName LIKE '%".mysqli_real_escape_string($this->connectionlink, $value)."%')";
								break;
						}
					}
				}
			}

			// Execute count query
			$q1 = mysqli_query($this->connectionlink, $query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$total_count = 0;
			if(mysqli_num_rows($q1) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$total_count = $row1['total'];
			}

			// Apply sorting
			if(isset($data['OrderBy']) && $data['OrderBy'] != '') {
				$order_by_type = (isset($data['OrderByType']) && $data['OrderByType'] == 'asc') ? 'ASC' : 'DESC';

				switch($data['OrderBy']) {
					case 'rule_name':
						$query .= " ORDER BY r.rule_name " . $order_by_type;
						break;
					case 'rule_description':
						$query .= " ORDER BY r.rule_description " . $order_by_type;
						break;
					case 'priority':
						$query .= " ORDER BY r.priority " . $order_by_type;
						break;
					case 'FacilityName':
						$query .= " ORDER BY f.FacilityName " . $order_by_type;
						break;
					case 'disposition':
						$query .= " ORDER BY d.disposition " . $order_by_type;
						break;
					case 'rule_summary':
						$query .= " ORDER BY r.rule_summary " . $order_by_type;
						break;
					case 'version_name':
						$query .= " ORDER BY v.version_name " . $order_by_type;
						break;
					case 'workflow':
						$query .= " ORDER BY w.workflow " . $order_by_type;
						break;
					case 'part_types':
						$query .= " ORDER BY r.part_types " . $order_by_type;
						break;
					case 'SourceTypeName':
						$query .= " ORDER BY ct.Cumstomertype " . $order_by_type;
						break;
					case 'MaterialTypeName':
						$query .= " ORDER BY mt.MaterialType " . $order_by_type;
						break;
					case 'CustomerName':
						$query .= " ORDER BY r.AWSCustomerID " . $order_by_type;
						break;
					case 'rule_id':
						$query .= " ORDER BY r.rule_id " . $order_by_type;
						break;
					case 'created_date':
						$query .= " ORDER BY r.created_date " . $order_by_type;
						break;
					case 'created_by':
						$query .= " ORDER BY CONCAT(cu.FirstName, ' ', cu.LastName) " . $order_by_type;
						break;
					case 'updated_date':
						$query .= " ORDER BY r.updated_date " . $order_by_type;
						break;
					case 'updated_by':
						$query .= " ORDER BY CONCAT(uu.FirstName, ' ', uu.LastName) " . $order_by_type;
						break;
					default:
						$query .= " ORDER BY r.created_date DESC";
						break;
				}
			} else {
				$query .= " ORDER BY r.created_date DESC";
			}

			// Apply pagination
			if(isset($data['limit']) && isset($data['skip'])) {
				$limit = (int)$data['limit'];
				$skip = (int)$data['skip'];
				$query .= " LIMIT $skip, $limit";
			}

			// Execute main query
			$q = mysqli_query($this->connectionlink, $query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$rules = array();
			if(mysqli_num_rows($q) > 0) {
				while($row = mysqli_fetch_assoc($q)) {
					$rules[] = $row;
				}
			}

			$json['Success'] = true;
			$json['Result'] = $rules;
			$json['TotalCount'] = $total_count;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	/**
	 * Generate Excel export for archived business rules
	 */
	public function GenerateArchivedRulesListXLS($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		$json = array(
			'Success' => false,
			'Result' => $data
		);

		$transaction = 'Administration ---> Business Rules ---> Archived Rules List';
		$description = 'Archived Rules List Exported';
		$this->RecordUserTransaction($transaction,$description);

		$_SESSION['ArchivedRuleListxls'] = $data;
		$json['Success'] = true;
		return json_encode($json);
	}


	public function GetBusinessRuleDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}
			$query = "select r.*, d.disposition, sd.disposition as sub_disposition, cu.FirstName as CreatedByFirstName, cu.LastName as CreatedByLastName, uu.FirstName as UpdatedByFirstName, uu.LastName as UpdatedByLastName
			from business_rule r
			left join disposition d on r.disposition_id = d.disposition_id
			left join disposition sd on r.sub_disposition_id = sd.disposition_id
			left join users cu on r.created_by = cu.UserId
			left join users uu on r.updated_by = uu.UserId
			where r.rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				// Convert comma-separated input_id to array for multi-select dropdown
				if(isset($row['input_id']) && !empty($row['input_id'])) {
					if($row['input_id'] === 'all') {
						$row['input_id'] = ['all'];
					} else {
						// Split comma-separated values into array
						$row['input_id'] = explode(',', $row['input_id']);
						// Remove any empty values and trim whitespace
						$row['input_id'] = array_filter(array_map('trim', $row['input_id']));
						// Re-index array to ensure proper JSON encoding
						$row['input_id'] = array_values($row['input_id']);
					}
				} else {
					$row['input_id'] = array();
				}

				// Convert comma-separated part_types to array for multi-select dropdown
				if(isset($row['part_types']) && !empty($row['part_types'])) {
					if($row['part_types'] === 'all' || $row['part_types'] === 'All') {
						$row['part_types'] = ['All'];
					} else {
						// Split comma-separated values into array
						$row['part_types'] = explode(',', $row['part_types']);
						// Remove any empty values and trim whitespace
						$row['part_types'] = array_filter(array_map('trim', $row['part_types']));
						// Re-index array to ensure proper JSON encoding
						$row['part_types'] = array_values($row['part_types']);
					}
				} else {
					$row['part_types'] = array();
				}

				// Convert comma-separated AWSCustomerID to array for multi-select dropdown
				if(isset($row['AWSCustomerID']) && !empty($row['AWSCustomerID'])) {
					if($row['AWSCustomerID'] === 'all' || $row['AWSCustomerID'] === 'All') {
						$row['AWSCustomerID'] = ['All'];
					} else {
						// Split comma-separated values into array
						$row['AWSCustomerID'] = explode(',', $row['AWSCustomerID']);
						// Remove any empty values and trim whitespace
						$row['AWSCustomerID'] = array_filter(array_map('trim', $row['AWSCustomerID']));
						// Re-index array to ensure proper JSON encoding
						$row['AWSCustomerID'] = array_values($row['AWSCustomerID']);
					}
				} else {
					$row['AWSCustomerID'] = array();
				}

				//$row['input_type'] = array();
				//Start get rule facilities
				// $query2 = "select FacilityID from business_rule_facilities where rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."'";
				// $q2 = mysqli_query($this->connectionlink,$query2);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$j = 0;
				// 	$facilities = array();
				// 	while($row2 = mysqli_fetch_assoc($q2)) {
				// 		array_push($facilities,$row2['FacilityID']);
				// 	}
				// 	$row['FacilityID'] = $facilities;
				// } else {
				// 	$row['FacilityID'] = array();
				// }
				//End get rule facilities

				//Start get Business Rule Conditions
				$query1 = "select bc.*,ba.attribute_name from business_rule_condition bc,business_rule_attributes ba where bc.rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."' and bc.attribute_id = ba.attribute_id";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i = 0;
					$conditions = array();
					while($row1 = mysqli_fetch_assoc($q1)) {
						/*if($row1['multiple_values'] == '1') {
							$row1['value_array'] = explode("@#$", $row1['value']);
							$row1['value'] = str_replace("@#$"," OR ",$row1['value']);
							if($row1['attribute_id'] == '14') { //If Input-Type
								$row['input_type'] = $row1['value_array'];
							}
						} else if($row1['attribute_id'] == '14') {
							$row['input_type'] = array();
							array_push($row['input_type'],$row1['value']);
						}*/

						/*if($row1['multiple_values'] == '1') {
							$row1['value_array'] = explode("@#$", $row1['value']);
							$row1['value'] = str_replace("@#$"," OR ",$row1['value']);
							if($row1['attribute_id'] == '14') { //If Input-Type
								$row['input_type'] = $row1['value_array'];
							}
						} else {
							$row1['value_array'] = explode("@#$", $row1['value']);
							if($row1['attribute_id'] == '14') { //If Input-Type
								$row['input_type'] = $row1['value_array'];
							}
						}*/


						$row1['value_array'] = explode("@#$", $row1['value']);
						$row1['value'] = str_replace("@#$"," OR ",$row1['value']);
						if($row1['attribute_id'] == '14') { //If Input-Type
							$row['input_type'] = $row1['value_array'];
						}

						$conditions[$i] = $row1;
						$i++;
					}
					$row['conditions'] = $conditions;
				} else {
					$row['conditions'] = array();
				}
				//End get Business Rule Conditions

				$json['Success'] = true;
				$json['Result'] = $row;
				return json_encode($json);

			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Business Rule";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetRuleVersions ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule List Page';
				return json_encode($json);
			}
			$query = "select * from business_rule_versions order by version_name";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Rule Versions Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function CreateNewBRVersion ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule List Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Business Rule List Page';
				return json_encode($json);
			}

			//Start check IF Rule Name exists
			$duplicate = $this->CheckDuplicate('New','business_rule_versions','version_name',$data['version_name'],false,'','');
			if($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'Version Name already exists';
				return json_encode($json);
			}
			//End check IF Rule Name exists

			$query = "insert into business_rule_versions (version_name,created_date,created_by,status,version_description) values ('".mysqli_real_escape_string($this->connectionlink,$data['version_name'])."',NOW(),'".$_SESSION['user']['UserId']."','Pending','".mysqli_real_escape_string($this->connectionlink,$data['version_description'])."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$version_id = mysqli_insert_id($this->connectionlink);
			$json['version_id'] = $version_id;
			$query = "select br.* from business_rule br,business_rule_versions bv where br.version_id = bv.version_id and bv.current_version = '1' and br.status != 'Archived'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)) {
					//Start Create Rule for New Version
					$old_rule_id = $row['rule_id'];
					$query1 = "insert into business_rule (rule_name,rule_description,priority";
					if($row['input_id'] != '') {
						$query1 = $query1 . ",workflow_id,input_id";
					}

					$query1 = $query1 . ",disposition_id,status,rule_summary,created_date,created_by,FacilityID,AWSCustomerID,part_types,idCustomertype,MaterialType";
					if($row['sub_disposition_id'] > 0) {
						$query1 = $query1 . ",sub_disposition_id";
					}

					$query1 = $query1 . ",version_id) values ('".mysqli_real_escape_string($this->connectionlink,$row['rule_name'])."','".mysqli_real_escape_string($this->connectionlink,$row['rule_description'])."','".mysqli_real_escape_string($this->connectionlink,$row['priority'])."'";
					if($row['input_id'] != '') {
						$query1 = $query1 . ",'".mysqli_real_escape_string($this->connectionlink,$row['workflow_id'])."','".mysqli_real_escape_string($this->connectionlink,$row['input_id'])."'";
					}

					$query1 = $query1 . ",'".mysqli_real_escape_string($this->connectionlink,$row['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$row['status'])."','".mysqli_real_escape_string($this->connectionlink,$row['rule_summary'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['FacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$row['AWSCustomerID'])."','".mysqli_real_escape_string($this->connectionlink,$row['part_types'])."','".mysqli_real_escape_string($this->connectionlink,$row['idCustomertype'])."','".mysqli_real_escape_string($this->connectionlink,$row['MaterialType'])."'";
					if($row['sub_disposition_id'] > 0) {
						$query1 = $query1 . ",'".mysqli_real_escape_string($this->connectionlink,$row['sub_disposition_id'])."'";
					}

					$query1 = $query1 . ",'".$version_id."') ";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink).$query1;
						return json_encode($json);
					}
					$new_rule_id = mysqli_insert_id($this->connectionlink);
					//End Create Rule for New Version

					//Start insert rule_id_text
					$rule_id_text = $data['version_name'].'-'.$new_rule_id;
					$query34 = "update business_rule set rule_id_text = '".mysqli_real_escape_string($this->connectionlink,$rule_id_text)."' where rule_id = '".$new_rule_id."'";
					$q34 = mysqli_query($this->connectionlink,$query34);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					//End insert rule_id_text


					//Start get old_rule_facilities and create for new rule
					$query2 = "select * from business_rule_facilities where rule_id = '".mysqli_real_escape_string($this->connectionlink,$old_rule_id)."'";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						while($row2 = mysqli_fetch_assoc($q2)) {
							$query3 = "insert into business_rule_facilities (rule_id,FacilityID,CreatedDate,CreatedBy) values ('".$new_rule_id."','".mysqli_real_escape_string($this->connectionlink,$row2['FacilityID'])."',NOW(),'".$_SESSION['user']['UserId']."')";
							$q3 = mysqli_query($this->connectionlink,$query3);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
						}
					}
					//End get old_rule_facilities and create for new


					//Start get old_rule_conditions and create for new rule
					$query4 = "select * from business_rule_condition where rule_id = '".mysqli_real_escape_string($this->connectionlink,$old_rule_id)."'";
					$q4 = mysqli_query($this->connectionlink,$query4);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						while($row4 = mysqli_fetch_assoc($q4)) {
							$query5 = "insert into business_rule_condition (rule_id,attribute_id,created_date,created_by,operator,value,multiple_values) values ('".$new_rule_id."','".mysqli_real_escape_string($this->connectionlink,$row4['attribute_id'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row4['operator'])."','".mysqli_real_escape_string($this->connectionlink,$row4['value'])."','".mysqli_real_escape_string($this->connectionlink,$row4['multiple_values'])."')";
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
						}
					}
					//End get old_rule_conditions and create for new

				}

				//Start get version details
				$query6 = "select * from business_rule_versions where version_id = '".$version_id."'";
				$q6 = mysqli_query($this->connectionlink,$query6);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row6 = mysqli_fetch_assoc($q6);
					$json['version'] = $row6;
				}
				//End get version details

				$json['Success'] = true;
				$json['Result'] = "New Version Created";
				return json_encode($json);

			} else {
				$json['Success'] = false;
				$json['Result'] = "No Rules available in Current Version";
				return json_encode($json);
			}


			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function MakeVersionActive ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule List Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Business Rule List Page';
				return json_encode($json);
			}

			$query = "update business_rule_versions set status = 'Active' where version_id = '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$json['Success'] = true;
			$json['Result'] = 'Version Status changed to Active';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function MakeCurrentVersion ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule List Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Business Rule List Page';
				return json_encode($json);
			}

			//Start check If rule is capable to edit
			$query32 = "select * from business_rule_versions where version_id = '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."' ";
			$q32 = mysqli_query($this->connectionlink,$query32);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row32 = mysqli_fetch_assoc($q32);
				if($row32['status'] != 'Active') {
					$json['Success'] = false;
					$json['Result'] = 'Version with only Active Status are only allowed to Modify';
					return json_encode($json);
				}

				if($row32['current_version'] == '1') {
					$json['Success'] = false;
					$json['Result'] = 'Version is already Current Version';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Version';
				return json_encode($json);
			}
			//End check If rule is capable to edit

			// First, get the current version
			$query_prev = "select version_id from business_rule_versions where current_version = '1'";
			$q_prev = mysqli_query($this->connectionlink,$query_prev);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row_current_version = mysqli_fetch_assoc($q_prev);
				$prev_version_id = $row_current_version['version_id'];				
			} else {
				$prev_version_id = 0;
			}			

			// Clear all current versions and other previous versions
			//$query = "update business_rule_versions set current_version = '0', previous_version = '0' where version_id != '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."'";
			$query = "update business_rule_versions set current_version = '0', previous_version = '0' where 1";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Set the new version as current and clear its previous version flag
			$query1 = "update business_rule_versions set current_version = '1', updated_date = NOW(), updated_by = '".$_SESSION['user']['UserId']."' where version_id = '".mysqli_real_escape_string($this->connectionlink,$data['version_id'])."' ";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			//set previous version
			if($prev_version_id > 0) {
				$query2 = "update business_rule_versions set previous_version = '1' where version_id = '".$prev_version_id."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}


			$json['Success'] = true;
			$json['Result'] = 'Version made as Current Version';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetAWSCustomers ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}

			$query = "SELECT AWSCustomerID, Customer FROM aws_customers ORDER BY Customer";
			$q = mysqli_query($this->connectionlink, $query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$customers = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)) {
					$customers[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $customers;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No AWS Customers Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPartTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}

			if(!isset($data['FacilityID']) || $data['FacilityID'] == '') {
				$json['Success'] = false;
				$json['Result'] = "Facility ID is required";
				return json_encode($json);
			}

			$query = "SELECT DISTINCT parttypeid as PartTypeID, parttype as PartTypeName
					FROM parttype
					WHERE FacilityID = ".mysqli_real_escape_string($this->connectionlink, $data['FacilityID'])."
					AND Status = 1
					ORDER BY parttype";

			$q = mysqli_query($this->connectionlink, $query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$partTypes = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)) {
					$partTypes[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $partTypes;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Part Types Available for the selected Facility";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetAllPartTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}

			$query = "SELECT DISTINCT parttype as PartTypeName
					FROM parttype
					WHERE Status = 1
					ORDER BY parttype";

			$q = mysqli_query($this->connectionlink, $query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$partTypes = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)) {
					$partTypes[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $partTypes;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Part Types Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetSourceTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}

			$query = "SELECT idCustomertype, Cumstomertype, Description
					FROM customertype
					WHERE Active = 'Active'
					ORDER BY Cumstomertype";

			$q = mysqli_query($this->connectionlink, $query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$sourceTypes = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)) {
					$sourceTypes[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $sourceTypes;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Source Types Available";
			}

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetMaterialTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule Page';
				return json_encode($json);
			}

			$query = "SELECT MaterialType
					FROM material_types
					WHERE 1
					ORDER BY MaterialType";

			$q = mysqli_query($this->connectionlink, $query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$materialTypes = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)) {
					$materialTypes[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $materialTypes;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Material Types Available";
			}

			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPendingBRVersions ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Business Rule List')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Business Rule List Page';
				return json_encode($json);
			}
			$query = "select * from business_rule_versions where status = 'Pending' order by version_name";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Business Rule Versions available with Pending Status";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

}
?>