<?php
session_start();
include_once("connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();

$sql = "Select * from ASNItemSerials where Processed = 0";
$query = mysqli_query($connectionlink,$sql);
while($row = mysqli_fetch_assoc($query))
{
    $sqlload = "Select * from ASNFile where TrackingNumber = '".$row['TrackingNumber']."' and TransmissionControlNumber = '".$row['TransmissionControlNumber']."'";
    $queryload = mysqli_query($connectionlink,$sqlload);
    $rowload = mysqli_fetch_assoc($queryload);
    $sqlpal = "Select * from ASNPallets where TrackingNumber = '".$row['TrackingNumber']."' AND HierarchicalIdNumber = '".$row['ParentHierarchicalIdNumber']."' AND ReceivedDate = '".$row['RecevedDate']."' and TransmissionControlNumber = '".$row['TransmissionControlNumber']."'";
    $querypal = mysqli_query($connectionlink,$sqlpal);
    $rowpal = mysqli_fetch_assoc($querypal);
    $sqlfac = "Select FacilityID from facility where FacilityName = '".$rowload['ShipTo']."' and FacilityStatus = 1";
    $queryfac = mysqli_query($connectionlink,$sqlfac);
    $rowfac = mysqli_fetch_assoc($queryfac);
    $query101 = "select * from customer where CustomerShotCode = '".$rowload['ShipFrom']."'";
    $q101 = mysqli_query($connectionlink,$query101);
    
    if(mysqli_affected_rows($connectionlink) > 0) {
        $row101 = mysqli_fetch_assoc($q101);
        $idCustomer = $row101['CustomerID'];
        $AWSCustomerID = $row101['AWSCustomerID'];
    } else {
        $idCustomer = NULL;
        $AWSCustomerID = NULL;
    }
    if($row['AssetId'] != '')
    {
        $SerialNumber = $row['AssetId'];
    }
    else
    {
        $SerialNumber = $row['SerialNumber'];
    }
    $LoadID = $row['TrackingNumber'];
    $PalletID = $rowpal['PackagingIdentifier'];
    $MPN = str_replace('^', '', $row['Mpn']); // Replaces ^ spaces with null.
    $APN = $row['Ipn'];
    //$query191 = "select part_type,apn_id,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($connectionlink,$MPN)."' AND apn_id = '".mysqli_real_escape_string($connectionlink,$APN)."'";
    $query191 = "select part_type,apn_id,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($connectionlink,$MPN)."' AND FacilityID = '".$rowfac['FacilityID']."' ";
    $q191 = mysqli_query($connectionlink,$query191);
    
    if(mysqli_affected_rows($connectionlink) > 0) {
        $row191 = mysqli_fetch_assoc($q191);
        $PART_TYPE = $row191['part_type'];
        $AWSIPN = $row191['apn_id'];
        $ID_MANU = $row191['idManufacturer'];
    } else {
        $PART_TYPE = 'n/a';
        $AWSIPN = 'n/a';
        $ID_MANU = '';
    }
    //Start check If Serial Number already exists for the Load
    $queryasncount = "select count(*) as asncount from asn_assets where SerialNumber = '".mysqli_real_escape_string($connectionlink,$SerialNumber)."' and LoadId = '".mysqli_real_escape_string($connectionlink,$LoadID)."'";
    $qasncount = mysqli_query($connectionlink,$queryasncount);
    
    if(mysqli_affected_rows($connectionlink) > 0) {
        $rowasncount = mysqli_fetch_assoc($qasncount);
        if($rowasncount['asncount'] == 0) {// New Serial Number
            //start check If Load exists
            $query1 = "select count(*) from loads where LoadId = '".mysqli_real_escape_string($connectionlink,$LoadID)."'";
            $q1 = mysqli_query($connectionlink,$query1);
            
            if(mysqli_affected_rows($connectionlink) > 0) {
                $row1 = mysqli_fetch_assoc($q1);
                if($row1['count(*)'] == 0) {
                    $query2 = "insert into loads (LoadId,DateCreated,CreatedBy,LoadType,FacilityID,UploadID,idCustomer,TransmissionControlNumber,TransmissionTimeStamp) values ('".mysqli_real_escape_string($connectionlink,$LoadID)."',NOW(),'778','ASN Upload','".$rowfac['FacilityID']."','".$rowload['ASNFilecol']."','".$idCustomer."','".$rowload['TransmissionControlNumber']."','".$rowload['TransmissionTimeStamp']."')";
                    $q2 = mysqli_query($connectionlink,$query2);
                    echo $query2;
                    //Start insert in load tracking
                    $query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($connectionlink,$LoadID)."','New Inbound Shipment Ticket Created through ASN Upload',NOW(),'778')";			
                    $q10 = mysqli_query($connectionlink,$query10);
                   //End insert in load tracking

                }
            }
            //End check If Load exists

            //Start check If Pallet exists
            $query3 = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($connectionlink,$PalletID)."'";
            $q3 = mysqli_query($connectionlink,$query3);
            
            if(mysqli_affected_rows($connectionlink) > 0) {
                $row3 = mysqli_fetch_assoc($q3);
                if($row3['count(*)'] == 0) {
                    
                    if($rowpal['Sealid2'] == '') {
                        $rowpal['Sealid2'] = 'n/a';
                    }
                    if($rowpal['Sealid3'] == '') {
                        $rowpal['Sealid3'] = 'n/a';
                    }
                    if($rowpal['Sealid4'] == '') {
                        $rowpal['Sealid4'] = 'n/a';
                    }
                    if($rowpal['SpeedRack'] == true) {
                        $rowpal['PackagingCode'] = 'Media Rack';
                    }
                    if($rowpal['pofDelivery'] == true) {
                        $rowpal['POF'] = '1';
                    }
                    else
                    {
                        $rowpal['POF'] = '0';
                    }
                    if($rowpal['PackagingCode'] != '') {
                        
                        $query11 = "select * from material_types where MaterialType = '".mysqli_real_escape_string($connectionlink,$rowpal['PackagingCode'])."'";
                        $q11 = mysqli_query($connectionlink, $query11);                        
                        if (mysqli_affected_rows($connectionlink) > 0) {
                            $row1 = mysqli_fetch_assoc($q1);
                            $MaterialTypeID = $row11['MaterialTypeID'];
                        } else {
                            $MaterialTypeID = 0;
                        }                                                
                    } else {
                        $MaterialTypeID = 0;
                        $idCustomertype = 0;
                    }

                    if($MaterialTypeID > 0 && $AWSCustomerID > 0) {
                        $query12 = "select * from source_type_configuration where Status = 'Active' and FacilityID = '".$rowfac['FacilityID']."' and AWSCustomerID = '".mysqli_real_escape_string($connectionlink,$AWSCustomerID)."' and MaterialTypeID = '".mysqli_real_escape_string($connectionlink,$MaterialTypeID)."'";
                        $q12 = mysqli_query($connectionlink, $query12);
                        if (mysqli_error($connectionlink)) {
                            echo mysqli_error($connectionlink);
                        }
                        if (mysqli_affected_rows($connectionlink) > 0) {
                            $row12 = mysqli_fetch_assoc($q12);
                            $idCustomertype = $row12['idCustomertype'];
                        } else {
                            $idCustomertype = 0;
                        }
                    } else {
                        $idCustomertype = 0;
                    }
                    $sqlrackcount = "Select count(*) as rackcount from ASNItemSerials where TrackingNumber = '".$row['TrackingNumber']."' AND ParentHierarchicalIdNumber = '".$row['ParentHierarchicalIdNumber']."' AND RecevedDate = '".$row['RecevedDate']."' and TransmissionControlNumber = '".$row['TransmissionControlNumber']."' and AssetId != ''";
                    $queryrackcount = mysqli_query($connectionlink, $sqlrackcount);
                    if (mysqli_error($connectionlink)) {
                       echo mysqli_error($connectionlink);
                    }
                    $rowrackcount = mysqli_fetch_assoc($queryrackcount);
                    if($rowrackcount['rackcount'] > 0)
                    {
                        $rackassetid = 1;
                    }
                    else
                    {
                        $rackassetid = 0;
                    }
                    $query4 = "insert into pallets (idPallet,LoadId,status,CreatedDate,CreatedBy,Type,SealNo1,SealNo2,SealNo3,SealNo4,PalletFacilityID,UploadID,MaterialType,POF,UnitOfMeasure,VolumeValue,VolumeUnitOfMeasure,HeightValue,HeightUnitOfMeasure,WidthValue,WidthUnitOfMeasure,LengthValue,LengthUnitOfMeasure,SpeedRack,PofDelivery,HierarchicalIdNumber,PackagingCode";
                    if($idCustomer > 0) {
                        $query4 = $query4 .",idCustomer";
                    }
                    if($AWSCustomerID > 0) {
                        $query4 = $query4 .",AWSCustomerID";
                    }
                    if($idCustomertype > 0) {
                        $query4 = $query4 .",idCustomertype";
                    }
                    $query4 = $query4 . ") values ('".mysqli_real_escape_string($connectionlink,$PalletID)."','".mysqli_real_escape_string($connectionlink,$LoadID)."','6',NOW(),'778','ASN Upload','".mysqli_real_escape_string($connectionlink,$rowpal['SealIds'])."','".mysqli_real_escape_string($connectionlink,$rowpal['Sealid2'])."','".mysqli_real_escape_string($connectionlink,$rowpal['Sealid3'])."','".mysqli_real_escape_string($connectionlink,$rowpal['Sealid4'])."','".$rowfac['FacilityID']."','".$rowload['ASNFilecol']."','".mysqli_real_escape_string($connectionlink,$rowpal['PackagingCode'])."','".mysqli_real_escape_string($connectionlink,$rowpal['POF'])."','".mysqli_real_escape_string($connectionlink,$rowpal['UnitOfMeasure'])."','".mysqli_real_escape_string($connectionlink,$rowpal['VolumeValue'])."','".mysqli_real_escape_string($connectionlink,$rowpal['VolumeUnitOfMeasure'])."','".mysqli_real_escape_string($connectionlink,$rowpal['HeightValue'])."','".mysqli_real_escape_string($connectionlink,$rowpal['HeightUnitOfMeasure'])."','".mysqli_real_escape_string($connectionlink,$rowpal['WidthValue'])."','".mysqli_real_escape_string($connectionlink,$rowpal['WidthUnitOfMeasure'])."','".mysqli_real_escape_string($connectionlink,$rowpal['LengthValue'])."','".mysqli_real_escape_string($connectionlink,$rowpal['LengthUnitOfMeasure'])."','".mysqli_real_escape_string($connectionlink,$rackassetid)."','".mysqli_real_escape_string($connectionlink,$rowpal['POF'])."','".mysqli_real_escape_string($connectionlink,$rowpal['HierarchicalIdNumber'])."','".mysqli_real_escape_string($connectionlink,$rowpal['PackagingCode'])."'";
                    
                    if($idCustomer > 0) {
                        $query4 = $query4 .",'".$idCustomer."'";
                    }
                    if($AWSCustomerID > 0) {
                        $query4 = $query4 .",'".$AWSCustomerID."'";
                    }
                    if($idCustomertype > 0) {
                        $query4 = $query4 .",'".$idCustomertype."'";
                    }
                    $query4 = $query4 . ")";
                    echo $query4;
                    $q4 = mysqli_query($connectionlink,$query4);
                    if (mysqli_error($connectionlink)) {
                       echo mysqli_error($connectionlink);
                    }
                    $query12 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($connectionlink,$PalletID)."','Container Created through ASN Upload','','',NOW(),'778','','','')";			
                    $q12 = mysqli_query($connectionlink,$query12);
                    
                }
            }
            //end check If Pallet exists

            //Start check If Pallet Item exists
            $query3 = "select id from pallet_items where palletId = '".mysqli_real_escape_string($connectionlink,$PalletID)."' and UniversalModelNumber = '".mysqli_real_escape_string($connectionlink,$MPN)."'";
            $q3 = mysqli_query($connectionlink,$query3);
            if(mysqli_affected_rows($connectionlink) > 0) {
                $row3 = mysqli_fetch_assoc($q3);
                $pallet_item_id = $row3['id'];
                $query5 = "update pallet_items set quantity = quantity + 1 where id = '".mysqli_real_escape_string($connectionlink,$pallet_item_id)."'";
                $q5 = mysqli_query($connectionlink,$query5);
                
            } else {
                $query4 = "insert into pallet_items (palletId,quantity,UniversalModelNumber,CreatedDate,CreatedBy,Type,UploadID) values ('".mysqli_real_escape_string($connectionlink,$PalletID)."','1','".mysqli_real_escape_string($connectionlink,$MPN)."',NOW(),'778','ASN Upload','".$rowload['ASNFilecol']."')";
                $q4 = mysqli_query($connectionlink,$query4);
                $pallet_item_id = mysqli_insert_id($connectionlink);
            }
            //end check If Pallet Item exists

             //Start check If Pallet exists
             $query5 = "select count(*) from asn_assets where SerialNumber = '".mysqli_real_escape_string($connectionlink,$SerialNumber)."'";
             $q5 = mysqli_query($connectionlink,$query5);
             if(mysqli_affected_rows($connectionlink) > 0) {
                $row5 = mysqli_fetch_assoc($q5);
                if($row5['count(*)'] == 0) {
                    //Start create asset
                    $OriSerialNumber = $SerialNumber;
				    $SerialNumber = preg_replace('/[^A-Za-z0-9]/', '', $SerialNumber);
                    $query6 = "insert into asn_assets (LoadId,idPallet,PalletItemsID,SerialNumber,UniversalModelNumber,CreatedDate,CreatedBy,Type,UploadID,OriginalPalletID,apn_id,part_type,idManufacturer,ActualSerialNumber,HierarchicalIdNumber,ParentHierarchicalIdNumber,Ipn,Mpn,Quantity,Uom) values ('".mysqli_real_escape_string($connectionlink,$LoadID)."','".mysqli_real_escape_string($connectionlink,$PalletID)."','".mysqli_real_escape_string($connectionlink,$pallet_item_id)."','".mysqli_real_escape_string($connectionlink,$SerialNumber)."','".mysqli_real_escape_string($connectionlink,$MPN)."',NOW(),'778','ASN Upload','".$rowload['ASNFilecol']."','".mysqli_real_escape_string($connectionlink,$PalletID)."','".mysqli_real_escape_string($connectionlink,$AWSIPN)."','".mysqli_real_escape_string($connectionlink,$PART_TYPE)."','".mysqli_real_escape_string($connectionlink,$ID_MANU)."','".mysqli_real_escape_string($connectionlink,$OriSerialNumber)."','".mysqli_real_escape_string($connectionlink,$row['HierarchicalIdNumber'])."','".mysqli_real_escape_string($connectionlink,$row['ParentHierarchicalIdNumber'])."','".mysqli_real_escape_string($connectionlink,$row['Ipn'])."','".mysqli_real_escape_string($connectionlink,$row['Mpn'])."','".mysqli_real_escape_string($connectionlink,$row['Quantity'])."','".mysqli_real_escape_string($connectionlink,$row['Uom'])."')";
                    $q6 = mysqli_query($connectionlink,$query6);
                    //$assets_count = $assets_count + 1;
                    //End create asset
                }
            }
        }
    }
    //End check IF Serial Number alredy exists for the Load
    $sqlupdateprocess = "UPDATE ASNItemSerials SET ProcessDate = NOW(), Processed = '1' WHERE ASNItemSerialsID = '".$row['ASNItemSerialsID']."'";
    $queryupdateprocess = mysqli_query($connectionlink,$sqlupdateprocess);
}
?>