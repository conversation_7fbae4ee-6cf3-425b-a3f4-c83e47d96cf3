<?php
session_start();
include_once("tracking.class.php");
class SpeedTrackingClass extends TrackingClass {
	public function GetAssetDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$query = "select A.*,D.disposition,SS.Status,C.BinName,br.rule_name,br.rule_description,br.rule_id_text from speed_media_recovery A 
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
                        LEFT JOIN speed_status SS on A.StatusID = SS.StatusID 
                        LEFT JOIN custompallet C on A.CustomPalletID = C.CustomPalletID  
						left join business_rule br on A.rule_id = br.rule_id 
						where A.MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				//Start check If serial available in pending save
				$query = "select count(*) from parts_recovery_wip_records where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {	
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Serial is not committed';
					return json_encode($json);
				}
				//End check If serial available in pending save

				$json['Success'] = false;
				$json['Result'] = "No Media Serial numbers available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	// public function GetAssetcompleteDetails ($data) {
	// 	if(!isset($_SESSION['user'])) {
	// 		$json['Success'] = false;
	// 		$json['Result'] = 'Login to continue';
	// 		return json_encode($json);
	// 	}
	// 	$json = array(
	// 		'Success' => false,
	// 		'Result' => 'No Data'
	// 	);	
	// 	try {
	// 		if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
	// 			$json['Success'] = false;
	// 			$json['Result'] = 'No Access to Tracking Page';
	// 			return json_encode($json);
	// 		}
	// 		$query = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition from asset A
	// 					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id
	// 					where A.SerialNumber = '".$data['SerialNumber']."'";
	// 		$q = mysqli_query($this->connectionlink,$query);
	// 		if(mysqli_error($this->connectionlink)) {	
	// 			$json['Success'] = false;
	// 			$json['Result'] = mysqli_error($this->connectionlink);
	// 			return json_encode($json);
	// 		}
	// 		if(mysqli_affected_rows($this->connectionlink) > 0) {
	// 			$i = 0;
	// 			$row = mysqli_fetch_assoc($q);
	// 			$json['Success'] = true;
	// 			$json['Result'] = $row;
	// 		} else {
	// 			$json['Success'] = false;
	// 			$json['Result'] = "No Serial numbers available";
	// 		}
	// 		return json_encode($json);

	// 	} catch (Exception $e) {
	// 		$json['Success'] = false;
	// 		$json['Result'] = $e->getMessage();
	// 		return json_encode($json);
	// 	}
	// }

	public function GetAssetcompleteTracking ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$querytrack = "select AT.Action,AT.CreatedDate,U.FirstName,U.LastName from speed_media_recovery_tracking AT 
            LEFT JOIN users U ON U.UserId = AT.CreatedBy 
            where AT.MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."' order by CreatedDate";
            $qtrack = mysqli_query($this->connectionlink,$querytrack);
            if(mysqli_error($this->connectionlink)) {	
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				$result = array();
                while($rowtrack = mysqli_fetch_assoc($qtrack)) {
                    $dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
                    $rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
                    $result[$i] = $rowtrack;
                    $i++;
                }
            } else {
                $json['Success'] = false;
                $json['Result'] = "No Tracking available";
                return json_encode($json);
            }
            $json['Success'] = true;
            $json['Result'] = $result;
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

    public function GerServerDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$query = "select A.*,D.disposition,SS.Status,C.BinName,br.rule_name,br.rule_description,br.rule_id_text from speed_server_recovery A 
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
                        LEFT JOIN asset_status SS on A.StatusID = SS.StatusID 
                        LEFT JOIN custompallet C on A.CustomPalletID = C.CustomPalletID  
						left join business_rule br on A.rule_id = br.rule_id 
						where A.ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['ServerSerialNumber'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				// $i = 0;
				// while($row = mysqli_fetch_assoc($q)) {
				// 	$result[$i] = $row;
				// 	$i++;
				// }                
                $row = mysqli_fetch_assoc($q);
                $row['ServerMedia'] = array();
				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				//Start check If serial available in pending save
				$query = "select count(*) from parts_recovery_wip_records where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['ServerSerialNumber'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {	
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Serial is not committed';
					return json_encode($json);
				}
				//End check If serial available in pending save

				$json['Success'] = false;
				$json['Result'] = "Invalid Host Asset ID";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

    public function GetServerLifeCycle ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$querytrack = "select AT.Action,AT.CreatedDate,U.FirstName,U.LastName from speed_server_recovery_tracking AT 
            LEFT JOIN users U ON U.UserId = AT.CreatedBy 
            where AT.ServerID = '".mysqli_real_escape_string($this->connectionlink,$data['ServerID'])."' order by CreatedDate";
            $qtrack = mysqli_query($this->connectionlink,$querytrack);
            if(mysqli_error($this->connectionlink)) {	
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
                while($rowtrack = mysqli_fetch_assoc($qtrack)) {
                    $dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
                    $rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
                    $result[$i] = $rowtrack;
                    $i++;
                }
            } else {
                $json['Success'] = false;
                $json['Result'] = "No Tracking available";
                return json_encode($json);
            }
            $json['Success'] = true;
            $json['Result'] = $result;
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

    public function GetSeverMedia ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			
            $query = "select A.*,D.disposition,SS.Status,C.BinName from speed_media_recovery A 
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
                        LEFT JOIN speed_status SS on A.StatusID = SS.StatusID 
                        LEFT JOIN custompallet C on A.CustomPalletID = C.CustomPalletID  
						where A.ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['ServerSerialNumber'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Media available for Host Asset ID";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function GetPalletDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$query = "Select P.idPallet,L.LocationName,P.SealNo1,P.SealNo2,P.SealNo3,P.SealNo4,P.ReceivedDate,P.LoadId,SC.CustomerName,SS.StatusValue from pallets P 
                LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer  
                LEFT JOIN location L ON L.LocationID = P.WarehouseLocationId 
                LEFT JOIN pallet_status SS on SS.status = P.status 
                WHERE P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['PalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$dt = strtotime($row['ReceivedDate']); //make timestamp with datetime string
					$row['ReceivedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Rack ID";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
    public function GetPalletAssetDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$i = 0;
            $result = array();
			$query = "select A.*,D.disposition,SS.Status,C.BinName from speed_server_recovery A 
                LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
                LEFT JOIN asset_status SS on A.StatusID = SS.StatusID 
                LEFT JOIN custompallet C on A.CustomPalletID = C.CustomPalletID  
                where A.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['PalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {				
				while($row = mysqli_fetch_assoc($q)) {
                    $row['ServerMedia'] = array();
					$result[$i] = $row;
					$i++;
				}                				
			}	
			
			//Stat get from Assets table
			$query = "select A.*,D.disposition,br.rule_name,br.rule_description,br.rule_id_text,SS.Status,C.BinName from asset A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
			left join business_rule br on A.rule_id = br.rule_id 
			LEFT JOIN asset_status SS on A.StatusID = SS.StatusID 
			LEFT JOIN custompallet C on A.CustomPalletID = C.CustomPalletID  
			where A.idPallet = '".$data['PalletID']."'";

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {				
				while($row = mysqli_fetch_assoc($q)) {
					$row['ItemType'] = 'Asset';
                    $row['ServerMedia'] = array();
					$result[$i] = $row;
					$i++;
				}                				
			}
			//End get from Assets table

			if(count($result) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'No serials available';
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = $result;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function GetPalletcompleteTracking ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$querytrack = "select PT.Action,PT.CreatedDate,U.FirstName,U.LastName from pallet_tracking PT 
							LEFT JOIN users U ON U.UserId = PT.CreatedBy
							where PT.idPallet = '".$data['PalletID']."'";
			$qtrack = mysqli_query($this->connectionlink,$querytrack);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($rowtrack = mysqli_fetch_assoc($qtrack)) {
					$dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
					$rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $rowtrack;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			}
			else {
				$json['Success'] = false;
				$json['Result'] = "No Tracking available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
    public function RecordUserTrackingActivity($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$transaction = $data['Type'];
		$description = '';
		if($data['SerialNumber']) {
			$description = 'Media SN ('.$data['SerialNumber'].') Tracking Exported';
		} else if($data['ServerSerial']) {
			$description = 'Host Asset ID ('.$data['SerialNumber'].') Tracking Exported';
		} else if($data['PalletID']) {
			$description = 'Rack ID ('.$data['PalletID'].') Tracking Exported';
		}
		$this->RecordUserTransaction($transaction,$description);

		$json['Success'] = true;
		$json['Result'] = "Transaction Recorded";
		return json_encode($json);
	}













	public function GetTicketDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			/*$query = "select L.LoadId,SC.CustomerName,F.FacilityName,L.DateReceived from loads L 
				LEFT JOIN customer SC ON SC.CustomerID = L.idCustomer
				LEFT JOIN facility F ON F.FacilityID = L.FacilityID
				where L.LoadId = '".$data['TicketID']."'";*/

			$query = "select L.LoadId,F.FacilityName,L.DateReceived from loads L 			
			LEFT JOIN facility F ON F.FacilityID = L.FacilityID
			where L.LoadId = '".$data['TicketID']."'";
			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q))
				{
					if($row['DateReceived'] != '')
					{	$dt = strtotime($row['DateReceived']); //make timestamp with datetime string
						$row['DateReceived'] = date("Y-m-d H:i:s", $dt);
					}
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Tickets available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetLoadPalletDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "Select P.idPallet,L.LocationName,P.SealNo1,P.ReceivedDate,SC.CustomerName from pallets P 
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer 
				LEFT JOIN location L ON L.LocationID = P.WarehouseLocationId 
				WHERE P.LoadId = '".$data['LoadID']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q))
				{
					$dt = strtotime($row['ReceivedDate']); //make timestamp with datetime string
					$row['ReceivedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Containers available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetLoadcompleteDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "select L.LoadId,F.FacilityName,L.DateReceived from loads L 
						LEFT JOIN facility F ON F.FacilityID = L.FacilityID 
						where L.LoadId = '".$data['LoadId']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				$dt = strtotime($row['DateReceived']); //make timestamp with datetime string
				$row['DateReceived'] = date("Y-m-d H:i:s", $dt);
				$row = mysqli_fetch_assoc($q);
				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Tickets available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetLoadcompleteTracking ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$querytrack = "select LT.Action,LT.CreatedDate,U.FirstName,U.LastName from load_tracking LT 
							LEFT JOIN users U ON U.UserId = LT.CreatedBy
							where LT.LoadId = '".$data['LoadId']."'";
			$qtrack = mysqli_query($this->connectionlink,$querytrack);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($rowtrack = mysqli_fetch_assoc($qtrack)) {
					$dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
					$rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $rowtrack;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			}
			else {
				$json['Success'] = false;
				$json['Result'] = "No Tracking available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPalletcompleteDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "Select P.idPallet,L.LocationName,P.SealNo1,P.ReceivedDate,SC.CustomerName from pallets P 
						LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer 
						LEFT JOIN location L ON L.LocationID = P.WarehouseLocationId
						WHERE P.idPallet = '".$data['PalletID']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				$dt = strtotime($row['ReceivedDate']); //make timestamp with datetime string
				$row['ReceivedDate'] = date("Y-m-d H:i:s", $dt);
				$row = mysqli_fetch_assoc($q);
				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Container available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetMediaSNSDetails ($data) {		
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$querytrack = "select * from speed_sns_messages	where MediaSerialNumber = '".$data['MediaSerialNumber']."'";
			$qtrack = mysqli_query($this->connectionlink,$querytrack);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($rowtrack = mysqli_fetch_assoc($qtrack)) {
					$dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
					$rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $rowtrack;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			}
			else {
				$json['Success'] = false;
				$json['Result'] = "No SNS Details available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetServerSNSDetails ($data) {		
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$querytrack = "select * from speed_sns_messages	where ServerSerialNumber = '".$data['ServerSerialNumber']."' and isnull(MediaID)";
			$qtrack = mysqli_query($this->connectionlink,$querytrack);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($rowtrack = mysqli_fetch_assoc($qtrack)) {
					$dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
					$rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $rowtrack;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			}
			else {
				$json['Success'] = false;
				$json['Result'] = "No SNS Details available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetSpeedSNType ($data) {		
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			//Start check If Serial is Media or not
			$query = "select count(*) from speed_media_recovery where MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SpeedSN'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['count(*)'] > 0) {
					//Start check If media is from SPEED or not
					$query6 = "select p.MaterialType from speed_media_recovery m 
					left join pallets p on m.idPallet = p.idPallet 
					where m.MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SpeedSN'])."'
					";
					$q6 = mysqli_query($this->connectionlink,$query6);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row6 = mysqli_fetch_assoc($q6);
						if($row6['MaterialType'] != 'Media Rack') {
							$json['Success'] = false;
							$json['Result'] = 'Scanned Serial is not Media Serial';
							return json_encode($json);
						}
					}
					//End check If media is from SPEED or not

					$json['Success'] = true;
					$json['SerialType'] = 'Media';
					$json['SN'] = $data['SpeedSN'];
					return json_encode($json);
				} else { // Start check If Serial is server or switch

					$query1 = "select count(*) from speed_server_recovery where ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SpeedSN'])."'";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						if($row1['count(*)'] > 0) {

							//Start check If media is from SPEED or not
							$query7 = "select p.MaterialType from speed_server_recovery m 
							left join pallets p on m.idPallet = p.idPallet 
							where m.ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SpeedSN'])."'";
							$q7 = mysqli_query($this->connectionlink,$query7);
							if(mysqli_error($this->connectionlink)) {	
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row7 = mysqli_fetch_assoc($q7);
								if($row7['MaterialType'] != 'Media Rack') {
									$json['Success'] = false;
									$json['Result'] = 'Scanned Serial is not Media Serial';
									return json_encode($json);
								}
							}
							//End check If media is from SPEED or not

							$json['Success'] = true;
							$json['SerialType'] = 'Server';
							$json['SN'] = $data['SpeedSN'];
							return json_encode($json);
						} else {// Start check If Serial is Rack or not
							$query2 = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['SpeedSN'])."' and MaterialType = 'Media Rack'";
							$q2 = mysqli_query($this->connectionlink,$query2);
							if(mysqli_error($this->connectionlink)) {	
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row2 = mysqli_fetch_assoc($q2);
								if($row2['count(*)'] > 0) {
									$json['Success'] = true;
									$json['SerialType'] = 'Rack';
									$json['SN'] = $data['SpeedSN'];
									return json_encode($json);
								} else {


									//Start check in asset table

									$query4 = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SpeedSN'])."'";
									$q4 = mysqli_query($this->connectionlink,$query4);
									if(mysqli_error($this->connectionlink)) {	
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row4 = mysqli_fetch_assoc($q4);
										if($row4['count(*)'] > 0) {

											$query6 = "select p.MaterialType from asset m 
											left join pallets p on m.idPallet = p.idPallet 
											where m.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SpeedSN'])."'
											";
											$q6 = mysqli_query($this->connectionlink,$query6);
											if(mysqli_error($this->connectionlink)) {	
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}
											if(mysqli_affected_rows($this->connectionlink) > 0) {
												// $row6 = mysqli_fetch_assoc($q6);
												// if($row6['MaterialType'] != 'Media Rack') {
												// 	$json['Success'] = false;
												// 	$json['Result'] = 'Scanned Serial is not Media Serial';
												// 	return json_encode($json);
												// }
												$valid_media = false;
												while($row6 = mysqli_fetch_assoc($q6)) {
													if($row6['MaterialType'] == 'Media Rack') {
														$valid_media = true;
														break;
													}
												}
												if($valid_media == false) {
													$json['Success'] = false;
													$json['Result'] = 'Scanned Serial is not Media Serial';
													return json_encode($json);
												}
											}
											//End check If media is from SPEED or not

											$json['Success'] = true;
											$json['SerialType'] = 'Asset';
											$json['SN'] = $data['SpeedSN'];
											return json_encode($json);

										} else {
											$json['Success'] = false;
											$json['Result'] = 'Scanned Serial is not a valid Serial';
											return json_encode($json);
										}
									} else {
										$json['Success'] = false;
										$json['Result'] = 'Scanned Serial is not a valid Serial';
										return json_encode($json);
									}
									//End check in asset table									
								}
							} else {	
								
								$json['Success'] = false;
								$json['Result'] = 'Scanned Serial is not a valid Serial';
								return json_encode($json);
							}
						} 
					} else {
						$json['Success'] = false;
						$json['Result'] = 'Scanned Serial is not a valid Serial';
						return json_encode($json);
					}

				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Scanned Serial is not a valid Serial';
				return json_encode($json);
			}	
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GenerateserverExportTrackRecordsXLS($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
        $query = "select * from speed_server_recovery where idPallet = '".$data['PalletID']."'";
        $q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$_SESSION['idPallet'] = $data['PalletID'];
			$json['Success'] = true;
			$json['Result'] = 1;
			return json_encode($json);
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Data';
			return json_encode($json);
		}
		return json_encode($json);
	}


	public function TrackInput ($data) {		
		$json = array(
			'Success' => false,
			'Result' => $data
		);	
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Track Integration')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Track Integration Page';
				return json_encode($json);
			}

			if($data['InputType'] == 'SNS') {
				$json['Type'] = $data['InputType'];
				$querytrack = "select m.*,u.FirstName,u.LastName from speed_sns_messages m	
				left join users u on m.CreatedBy = u.UserId 
				where (m.MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SearchInput'])."' or m.ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SearchInput'])."')";
				$qtrack = mysqli_query($this->connectionlink,$querytrack);
				if(mysqli_error($this->connectionlink)) {	
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i = 0;
					while($rowtrack = mysqli_fetch_assoc($qtrack)) {
						$dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
						$rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
						$result[$i] = $rowtrack;
						$i++;
					}
					$json['Success'] = true;
					$json['Result'] = $result;
					return json_encode($json);
				} else {
					$querytrack = "select m.*,u.FirstName,u.LastName from speed_sns_messages m	
					left join users u on m.CreatedBy = u.UserId 
					where m.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['SearchInput'])."' and isnull(MediaSerialNumber) and isnull(ServerSerialNumber) ";
					$qtrack = mysqli_query($this->connectionlink,$querytrack);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$i = 0;
						while($rowtrack = mysqli_fetch_assoc($qtrack)) {
							$dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
							$rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
							$result[$i] = $rowtrack;
							$i++;
						}
						$json['Success'] = true;
						$json['Result'] = $result;
						return json_encode($json);
					} else {
						$json['Success'] = false;
						$json['Result'] = 'No Records Available';
						return json_encode($json);
					}
				}
			}

			if($data['InputType'] == 'API') {
				$json['Type'] = $data['InputType'];
				$query = "select r.*,u.FirstName,u.LastName from speed_rackdetails_api r 
				left join users u on r.APICalledBy = u.UserId 
				where r.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['SearchInput'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {	
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$result = array();
					$i = 0;
					while($row = mysqli_fetch_assoc($q)) {
						$result[$i] = $row;
						$i++;
					}
					$json['Success'] = true;
					$json['Result'] = $result;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'No Records Available';
					return json_encode($json);
				}
			}

			if($data['InputType'] == 'ASN') {
			$json['Type'] = $data['InputType'];
			$query = "select r.LoadId,r.idPallet,r.SerialNumber,r.UniversalModelNumber,r.AssetScanID,r.CreatedDate,r.AssetCreatedDate,r.part_type from asn_assets r where r.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['SearchInput'])."'";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {
							if ($key == 'LoadId') {
								$query = $query . " AND r.LoadId like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'idPallet') {
								$query = $query . " AND r.idPallet like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'SerialNumber') {
								$query = $query . " AND r.SerialNumber like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'UniversalModelNumber') {
								$query = $query . " AND r.UniversalModelNumber like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'part_type') {
								$query = $query . " AND r.part_type like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
						}
					}
				}
				if ($data['OrderBy'] != '') {
					if ($data['OrderByType'] == 'asc') {
						$order_by_type = 'asc';
					} else {
						$order_by_type = 'desc';
					}

					if ($data['OrderBy'] == 'LoadId') {
						$query = $query . " order by r.LoadId " . $order_by_type . " ";
					} else if ($data['OrderBy'] == 'idPallet') {
						$query = $query . " order by r.idPallet " . $order_by_type . " ";
					} else if ($data['OrderBy'] == 'SerialNumber') {
						$query = $query . " order by r.SerialNumber " . $order_by_type . " ";
					} else if ($data['OrderBy'] == 'SerialNumber') {
						$query = $query . " order by r.SerialNumber " . $order_by_type . " ";
					} else if ($data['OrderBy'] == 'UniversalModelNumber') {
						$query = $query . " order by r.UniversalModelNumber " . $order_by_type . " ";
					} else if ($data['OrderBy'] == 'part_type') {
						$query = $query . " order by r.part_type " . $order_by_type . " ";
					}
				} else {
					//$query = $query . " order by ManufacturerName desc ";
				}

				$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));

				$q = mysqli_query($this->connectionlink, $query);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$i = 0;
					while ($row = mysqli_fetch_assoc($q)) {
						$result[$i] = $row;
						$i++;
					}
					$json['Success'] = true;
					$json['Result'] = $result;
				} else {
					$json['Success'] = false;
					$json['Result'] = "No Records Available";
				}

				if ($data['skip'] == 0) {

					$query1 = "select count(*) from asn_assets r where r.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['SearchInput'])."'";
					if (count($data[0]) > 0) {
						foreach ($data[0] as $key => $value) {
							if ($value != '') {

								if ($key == 'LoadId') {
									$query1 = $query1 . " AND r.LoadId like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
								}
								if ($key == 'idPallet') {
									$query1 = $query1 . " AND r.idPallet like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
								}
								if ($key == 'SerialNumber') {
									$query1 = $query1 . " AND r.SerialNumber like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
								}
								if ($key == 'UniversalModelNumber') {
									$query1 = $query1 . " AND r.UniversalModelNumber like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
								}
								if ($key == 'part_type') {
									$query1 = $query1 . " AND r.part_type like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
								}
							}
						}
					}

					$q1 = mysqli_query($this->connectionlink, $query1);
					if (mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if (mysqli_affected_rows($this->connectionlink) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						$count = $row1['count(*)'];
					}
					$json['total'] = $count;
				}	
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function GenerateTrackIntegrationsxls($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		$transaction = 'Tracking ---> Track Integration';
		$description = 'Track Integration Exported';
		$this->RecordUserTransaction($transaction, $description);

		$_SESSION['TrackIntegrationsxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}



	public function GetAssetTracking ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$query = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,A.FirstSanitizationCustomPalletID from asset A
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id
						where A.SerialNumber = '".$data['SerialNumber']."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					if($row['StatusID'] == '10') {
						$json['Success'] = false;
						$json['Result'] = 'Serial is not committed';
						return json_encode($json);
					}
					if($row['FirstSanitizationCustomPalletID'] > 0) {//Sanitzaed Asset
						$query1 = "select sanitization_seal_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' order by CreatedDate desc limit 1";
						$q1 = mysqli_query($this->connectionlink,$query1);
						if(mysqli_error($this->connectionlink)) {	
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row1 = mysqli_fetch_assoc($q1);
							$row['sanitization_seal_id'] = $row1['sanitization_seal_id'];
						}
					}
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Serial numbers available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}





	public function GetAssetcompleteDetails14 ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			$query = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition from asset A
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id
						where A.SerialNumber = '".$data['SerialNumber']."'";
			
			$query = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,A.FirstSanitizationCustomPalletID,br.rule_name,br.rule_description,br.rule_id_text from asset A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
			left join business_rule br on A.rule_id = br.rule_id 
			where A.AssetScanID = '".$data['SerialNumber']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				$row = mysqli_fetch_assoc($q);

				if($row['FirstSanitizationCustomPalletID'] > 0) {//Sanitzaed Asset
					$query1 = "select sanitization_seal_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' order by CreatedDate desc limit 1";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						$row['sanitization_seal_id'] = $row1['sanitization_seal_id'];
					}
				}
				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Serial numbers available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetAssetcompleteTracking14 ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'SPEED Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to SPEED Tracking Page';
				return json_encode($json);
			}
			//$query = "select AssetScanID from asset where SerialNumber = '".$data['SerialNumber']."'";
			$query = "select AssetScanID from asset where AssetScanID = '".$data['SerialNumber']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				$row = mysqli_fetch_assoc($q);
				$querytrack = "select AT.Action,AT.CreatedDate,U.FirstName,U.LastName from asset_tracking AT 
								LEFT JOIN users U ON U.UserId = AT.CreatedBy
								where AT.AssetScanID = '".$row['AssetScanID']."'";
				$qtrack = mysqli_query($this->connectionlink,$querytrack);
				if(mysqli_error($this->connectionlink)) {	
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($rowtrack = mysqli_fetch_assoc($qtrack)) {
						$dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
						$rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
						$result[$i] = $rowtrack;
						$i++;
					}
				}
				else {
					$json['Success'] = false;
					$json['Result'] = "No Tracking available";
					return json_encode($json);
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Tracking available";
				return json_encode($json);
			}			

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



}
?>