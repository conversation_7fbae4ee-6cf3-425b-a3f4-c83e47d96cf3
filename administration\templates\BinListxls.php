<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['BinListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "BinList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Bin List');
//$header = array('Facility','Bin Name','Qty x Part Type','Location','Room','Disposition','Status');
//$header = array('Facility','Bin Name','Qty x Part Type','Mapped Stations','Disposition','Status');
$header = array('Facility','Bin Name','Parent Bin','Mobility Name','Bin Type','Mapped Stations','Disposition','Status');

/*$sql = "select c.*,f.FacilityName,l.LocationName,d.disposition,s.Status from facility f,location l,disposition d,custompallet_status s, custompallet c where f.FacilityID = c.FacilityID AND l.LocationID = c.LocationID AND d.disposition_id = c.disposition_id AND s.StatusID = c.StatusID";*/

$sql = "select c.*,f.FacilityName,d.disposition,s.Status,bt.BinType from custompallet c
            left join facility f on f.FacilityID = c.FacilityID
            left join disposition d on d.disposition_id = c.disposition_id
            left join custompallet_status s on s.StatusID = c.StatusID 
            left join bin_types bt on c.BinTypeID = bt.BinTypeID 
            where  1 ";
if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {
                    if($key == 'FacilityName') {
                        $sql = $sql . " AND f.FacilityName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }                   
                    if($key == 'BinName') {
                        $sql = $sql . " AND c.BinName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'LocationName') {
                        $sql = $sql . " AND l.LocationName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                   /* if($key == 'workflow') {
                        $sql = $sql . " AND w.workflow like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    } */                      
                    if($key == 'Room') {
                        $sql = $sql . " AND c.Room like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                     if($key == 'disposition') {
                        $sql = $sql . " AND d.disposition like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'Status') {
                        $sql = $sql . " AND s.Status like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    } 
                    if($key == 'MobilityName') {
                        $sql = $sql . " AND c.MobilityName like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'ParentBinName') {
                        $sql = $sql . " AND c.ParentBinName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'BinType') {
                        $sql = $sql . " AND bt.BinType like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                                   
                }
                //$sql = $sql . " AND `".$key."` like '%".$value."%' ";
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

            if($data['OrderBy'] == 'FacilityName') {
                $sql = $sql . " order by f.FacilityName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'BinName') {
                $sql = $sql . " order by c.BinName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'LocationName') {
                $sql = $sql . " order by l.LocationName ".$order_by_type." ";
            } /*else if($data['OrderBy'] == 'workflow') {
                $sql = $sql . " order by w.workflow ".$order_by_type." ";
            }*/ else if($data['OrderBy'] == 'Room') {
                $sql = $sql . " order by c.Room ".$order_by_type." ";
            } else if($data['OrderBy'] == 'disposition') {
                $sql = $sql . " order by d.disposition ".$order_by_type." ";
            } else if($data['OrderBy'] == 'Status') {
                $sql = $sql . " order by s.Status ".$order_by_type." ";
            } else if($data['OrderBy'] == 'MobilityName') {
                $sql = $sql . " order by c.MobilityName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'ParentBinName') {
                $sql = $sql . " order by c.ParentBinName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'BinType') {
                $sql = $sql . " order by bt.BinType ".$order_by_type." ";
            }

           
        } else {
            $sql = $sql . " order by f.FacilityName desc ";
        }
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
            while($row = mysqli_fetch_assoc($query)) {

                $parttype = '';				
				// $sqlpalitems = "Select count(*) as palquantity, CC.part_type from asset PI, catlog_creation CC
				// WHERE 
				// CC.mpn_id = PI.UniversalModelNumber
				// AND PI.CustomPalletID = '".$row['CustomPalletID']."' 
				// GROUP BY CC.part_type";

                // $sqlpalitems = "Select count(*) as palquantity, PI.part_type from asset PI	WHERE  PI.CustomPalletID = '".$row['CustomPalletID']."' GROUP BY PI.part_type";

				// $querypalitems = mysqli_query($connectionlink1,$sqlpalitems);
				// while($rowpalitems = mysqli_fetch_assoc($querypalitems)) {
				// 	$parttype = $parttype." ".$rowpalitems['palquantity']."-".$rowpalitems['part_type']."\n";
				// }
				// $row['parttype'] = $parttype;


                //Start get linked Stations
				$mapped_stations = '';
				$query_1 = "select s.SiteName from station_custompallet_mapping m,site s where m.SiteID = s.SiteID and m.CustomPalletID = '".$row['CustomPalletID']."' ";
				$q_1 = mysqli_query($connectionlink1,$query_1);
				if(mysqli_affected_rows($connectionlink1) > 0) {
					while($row_1 = mysqli_fetch_assoc($q_1)) {
						$mapped_stations = $mapped_stations ." ".$row_1['SiteName'].",";
					}
				}
				$row['MappedStations'] = $mapped_stations;
				//End get linked Stations


                //$row2  = array($row['FacilityName'],$row['BinName'],$row['parttype'],$row['LocationName'],$row['Room'],$row['disposition'],$row['Status']);
                //$row2  = array($row['FacilityName'],$row['BinName'],$row['parttype'],$row['MappedStations'],$row['disposition'],$row['Status']);
                $row2  = array($row['FacilityName'],$row['BinName'],$row['ParentBinName'],$row['MobilityName'],$row['BinType'],$row['MappedStations'],$row['disposition'],$row['Status']);
                $rows[] = $row2;
            }

$sheet_name = 'Bin List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 8);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 