<div class="row page" data-ng-controller="speedtracking">
    <!--SN life cycles side nav start-->
    <md-sidenav class="md-sidenav-right" md-component-id="closeEventsDisabled"
    md-whiteframe="4" md-disable-close-events>
        <md-toolbar class="md-table-toolbar md-default">
            <div class="md-toolbar-tools">
                <span class="text-warning">Media SN Results</span>
                <div flex></div>
                <md-icon class="excel_icon" md-svg-src="../assets/images/excel.svg" ng-click="GetSerialExcel(assetcompletedetails.MediaSerialNumber)"></md-icon>
                <i class="material-icons text-danger" ng-click="toggleSidenav()">close</i>                       
            </div>
        </md-toolbar>
        <md-content layout-margin="">
            <form>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>SN</label>
                        <p class="form-control-static">{{assetcompletedetails.MediaSerialNumber}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Container ID</label>
                        <p class="form-control-static">{{assetcompletedetails.idPallet}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>MPN</label>
                        <p class="form-control-static">{{assetcompletedetails.MediaMPN}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Disposition</label>
                        <p class="form-control-static">{{assetcompletedetails.disposition}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Type</label>
                        <p class="form-control-static">{{assetcompletedetails.MediaType}}</p>
                    </div>
                </div>

                <!-- <div class="col-md-4">
                    <div class="form-group">
                        <label>Business Rule ID</label>
                        <p class="form-control-static">
                            <span ng-show="assetcompletedetails.rule_name != ''">{{assetcompletedetails.rule_name}}</span>
                            <span ng-show="assetcompletedetails.rule_name == '' || assetcompletedetails.rule_name == null">Disposition Auto Assigned</span>
                        </p>
                    </div>
                </div> -->
            </form>
            <div style="clear: both;"></div>

            <div >
                <md-tabs md-dynamic-height md-border-bottom class="md-litegrey" md-selected="SelectedTabIndex">
                    <md-tab label="Life Cycle">
                        <!-- <h5 class="text-success" style="margin-top: 22px;">Life Cycle</h5> -->
                        <md-table-container>
                            <table md-table class="table">
                                <thead md-head>
                                    <tr md-row class="bg-grey">                                 
                                        <th md-column>Action</th>
                                        <th md-column style="min-width:220px;">Modified Date</th>    
                                        <th md-column>Modified By</th>                                                                                                                        
                                    </tr>
                                </thead>
                                <tbody md-body>
                                    <tr md-row ng-repeat="itemtrack in assetcompletetracking">
                                        <td md-cell>{{itemtrack.Action}}</td>
                                        <td md-cell>{{itemtrack.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                        <td md-cell>{{itemtrack.FirstName}}{{itemtrack.LastName}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </md-table-container>
                    </md-tab>
                    <md-tab label="SNS Events" ng-click="GetMediaSNSDetails(assetcompletedetails)">
                        <md-table-container>
                            <table md-table class="table">
                                <thead md-head>
                                    <tr md-row class="bg-grey">                                 
                                        <th md-column>Type</th>
                                        <th md-column>MessageID</th>    
                                        <th md-column>Result</th>
                                        <th md-column>Date</th>                                                                                                                        
                                    </tr>
                                </thead>
                                <tbody md-body>
                                    <tr md-row ng-repeat="itemtrack in assetcompletedetails.SNSTracking">
                                        <td md-cell>{{itemtrack.event_type}}</td>
                                        <td md-cell>{{itemtrack.MessageID}}</td>
                                        <td md-cell>{{itemtrack.SNSResult}}</td>
                                        <td md-cell>{{itemtrack.CreatedDate}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </md-table-container>
                    </md-tab>
                </md-tabs>
            </div>            

        </md-content>
    </md-sidenav>
    <!--SN life cycles side nav end-->


    <!--Server life cycles side nav start-->
    <md-sidenav class="md-sidenav-right" md-component-id="closeEventsDisabled4"
    md-whiteframe="4" md-disable-close-events>
        <md-toolbar class="md-table-toolbar md-default">
            <div class="md-toolbar-tools">
                <span class="text-warning">Host Asset ID Results</span>
                <div flex></div>
                <md-icon class="excel_icon" md-svg-src="../assets/images/excel.svg" ng-click="GetServerExcel(ServerCompleteDetails.ServerSerialNumber)"></md-icon>
                <i class="material-icons text-danger" ng-click="toggleSidenav4()">close</i>                       
            </div>
        </md-toolbar>
        <md-content layout-margin="">
            <form>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Host Asset ID</label>
                        <p class="form-control-static">{{ServerCompleteDetails.ServerSerialNumber}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Rack ID</label>
                        <p class="form-control-static">{{ServerCompleteDetails.idPallet}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>MPN</label>
                        <p class="form-control-static">{{ServerCompleteDetails.MPN}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Disposition</label>
                        <p class="form-control-static">{{ServerCompleteDetails.disposition}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Type</label>
                        <p class="form-control-static">{{ServerCompleteDetails.Type}}</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label>Current BIN</label>
                        <p class="form-control-static">{{ServerCompleteDetails.BinName}}</p>
                    </div>
                </div>

                <!-- <div class="col-md-4">
                    <div class="form-group">
                        <label>Business Rule ID</label>
                        <p class="form-control-static">
                            <span ng-show="ServerCompleteDetails.rule_name != ''">{{ServerCompleteDetails.rule_name}}</span>
                            <span ng-show="ServerCompleteDetails.rule_name == '' || ServerCompleteDetails.rule_name == null">Disposition Auto Assigned</span>
                        </p>
                    </div>
                </div> -->
            </form>
            <div style="clear: both;"></div>            

            <div >
                <md-tabs md-dynamic-height md-border-bottom class="md-litegrey" md-selected="SelectedTabIndex">
                    <md-tab label="Life Cycle">

                        <md-table-container>
                            <table md-table class="table">
                                <thead md-head>
                                    <tr md-row class="bg-grey">                                 
                                        <th md-column>Action</th>
                                        <th md-column style="min-width:220px;">Modified Date</th>    
                                        <th md-column>Modified By</th>                                                                                                                        
                                    </tr>
                                </thead>
                                <tbody md-body>
                                    <tr md-row ng-repeat="itemtrack in ServerLifeCycle">
                                        <td md-cell>{{itemtrack.Action}}</td>
                                        <td md-cell>{{itemtrack.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                        <td md-cell>{{itemtrack.FirstName}}{{itemtrack.LastName}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </md-table-container>
                    </md-tab>

                    <md-tab label="SNS Events" ng-click="GetServerSNSDetails(ServerCompleteDetails)">
                        <md-table-container>
                            <table md-table class="table">
                                <thead md-head>
                                    <tr md-row class="bg-grey">                                 
                                        <th md-column>Type</th>
                                        <th md-column>MessageID</th>    
                                        <th md-column>Result</th>
                                        <th md-column>Date</th>                                                                                                                        
                                    </tr>
                                </thead>
                                <tbody md-body>
                                    <tr md-row ng-repeat="itemtrack in ServerCompleteDetails.SNSTracking">
                                        <td md-cell>{{itemtrack.event_type}}</td>
                                        <td md-cell>{{itemtrack.MessageID}}</td>
                                        <td md-cell>{{itemtrack.SNSResult}}</td>
                                        <td md-cell>{{itemtrack.CreatedDate}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </md-table-container>
                    </md-tab>

                </md-tabs>
            </div>
        </md-content>
    </md-sidenav>
    <!--Server life cycles side nav end-->

    <!--Container life cycles side nav start-->
    <md-sidenav class="md-sidenav-right" md-component-id="closeEventsDisabled3"
    md-whiteframe="4" md-disable-close-events>
        <md-toolbar class="md-table-toolbar md-default">
            <div class="md-toolbar-tools">
                <span class="text-warning">Container Results</span>
                <div flex></div>
                <md-icon class="excel_icon" md-svg-src="../assets/images/excel.svg" ng-click="GetContainerExcel(palletcompletedetails.idPallet)"></md-icon>
                <i class="material-icons text-danger" ng-click="toggleSidenav3()">close</i>                       
            </div>
        </md-toolbar>
        <md-content layout-margin="">
            <form>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Container ID</label>
                        <p class="form-control-static">{{palletcompletedetails.idPallet}}</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label>Source</label>
                        <p class="form-control-static">{{palletcompletedetails.CustomerName}}</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label>Seal ID</label>
                        <p class="form-control-static">	{{palletcompletedetails.SealNo1}}--{{palletcompletedetails.SealNo2}}--{{palletcompletedetails.SealNo3}}--{{palletcompletedetails.SealNo4}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Location</label>
                        <p class="form-control-static">{{palletcompletedetails.LocationName}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Received Date</label>
                        <p class="form-control-static">	{{palletcompletedetails.ReceivedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</p>
                    </div>
                </div>
            </form>
            <div style="clear: both;"></div>
            <h5 class="text-success" style="margin-top: 22px;">Life Cycle</h5>
            <md-table-container>
                <table md-table class="table">
                    <thead md-head>
                        <tr md-row class="bg-grey">                                 
                            <th md-column>Action</th>
                            <th md-column style="min-width:220px;">Modified Date</th>    
                            <th md-column>Modified  By</th>                                                                                                                        
                        </tr>
                    </thead>
                    <tbody md-body>
                        <tr md-row ng-repeat="itemtrack in palletcompletetracking">
                            <td md-cell>{{itemtrack.Action}}</td>
                            <td md-cell>{{itemtrack.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                            <td md-cell>{{itemtrack.FirstName}}{{itemtrack.LastName}}</td>
                        </tr>
                    </tbody>
                </table>
            </md-table-container>
        </md-content>
    </md-sidenav>
    <!--Container life cycles side nav end-->




    <md-sidenav class="md-sidenav-right" md-component-id="closeEventsDisabled14"
    md-whiteframe="4" md-disable-close-events>
        <md-toolbar class="md-table-toolbar md-default">
            <div class="md-toolbar-tools">
                <span class="text-warning">SN Results</span>
                <div flex></div>
                <md-icon class="excel_icon" md-svg-src="../assets/images/excel.svg" ng-click="GetSerialExcel(assetcompletedetails.SerialNumber,assetcompletedetails.AssetScanID)"></md-icon>
                <i class="material-icons text-danger" ng-click="toggleSidenav14()">close</i>                       
            </div>
        </md-toolbar>
        <md-content layout-margin="">
            <form>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>SN</label>
                        <p class="form-control-static">{{assetcompletedetails.SerialNumber}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Container ID</label>
                        <p class="form-control-static">{{assetcompletedetails.idPallet}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>MPN</label>
                        <p class="form-control-static">{{assetcompletedetails.UniversalModelNumber}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>disposition</label>
                        <p class="form-control-static">{{assetcompletedetails.disposition}}</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label>Sanitization seal ID</label>
                        <p class="form-control-static">{{assetcompletedetails.sanitization_seal_id}}</p>
                    </div>
                </div>

                <!-- <div class="col-md-4">
                    <div class="form-group">
                        <label>Business Rule ID</label>
                        <p class="form-control-static">
                            <span ng-show="assetcompletedetails.rule_name != ''">{{assetcompletedetails.rule_name}}</span>
                            <span ng-show="assetcompletedetails.rule_name == '' || assetcompletedetails.rule_name == null">Disposition Auto Assigned</span>
                        </p>
                    </div>
                </div> -->
            </form>
            <div style="clear: both;"></div>
            <h5 class="text-success" style="margin-top: 22px;">Life Cycle</h5>
            <md-table-container>
                <table md-table class="table">
                    <thead md-head>
                        <tr md-row class="bg-grey">                                 
                            <th md-column>Action</th>
                            <th md-column style="min-width:220px;">Modified Date</th>    
                            <th md-column>Modified By</th>                                                                                                                        
                        </tr>
                    </thead>
                    <tbody md-body>
                        <tr md-row ng-repeat="itemtrack in assetcompletetracking">
                            <td md-cell>{{itemtrack.Action}}</td>
                            <td md-cell>{{itemtrack.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                            <td md-cell>{{itemtrack.FirstName}}{{itemtrack.LastName}}</td>
                        </tr>
                    </tbody>
                </table>
            </md-table-container>
        </md-content>
    </md-sidenav>


    <div class="col-md-12">
        <article class="article">
            <md-card class="no-margin-h"> 
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>SPEED Tracking</span>
                        <div flex></div>
                            <a ng-click="ExportTrackRecords(item.idPallet)" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                <md-icon class="excel_icon mr-5" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                            </a>                          
                    </div>
                </md-toolbar>
                <div class="row">
                    <form>
                        <div class="col-md-12">

                            <div class="col-md-4">
                                <md-input-container class="md-block includedsearch">
                                    <label>SPEED Rack ID/ Host SN / Media SN</label>
                                    <input name="SpeedSN" ng-model="SpeedSN" ng-enter="GetSpeedSNType()" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="Speed SN" ng-disabled="!SpeedSN">
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="GetSpeedSNType()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>

                            <div class="col-md-4" style="display: none;">
                                <md-input-container class="md-block includedsearch">
                                    <label>Media SN</label>
                                    <input name="SerialIDtracking" ng-model="SerialIDtracking" ng-enter="GetAssetDetails()" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="SerialIDtracking" ng-disabled="!SerialIDtracking">
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="GetAssetDetails()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>
                            <!-- <div class="col-md-3">
                                <md-input-container class="md-block includedsearch">
                                    <label>Inbound Ticket ID</label>
                                    <input name="TicketIDtracking" ng-model="TicketIDtracking" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="TicketIDtracking" ng-disabled="!TicketIDtracking">
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="GetTicketDetails()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div> -->

                            <div class="col-md-4" style="display: none;">
                                <md-input-container class="md-block includedsearch">
                                    <label>Host Asset ID</label>
                                    <input name="ServerSerialNumber" ng-model="ServerSerialNumber" ng-enter="GerServerDetails()" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="ServerSerialNumber" ng-disabled="!ServerSerialNumber">
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="GerServerDetails()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>

                            <div class="col-md-4" style="display: none;">
                                <md-input-container class="md-block includedsearch">
                                    <label>Rack ID</label>
                                    <input name="idPallet" ng-model="idPallet" ng-enter="GetContainerDetails()" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="idPallet" ng-disabled="!idPallet">
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="GetContainerDetails()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>
                        </div>
                    </form>
                </div>
            </md-card>


            <md-card class="no-margin-h" ng-if="assetssearchresult.length >0">
                <md-card-content>
                    <h5>Media SN Tracking</h5>
                    <md-table-container>
                        <table md-table class="table mb-0">
                            <thead md-head>
                                <tr md-row>
                                    <th md-column>Action</th>
                                    <th md-column>SN</th> 
                                    <th md-column>Rack ID</th>
                                    <th md-column>Host Asset ID</th>
                                    <th md-column>MPN</th>
                                    <th md-column>Disposition</th>
                                    <th md-column>Current Status</th>
                                    <th md-column>Current BIN</th>
                                </tr>
                            </thead>
                            <tbody md-body>
                                <tr md-row ng-repeat="item in assetssearchresult">
                                    <td md-cell class="actionicons dialog-demo-content" style="width: 40px; min-width:40px;">
                                        <i ng-click="toggleSidenav();GetserialDetails(item)" class="material-icons open">open_in_new</i>
                                    </td>
                                    <td md-cell>{{item.MediaSerialNumber}}</td>
                                    <td md-cell>{{item.idPallet}}</td>
                                    <td md-cell>{{item.ServerSerialNumber}}</td>
                                    <td md-cell>{{item.MediaMPN}}</td>
                                    <td md-cell>{{item.disposition}}</td>
                                    <td md-cell>{{item.Status}}</td>
                                    <td md-cell>{{item.BinName}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </md-table-container>
                </md-card-content>
            </md-card>

            <md-card class="no-margin-h" ng-if="ServerTracking.ServerID" ng-init="ServerTracking.showDetails = false;">
                <md-card-content>
                    <h5>Host Asset ID Tracking</h5>
                    <md-table-container>
                        <table md-table class="table mb-0 multi-tables">
                            <thead md-head>
                                <tr md-row>
                                    <th md-column>Action</th>
                                    <th md-column>Host Asset ID</th> 
                                    <th md-column>Rack ID</th>
                                    <th md-column>Type</th>
                                    <th md-column>MPN</th>
                                    <th md-column>Disposition</th>
                                    <th md-column>Current Status</th>
                                    <th md-column>Current BIN</th>
                                </tr>
                            </thead>
                            <tbody md-body>
                                <tr >
                                    <td md-cell class="actionicons dialog-demo-content" style="width: 72px; min-width: 72px;">                                        

                                        <i class="material-icons add text-warning" ng-click="ServerTracking.showDetails = !ServerTracking.showDetails" ng-show="ServerTracking.showDetails">remove</i>
                                        <i class="material-icons add text-success" ng-click="ServerTracking.showDetails = !ServerTracking.showDetails;GetSeverMedia(ServerTracking)" ng-show="! ServerTracking.showDetails">add</i>
                                        <i ng-click="toggleSidenav4();GetServerLifeCycle(ServerTracking)" class="material-icons open">open_in_new</i>

                                    </td>
                                    <td md-cell>{{ServerTracking.ServerSerialNumber}}</td>
                                    <td md-cell>{{ServerTracking.idPallet}}</td>
                                    <td md-cell>{{ServerTracking.Type}}</td>
                                    <td md-cell>{{ServerTracking.MPN}}</td>
                                    <td md-cell>{{ServerTracking.disposition}}</td>
                                    <td md-cell>{{ServerTracking.Status}}</td>
                                    <td md-cell>{{ServerTracking.BinName}}</td>
                                </tr>
                                <tr ng-show="ServerTracking.showDetails">
                                    <td></td>
                                    <td colspan="7">
                                        <md-table-container ng-show="ServerTracking.ServerMedia.length > 0">
                                            <table md-table class="table mb-0">
                                                <thead md-head>
                                                    <tr md-row>
                                                        <th md-column>Action</th>
                                                        <th md-column>SN</th> 
                                                        <th md-column>Rack ID</th>
                                                        <th md-column>Host Asset ID</th>
                                                        <th md-column>MPN</th>
                                                        <th md-column>Disposition</th>
                                                        <th md-column>Current Status</th>
                                                        <th md-column>Current BIN</th>
                                                    </tr>
                                                </thead>
                                                <tbody md-body>
                                                    <tr md-row ng-repeat="item in ServerTracking.ServerMedia">
                                                        <td md-cell class="actionicons dialog-demo-content" style="width: 72px; min-width: 72px;">
                                                            <i ng-click="toggleSidenav();GetserialDetails(item)" class="material-icons open">open_in_new</i>
                                                        </td>
                                                        <td md-cell>{{item.MediaSerialNumber}}</td>
                                                        <td md-cell>{{item.idPallet}}</td>
                                                        <td md-cell>{{item.ServerSerialNumber}}</td>
                                                        <td md-cell>{{item.MediaMPN}}</td>
                                                        <td md-cell>{{item.disposition}}</td>
                                                        <td md-cell>{{item.Status}}</td>
                                                        <td md-cell>{{item.BinName}}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </md-table-container>                                        
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </md-table-container>
                </md-card-content>
            </md-card>
            
            
            <md-card class="no-margin-h" ng-if="containerpalletdetails1.length >0">
                <md-card-content>
                    <h5>Rack ID Tracking</h5>
                    <md-table-container>
                        <table md-table class="table mb-0 multi-tables">
                            <thead md-head>
                                <tr md-row>
                                    <th md-column>Action</th>
                                    <th md-column>Rack ID</th>
                                    <th md-column>Source</th>
                                    <th md-column>Ticket ID</th>
                                    <!-- <th md-column>Seal ID</th> -->
                                    <th md-column>Status</th>
                                    <th md-column>Location</th>
                                    <th md-column>Received Date</th>                                                                                                                                                   
                                </tr>
                            </thead>
                            <tbody md-body ng-repeat="item in containerpalletdetails1">
                                <tr md-row >
                                    <td md-cell class="actionicons" style="width: 72px; min-width: 72px;">  
                                        <i class="material-icons add text-warning" ng-click="item.showDetails = !item.showDetails" ng-show="item.showDetails">remove</i>
                                        <i class="material-icons add text-success" ng-click="item.showDetails = !item.showDetails;GetPalletAssetDetails(item.idPallet,item)" ng-show="! item.showDetails">add</i>                                           
                                        <i ng-click="toggleSidenav3();GetLoadPalletDetails1(item)" class="material-icons open">open_in_new</i>
                                    </td>
                                    <td md-cell>{{item.idPallet}}</td>
                                    <td md-cell>{{item.CustomerName}}</td>
                                    <td md-cell>{{item.LoadId}}</td>
                                    <!-- <td md-cell>{{item.SealNo1}}</td> -->
                                    <td md-cell>{{item.StatusValue}}</td>
                                    <td md-cell>{{item.LocationName}}</td>
                                    <td md-cell>{{item.ReceivedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                </tr>

                                <tr ng-show="item.showDetails">
                                    <td></td>
                                    <td colspan="6">

                                        <md-table-container>
                                            <table md-table class="table mb-0">
                                                <thead md-head>
                                                    <tr md-row>
                                                        <th md-column>Action</th>
                                                        <th md-column>Host Asset ID</th> 
                                                        <th md-column>Rack ID</th>
                                                        <th md-column>Type</th>
                                                        <th md-column>MPN</th>
                                                        <th md-column>Disposition</th>
                                                        <th md-column>Current Status</th>
                                                        <th md-column>Current BIN</th>
                                                    </tr>
                                                </thead>
                                                <tbody md-body ng-repeat="server in item.Servers" ng-init = "server.showDetails = false;">
                                                    <tr ng-show="server.ItemType != 'Asset'">
                                                        <td md-cell class="actionicons dialog-demo-content" style="width: 72px; min-width: 72px;">                                                            
                    
                                                            <i class="material-icons add text-warning" ng-click="server.showDetails = !server.showDetails" ng-show="server.showDetails">remove</i>
                                                            <i class="material-icons add text-success" ng-click="server.showDetails = !server.showDetails;GetSeverMedia(server)" ng-show="! server.showDetails">add</i>
                                                            <i ng-click="toggleSidenav4();GetServerLifeCycle(server)" class="material-icons open">open_in_new</i>
                    
                                                        </td>
                                                        <td md-cell>{{server.ServerSerialNumber}}</td>
                                                        <td md-cell>{{server.idPallet}}</td>
                                                        <td md-cell>{{server.Type}}</td>
                                                        <td md-cell>{{server.MPN}}</td>
                                                        <td md-cell>{{server.disposition}}</td>
                                                        <td md-cell>{{server.Status}}</td>
                                                        <td md-cell>{{server.BinName}}</td>
                                                    </tr>

                                                    <tr ng-show="server.ItemType == 'Asset'">
                                                        <td md-cell class="actionicons dialog-demo-content" style="width: 72px; min-width: 72px;">                                                            
                    
                                                            <i class="material-icons add text-warning" ng-click="server.showDetails = !server.showDetails" ng-show="server.showDetails">remove</i>
                                                            <i class="material-icons add text-success" ng-click="server.showDetails = !server.showDetails;GetNextLevelRecovery(server.SerialNumber,server)" ng-show="! server.showDetails">add</i>
                                                            <i ng-click="toggleSidenav14();GetserialDetails14(server.AssetScanID)" class="material-icons open">open_in_new</i>                                                            
                    
                                                        </td>
                                                        <td md-cell>{{server.SerialNumber}}</td>
                                                        <td md-cell>{{server.idPallet}}</td>
                                                        <td md-cell>{{server.part_type}}</td>
                                                        <td md-cell>{{server.UniversalModelNumber}}</td>
                                                        <td md-cell>{{server.disposition}}</td>
                                                        <td md-cell>{{server.Status}}</td>
                                                        <td md-cell>{{server.BinName}}</td>
                                                    </tr>
                                                    <tr ng-show="server.showDetails">
                                                        <td></td>
                                                        <td colspan="7">
                                                            <md-table-container ng-show="server.ServerMedia.length > 0">
                                                                <table md-table class="table mb-0">
                                                                    <thead md-head>
                                                                        <tr md-row>
                                                                            <th md-column style="width: 72px; min-width: 72px;">Action</th>
                                                                            <th md-column>SN</th> 
                                                                            <th md-column>Rack ID</th>
                                                                            <th md-column>Host Asset ID</th>
                                                                            <th md-column>MPN</th>
                                                                            <th md-column>Disposition</th>
                                                                            <th md-column>Current Status</th>
                                                                            <th md-column>Current BIN</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody md-body>
                                                                        <tr md-row ng-repeat="item in server.ServerMedia">
                                                                            <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                                                                                <i ng-click="toggleSidenav();GetserialDetails(item)" class="material-icons open">open_in_new</i>
                                                                            </td>
                                                                            <td md-cell>{{item.MediaSerialNumber}}</td>
                                                                            <td md-cell>{{item.idPallet}}</td>
                                                                            <td md-cell>{{item.ServerSerialNumber}}</td>
                                                                            <td md-cell>{{item.MediaMPN}}</td>
                                                                            <td md-cell>{{item.disposition}}</td>
                                                                            <td md-cell>{{item.Status}}</td>
                                                                            <td md-cell>{{item.BinName}}</td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </md-table-container>                                        
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </md-table-container>                                        
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </md-table-container>
                </md-card-content>
            </md-card>



            <md-card class="no-margin-h" ng-if="AssetTracking.length >0">
                <md-card-content>
                    <md-table-container>
                        <table md-table class="table mb-0 multi-tables">
                            <thead md-head>
                                <tr md-row>
                                    <th md-column style="width: 60px; min-width: 60px;">Action</th>
                                    <th md-column>SN</th> 
                                    <th md-column>Rack ID</th>
                                    <th md-column>MPN</th>
                                    <th md-column>Disposition</th>
                                    <th md-column>Sanitization Seal ID</th>
                                </tr>
                            </thead>
                            <tbody md-body ng-repeat="asset in AssetTracking">
                                <tr md-row>
                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">

                                        <i class="material-icons add text-warning" ng-click="asset.showDetails = !asset.showDetails" ng-show="asset.showDetails">remove</i>
                                        <i class="material-icons add text-success" ng-click="asset.showDetails = !asset.showDetails;GetNextLevelRecovery(asset.SerialNumber,asset)" ng-show="! asset.showDetails">add</i>                                           
                                        <i ng-show= "asset.AssetScanID" ng-click="toggleSidenav14();GetserialDetails14(asset.AssetScanID)" class="material-icons open">open_in_new</i>
                                        <i ng-show="asset.ServerID" ng-click="toggleSidenav11();GetServerLifeCycle(asset)" class="material-icons open">open_in_new</i>

                                    </td>
                                    <td md-cell>{{asset.SerialNumber}}</td>
                                    <td md-cell>{{asset.idPallet}}</td>
                                    <td md-cell>{{asset.UniversalModelNumber}}</td>
                                    <td md-cell>{{asset.disposition}}</td>
                                    <td md-cell>{{asset.Recoverytype}}</td>
                                    <td md-cell>{{asset.sanitization_seal_id}}</td>
                                </tr>
                                <tr ng-show="asset.showDetails">
                                    <td colspan="7">
                                        <div ng-show="asset.NewLevel.length == 0">No Serials Available</div>
                                        <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;" ng-show="asset.NewLevel.length > 0">
                                            <thead md-head>
                                                <tr md-row>
                                                    <th md-column>Action</th>
                                                    <th md-column>SN</th> 
                                                    <th md-column>Part Type</th>
                                                    <th md-column>MPN</th>
                                                    <th md-column>Disposition</th>
                                                    <th md-column>Recovery Type</th>                                                                                                                          
                                                </tr>
                                            </thead>
                                            <tbody md-body ng-repeat="asset1 in asset.NewLevel">
                                                <tr md-row>
                                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">

                                                        <i class="material-icons add text-warning" ng-click="asset1.showDetails = !asset1.showDetails" ng-show="asset1.showDetails">remove</i>
                                                        <i class="material-icons add text-success" ng-click="asset1.showDetails = !asset1.showDetails;GetNextLevelRecovery(asset1.SerialNumber,asset1)" ng-show="! asset1.showDetails">add</i>                                           
                                                        <i ng-show="asset1.AssetScanID" ng-click="toggleSidenav14();GetserialDetails14(asset.AssetScanID)" class="material-icons open">open_in_new</i>
                                                        <i ng-show="asset1.MediaID" ng-click="toggleSidenav10();GetMediaLifeCycle(asset1)" class="material-icons open">open_in_new</i>

                                                    </td>
                                                    <td md-cell>{{asset1.SerialNumber}}</td>
                                                    <td md-cell>{{asset1.parttype}}</td>
                                                    <td md-cell>{{asset1.UniversalModelNumber}}</td>
                                                    <td md-cell>{{asset1.disposition}}</td>
                                                    <td md-cell>{{asset1.Recoverytype}}</td>
                                                </tr>
                                                <tr ng-show="asset1.showDetails">
                                                    <td colspan="6">
                                                        <div ng-show="asset1.NewLevel.length == 0">No Serials Available</div>
                                                        <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;" ng-show="asset1.NewLevel.length > 0">
                                                            <thead md-head>
                                                                <tr md-row>
                                                                    <th md-column>Action</th>
                                                                    <th md-column>SN</th> 
                                                                    <th md-column>Part Type</th>
                                                                    <th md-column>MPN</th>
                                                                    <th md-column>Disposition</th>
                                                                    <th md-column>Recovery Type</th>                                                                                                                          
                                                                </tr>
                                                            </thead>
                                                            <tbody md-body ng-repeat="asset2 in asset1.NewLevel">
                                                                <tr md-row>
                                                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                
                                                                        <!-- <i class="material-icons add text-warning" ng-click="asset2.showDetails = !asset2.showDetails" ng-show="asset2.showDetails">remove</i>
                                                                        <i class="material-icons add text-success" ng-click="asset2.showDetails = !asset2.showDetails;GetNextLevelRecovery(asset2.SerialNumber,asset2)" ng-show="! asset2.showDetails">add</i>                                            -->
                                                                        <i ng-show="asset2.AssetScanID" ng-click="toggleSidenav14();GetserialDetails14(asset2.AssetScanID)" class="material-icons open">open_in_new</i>
                                                                        <i ng-show="asset2.MediaID" ng-click="toggleSidenav10();GetMediaLifeCycle(asset2)" class="material-icons open">open_in_new</i>
                
                                                                    </td>
                                                                    <td md-cell>{{asset2.SerialNumber}}</td>
                                                                    <td md-cell>{{asset2.parttype}}</td>
                                                                    <td md-cell>{{asset2.UniversalModelNumber}}</td>
                                                                    <td md-cell>{{asset2.disposition}}</td>
                                                                    <td md-cell>{{asset2.Recoverytype}}</td>
                                                                </tr>
                                                                <tr ng-show="asset2.showDetails">
                                                                    <td colspan="6">
                                                                        
                                                                        
                                                                        
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>

                                        
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </md-table-container>
                </md-card-content>
            </md-card>


        </article>        
    </div>
</div>