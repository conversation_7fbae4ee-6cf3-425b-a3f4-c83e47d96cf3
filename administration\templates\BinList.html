<div ng-controller = "BinList" class="page">
    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="BinList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">    
                                 <i ng-click="BinList = !BinList" class="material-icons md-primary" ng-show="BinList">keyboard_arrow_up</i>
                                <i ng-click="BinList = !BinList" class="material-icons md-primary" ng-show="! BinList">keyboard_arrow_down</i>
                                <span ng-click="BinList = !BinList">Bin List</span>
                                <div flex></div>

                                 <a href="#!/BinList" ng-click="BinListxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                                    <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>
                                <div class="upload-btn-wrapper text-center mt-5">
                                    <button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right;"><i class="material-icons mr-5" style="margin-top: 2px;">file_upload</i>Upload File</button>                           
                                    <input type="file" ng-file-select="onFileSelect($files)" id="BinFile">  
                                    <a href="../../sample_files/upload_Bin_File.xlsx" target="_blank" class="md-button btn-w-md text-warning mr-5" style="float: right; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a> 
                                </div> 

                                <a href="#!/BinList" ng-click="BinListxls()" class="md-button md-raised md-default dis_open_v mr-5" style="display:none; min-width: 40px;">
                                    <md-icon class="mt-10 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon>
                                </a>
                                 
                                <a href="#!/Bin" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Bin
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="BinList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>
                                    <div class="table-responsive" style="overflow: auto;">                                                                                                                                                                            
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>
                                                    <th style="min-width: 40px;">Print</th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">
                                                        <div>                               
                                                            Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                 
                                                            <span ng-show="OrderBy == 'FacilityName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                                                                                                            
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('BinName')" ng-class="{'orderby' : OrderBy == 'BinName'}">
                                                        <div>
                                                            Bin Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'BinName'"></i>
                                                            <span ng-show="OrderBy == 'BinName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ParentBinName')" ng-class="{'orderby' : OrderBy == 'ParentBinName'}">
                                                        <div>
                                                            Parent Bin <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ParentBinName'"></i>
                                                            <span ng-show="OrderBy == 'ParentBinName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('MobilityName')" ng-class="{'orderby' : OrderBy == 'MobilityName'}">
                                                        <div>
                                                            Mobility Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'MobilityName'"></i>
                                                            <span ng-show="OrderBy == 'MobilityName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('BinType')" ng-class="{'orderby' : OrderBy == 'BinType'}">                           
                                                        <div>                               
                                                            Bin Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'BinType'"></i>                                    
                                                            <span ng-show="OrderBy == 'BinType'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->

                                                    <th style="cursor:pointer; min-width: 270px;" ng-click="MakeOrderBy('GroupName')" ng-class="{'orderby' : OrderBy == 'GroupName'}">                           
                                                        <div>                               
                                                            Location Group <i class="fa fa-sort pull-right" ng-show="OrderBy != 'GroupName'"></i>                                    
                                                            <span ng-show="OrderBy == 'GroupName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
    
                                                    <th style="cursor:pointer; min-width: 270px;" ng-click="MakeOrderBy('LocationName')" ng-class="{'orderby' : OrderBy == 'LocationName'}">                           
                                                        <div>                               
                                                            Location <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationName'"></i>                                    
                                                            <span ng-show="OrderBy == 'LocationName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th>                           
                                                        <div>                                                            	
                                                            Qty x Part Type                                                                                                                                                       
                                                        </div>                                                                                  
                                                    </th>

                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('LocationName')" ng-class="{'orderby' : OrderBy == 'LocationName'}">                           
                                                        <div>                               
                                                            Location <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationName'"></i>                                    
                                                            <span ng-show="OrderBy == 'LocationName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->

                                                   <!--  <th style="cursor:pointer;" ng-click="MakeOrderBy('workflow')" ng-class="{'orderby' : OrderBy == 'workflow'}">                          
                                                        <div>                               
                                                            workflow <i class="fa fa-sort pull-right" ng-show="OrderBy != 'workflow'"></i>                                  
                                                            <span ng-show="OrderBy == 'workflow'">                                  
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->
                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('Room')" ng-class="{'orderby' : OrderBy == 'Room'}">                          
                                                        <div>                               
                                                            Room <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Room'"></i>                                    
                                                            <span ng-show="OrderBy == 'Room'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->

                                                    <th>                          
                                                        <div>                               
                                                            Mapped Stations                                                                                                                                                            
                                                        </div>                                                                                  
                                                    </th>
                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">                           
                                                        <div>                               
                                                            Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>                                    
                                                            <span ng-show="OrderBy == 'disposition'">                                   
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> 

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>                                  
                                                            <span ng-show="OrderBy == 'Status'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                </tr>
                                                
                                                <tr class="errornone">  
                                                    <td></td>                      
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }'  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="BinName" ng-model="filter_text[0].BinName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ParentBinName" ng-model="filter_text[0].ParentBinName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="MobilityName" ng-model="filter_text[0].MobilityName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="BinType" ng-model="filter_text[0].BinType" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->

                                                    <td >
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="GroupName" ng-model="filter_text[0].GroupName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }'  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
    
                                                    <td >
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LocationName" ng-model="filter_text[0].LocationName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }'  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td></td>
                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LocationName" ng-model="filter_text[0].LocationName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->
                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="workflow" ng-model="filter_text[0].workflow" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->
                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Room" ng-model="filter_text[0].Room" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->
                                                    <td></td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>                                                                    
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>                       
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/Bin/{{product.CustomPalletID}}"><md-icon class="material-icons text-danger">edit</md-icon></a></td>
                                                    <td>
                                                     <a href="{{host}}label/master/examples/binlabel.php?id={{product.CustomPalletID}}" target="_blank">
                                                        <md-icon class="material-icons action-icons" role="img" aria-label="print">print</md-icon>
                                                    </a>
                                                    </td>
                                                    <td>
                                                        {{product.FacilityName}}                            
                                                    </td> 
                                                    <td>
                                                        {{product.BinName}}
                                                    </td>
                                                    <td>
                                                        {{product.ParentBinName}}
                                                    </td>
                                                    <td>
                                                        {{product.MobilityName}}
                                                    </td>

                                                    <!-- <td>
                                                        {{product.BinType}}                            
                                                    </td> -->

                                                    <td>                                                
                                                        <div class="autocomplete insideuse">
                                                            <md-autocomplete required style="width: 180px;" 
                                                                ng-disabled="product.Received == 0"
                                                                md-no-cache="noCache"    
                                                                md-search-text-change="ContainerLocationChange1(product.group,product)"      
                                                                md-search-text="product.group"                                  
                                                                md-items="item in queryContainerLocationSearch1(product.group,product)"
                                                                md-item-text="item.GroupName"
                                                                md-selected-item-change="selectedContainerLocationChange1(item,product)"
                                                                md-min-length="0"       
                                                                ng-model-options='{ debounce: 1000 }'                             
                                                                placeholder="Search Location Group">
                                                                <md-item-template>
                                                                    <span md-highlight-text="product.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                                </md-item-template>
                                                                <md-not-found>
                                                                    No Records matching "{{product.group}}" were found.                                    
                                                                </md-not-found>
                                                            </md-autocomplete>                                                    
                                                            <button  style="min-width: 40px; min-height: 30px; margin-top: -6px;" class="md-button md-raised md-accent"  type="button" ng-disabled="! product.group" ng-click="UpdateBinLocationGroup(product,$event)">
                                                                <!-- Update -->
    
                                                                <span ng-show="! product.busy">Update</span>
                                                                <span ng-show="product.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
    
                                                            </button>                                                            
                                                        </div>
                                                    </td> 
    
                                                    <td>
                                                        {{product.LocationName}}                                                        
                                                    </td>

                                                    <td>
                                                        <!-- {{product.parttype}}                             -->
                                                        <span ng-click="UpdateBinPartTypeSummary(product)"><md-icon class="material-icons text-danger">refresh</md-icon></a></span>
                                                        {{product.PartTypeSummary}}
                                                    </td>                       
                                                    <!-- <td>
                                                        {{product.LocationName}}                            
                                                    </td>  -->
                                                    <!-- <td>
                                                        {{product.workflow}}
                                                    </td> -->
                                                    <!-- <td>
                                                        {{product.Room}}
                                                    </td> -->
                                                    <td>
                                                        {{product.MappedStations}}
                                                    </td>
                                                    <td>
                                                        {{product.disposition}}
                                                    </td>
                                                    <td>
                                                        {{product.Status}}
                                                    </td>
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="12">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>