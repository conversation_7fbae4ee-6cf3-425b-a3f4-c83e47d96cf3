<?php
	session_start();
	include_once("../database/admin.class.php");
	$obj = new AdminClass();
	if($_POST['ajax'] == "NewProfile"){
		$result = $obj->NewProfile($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "NewUser"){
		$result = $obj->NewUser($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "User"){
		$result = $obj->User($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Customer"){
		$result = $obj->Customer($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetCustomerList"){
  		$result = $obj->GetCustomerList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SelectIndustry"){
		$result = $obj->SelectIndustry();
		echo $result;
	}

	if($_POST['ajax'] == "Account"){
		$result = $obj->Account($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Category"){
		$result = $obj->Category($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "NewProductClass"){
		$result = $obj->NewProductClass($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetLanguages"){
		$result = $obj->GetLanguages();
		echo $result;
	}

	if($_POST['ajax'] == "SetLanguage"){
		$result = $obj->SetLanguage($_POST);
		echo $result;
	}

	if($_GET['ajax'] == "GetCurrentLanguage"){
		$result = $obj->GetCurrentLanguage();
		echo $result;
	}
	if($_POST['ajax'] == "Login"){
  		$result = $obj->Login($_POST);
		echo $result;
	 }

	if($_POST['ajax'] == "GetUsers"){
  		$result = $obj->GetUsers($_POST);
		echo $result;
	 }
	if($_POST['ajax'] == "GenerateUserListxls") {
		$result = $obj->GenerateUserListxls($_POST);
		echo $result;
	}
	 if($_GET['ajax'] == "GetProfiles"){
  		$result = $obj->GetProfiles($_POST);
		echo $result;
	 }
	  if($_GET['ajax'] == "GetAccounts"){
  		$result = $obj->GetAccounts($_POST);
		echo $result;
	 }
	  if($_GET['ajax'] == "GetCustomers"){
  		$result = $obj->GetCustomers($_POST);
		echo $result;
	 }
	  if($_POST['ajax'] == "GetProfileDetails"){
  		$result = $obj->GetProfileDetails($_POST);
		echo $result;
	 }
	  if($_POST['ajax'] == "GetAccountDetails"){
  		$result = $obj->GetAccountDetails($_POST);
		echo $result;
	 }

	  if($_POST['ajax'] == "GetCustomerDetails"){
  		$result = $obj->GetCustomerDetails($_POST);
		echo $result;
	 }

	   if($_POST['ajax'] == "GetUserDetails"){
  		$result = $obj->GetUserDetails($_POST);
		echo $result;
	 }

	  if($_GET['ajax'] == "GetClasses"){
  		$result = $obj->GetClasses($_POST);
		echo $result;
	 }

	  if($_POST['ajax'] == "GetClassDetails"){
  		$result = $obj->GetClassDetails($_POST);
		echo $result;
	 }

	  if($_GET['ajax'] == "GetCategories"){
  		$result = $obj->GetCategories($_POST);
		echo $result;
	 }

	  if($_POST['ajax'] == "GetCategoryDetails"){
  		$result = $obj->GetCategoryDetails($_POST);
		echo $result;
	 }

	 if($_POST['ajax'] == "Attribute"){
		$result = $obj->Attribute($_POST);
		echo $result;
	}

	 if($_GET['ajax'] == "GetAttributes"){
  		$result = $obj->GetAttributes($_POST);
		echo $result;
	 }

	  if($_POST['ajax'] == "GetAttributesDetails"){
  		$result = $obj->GetAttributesDetails($_POST);
		echo $result;
	 }
	 	 if($_GET['ajax'] == "GetRequisitions"){
  		$result = $obj->GetRequisitions($_GET);
		echo $result;
	 }

	 if($_GET['ajax'] == "GetRequests"){
  		$result = $obj->GetRequests($_GET);
		echo $result;
	 }

	  if($_GET['ajax'] == "GetTodos"){
  		$result = $obj->GetTodos($_GET);
		echo $result;
	 }
	 if($_GET['ajax'] == "GetTabs"){
    	$result = $obj->GetTabs($_GET);
  		echo $result;
  	}
	if($_POST['ajax'] == "selectdepartment"){
		$result = $obj->SelectDepartment();
		echo $result;
	}

	if($_POST['ajax'] == "AddAttributeValue"){
		$result = $obj->AddAttributeValue($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAttributeValue"){
		$result = $obj->GetAttributeValue($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteAttributeValue"){
		$result = $obj->DeleteAttributeValue($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAttributeDetails"){
		$result = $obj->GetAttributeDetails($_POST);
		echo $result;
	}

	if($_GET['ajax'] == "GetMatchingAttributes"){
		$result = $obj->GetMatchingAttributes($_GET);
		echo $result;
	}

	if($_POST['ajax'] == "AssignAttribute"){
		$result = $obj->AssignAttribute($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteAttribute"){
		$result = $obj->DeleteAttribute($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Manufacturer"){
		$result = $obj->Manufacturer($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetManufacturersList"){
  		$result = $obj->GetManufacturersList($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetManufacturers"){
		$result = $obj->GetManufacturers($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "selectmanufactures"){
		$result = $obj->selectmanufactures();
		echo $result;
	}
	if($_POST['ajax'] == "GetManufacturersDetails"){
  		$result = $obj->GetManufacturersDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateManufacturerListxls") {
		$result = $obj->GenerateManufacturerListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Workflow"){
		$result = $obj->Workflow($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetWorkflow"){
  		$result = $obj->GetWorkflow($_GET);
		echo $result;
	}
	if($_POST['ajax'] == "GetWorkflow"){
		$result = $obj->GetWorkflow($_POST);
	  	echo $result;
  	}
	if($_POST['ajax'] == "SelectWorkflow"){
		$result = $obj->SelectWorkflow();
		echo $result;
	}
	if($_POST['ajax'] == "GetWorkflowDetails"){
  		$result = $obj->GetWorkflowDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Addinputtype") {
		$result = $obj->Addinputtype($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "Deleteinputtype") {
		$result = $obj->Deleteinputtype($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "Vendor"){
		$result = $obj->Vendor($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetVendors"){
  		$result = $obj->GetVendors($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetVendorsList"){
  		$result = $obj->GetVendorsList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetVendorDetails"){
  		$result = $obj->GetVendorDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetLocations"){
  		$result = $obj->GetLocations($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateLocationListxls") {
		$result = $obj->GenerateLocationListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetLocations1"){
  		$result = $obj->GetLocations1($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "site"){
  		$result = $obj->Site($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetSite"){
  		$result = $obj->GetSite();
		echo $result;
	}
	if($_POST['ajax'] == "GetsiteList"){
  		$result = $obj->GetsiteList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GenerateSiteListxls") {
		$result = $obj->GenerateSiteListxls($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "disposition"){
  		$result = $obj->disposition($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetDispositionDetails"){
  		$result = $obj->GetDispositionDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetDispositionList"){
  		$result = $obj->GetDispositionList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ReferenceCustomer"){
		$result = $obj->ReferenceCustomer($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "ReferenceCustomer1"){
		$result = $obj->ReferenceCustomer1($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetReferenceCustomers"){
  		$result = $obj->GetReferenceCustomers($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetReferenceCustomerDetails"){
  		$result = $obj->GetReferenceCustomerDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCommodities"){
  		$result = $obj->GetCommodities();
		echo $result;
	}

	if($_POST['ajax'] == "AddCommodity"){
  		$result = $obj->AddCommodity($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAssignedCommodities"){
  		$result = $obj->GetAssignedCommodities($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "DeletePart"){
  		$result = $obj->DeletePart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetLeftMenus"){
  		$result = $obj->GetLeftMenus($_POST['TabID']);
		echo $result;
	}

	if($_GET['ajax'] == "GetPickups"){
		$result = $obj->GetPickups();
		echo $result;
	}

	if($_POST['ajax'] == "GetCustomerShares"){
		$result = $obj->GetCustomerShares($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SaveCustomerShare"){
		$result = $obj->SaveCustomerShare($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "FacilityDetails"){
		$result = $obj->FacilityDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "facility"){
		$result = $obj->Facility($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "LocationDetails"){
		$result = $obj->LocationDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "selectfacility"){
		$result = $obj->SelectFacility();
		echo $result;
	}

	if($_POST['ajax'] == "location"){
		$result = $obj->Location($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetFacility"){
		$result = $obj->GetFacility();
		echo $result;
	}
	if($_POST['ajax'] == "GetLocation"){
		$result = $obj->GetLocation($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "NewCustomerType"){
		$result = $obj->NewCustomerType($_POST);
		echo $result;
	}

	if($_GET['ajax'] == "GetCustomerTypes"){
  		$result = $obj->GetCustomerTypes();
		echo $result;
	 }
	   if($_POST['ajax'] == "GetCustomerTypeDetials"){
  		$result = $obj->GetCustomerTypeDetials($_POST);
		echo $result;
	 }

	 if($_POST['ajax'] == "NewPackageType"){
		$result = $obj->NewPackageType($_POST);
		echo $result;
	}


	if($_GET['ajax'] == "GetPackages"){
  		$result = $obj->GetPackages($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "GetPackageTypeDetials"){
  		$result = $obj->GetPackageTypeDetials($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateRecoveryPrice"){
  		$result = $obj->UpdateRecoveryPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "selectlabel"){
		$result = $obj->SelectLabel();
		echo $result;
	}
	if($_POST['ajax'] == "selectlocation"){
		$result = $obj->selectlocation();
		echo $result;
	}
	if($_GET['ajax'] == "GetLabel"){
  		$result = $obj->GetLabel($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "LabelDetails"){
		$result = $obj->LabelDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "label"){
		$result = $obj->Label($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "Getcatlogcreation"){
  		$result = $obj->Getcatlogcreation($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "catlogcreationDetails"){
		$result = $obj->catlogcreationDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "catlogcreation"){
		$result = $obj->catlogcreation($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GeneratecatlogcreationListXLS") {
		$result = $obj->GeneratecatlogcreationListXLS($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SelectDemandType"){
		$result = $obj->SelectDemandType();
		echo $result;
	}
	if($_GET['ajax'] == "GetDemandType"){
  		$result = $obj->GetDemandType();
		echo $result;
	}
	if($_POST['ajax'] == "SelectDisposition"){
		$result = $obj->SelectDisposition();
		echo $result;
	}
	if($_POST['ajax'] == "SelectParentDisposition"){
		$result = $obj->SelectParentDisposition();
		echo $result;
	}
	if($_GET['ajax'] == "GetDisposition"){
  		$result = $obj->GetDisposition();
		echo $result;
	}
	if($_GET['ajax'] == "GetMatchingLocations"){
		$result = $obj->GetMatchingLocations($_GET);
		echo $result;
	}
	if($_POST['ajax'] == "SelectStatus"){
		$result = $obj->SelectStatus();
		echo $result;
	}
	if($_GET['ajax'] == "GetStatus"){
  		$result = $obj->GetStatus();
		echo $result;
	}
	if($_POST['ajax'] == "bincreation"){
		$result = $obj->bincreation($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetBinDetails"){
  		$result = $obj->GetBinDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetBin"){
  		$result = $obj->GetBin($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetMatchingBins"){
  		$result = $obj->GetMatchingBins($_GET);
		echo $result;
	}
	if($_GET['ajax'] == "ValidateParentBin"){
  		$result = $obj->ValidateParentBin($_GET);
		echo $result;
	}
	if($_GET['ajax'] == "GetBinTrackingHistory"){
  		$result = $obj->GetBinTrackingHistory($_GET);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateBinListxls") {
		$result = $obj->GenerateBinListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Selectndsanitizationtype"){
		$result = $obj->Selectndsanitizationtype();
		echo $result;
	}
	if($_GET['ajax'] == "Getsanitizationtype"){
  		$result = $obj->Getsanitizationtype();
		echo $result;
	}

	if($_POST['ajax'] == "Selectcooid"){
		$result = $obj->Selectcooid();
		echo $result;
	}
	if($_GET['ajax'] == "Getcooid"){
  		$result = $obj->Getcooid();
		echo $result;
	}
	if($_POST['ajax'] == "Selectharvesttype"){
		$result = $obj->Selectharvesttype();
		echo $result;
	}
	if($_GET['ajax'] == "Getharvesttype"){
  		$result = $obj->Getharvesttype();
		echo $result;
	}
	if($_POST['ajax'] == "Selectliquidationtype"){
		$result = $obj->Selectliquidationtype();
		echo $result;
	}
	if($_GET['ajax'] == "Getliquidationtype"){
  		$result = $obj->Getliquidationtype();
		echo $result;
	}


	if($_POST['ajax'] == "NewTransportation"){
		$result = $obj->NewTransportation($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetTransportations"){
  		$result = $obj->GetTransportations($_POST);
		echo $result;
	}
     if($_POST['ajax'] == "GetTransportationDetials"){
  		$result = $obj->GetTransportationDetials($_POST);
		echo $result;
	 }

	 if($_POST['ajax'] == "NewMethodErase"){
		$result = $obj->NewMethodErase($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetMethodErase"){
  		$result = $obj->GetMethodErase();
		echo $result;
	}
	if($_POST['ajax'] == "GetMethodErase"){
  		$result = $obj->GetMethodErase();
		echo $result;
	}
     if($_POST['ajax'] == "GetMethodEraseDetails"){
  		$result = $obj->GetMethodEraseDetails($_POST);
		echo $result;
	 }

	 if($_POST['ajax'] == "NewCosmeticGrade"){
		$result = $obj->NewCosmeticGrade($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetCosmeticGrade"){
  		$result = $obj->GetCosmeticGrade($_POST);
		echo $result;
	}
     if($_POST['ajax'] == "GetCosmeticGradeDetails"){
  		$result = $obj->GetCosmeticGradeDetails($_POST);
		echo $result;
	 }
	  if($_POST['ajax'] == "NewCosmeticGradeDesc"){
		$result = $obj->NewCosmeticGradeDesc($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetCosmeticGradeDesc"){
  		$result = $obj->GetCosmeticGradeDesc($_POST);
		echo $result;
	}
     if($_POST['ajax'] == "GetCosmeticGradeDescDetails"){
  		$result = $obj->GetCosmeticGradeDescDetails($_POST);
		echo $result;
	 }

	if($_POST['ajax'] == "NewMissingDesc"){
		$result = $obj->NewMissingDesc($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetMissingDesc"){
  		$result = $obj->GetMissingDesc($_POST);
		echo $result;
	}
     if($_POST['ajax'] == "GetMissingDescDetails"){
  		$result = $obj->GetMissingDescDetails($_POST);
		echo $result;
	 }
	 if($_POST['ajax'] == "NewFailureDesc"){
		$result = $obj->NewFailureDesc($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetFailureDesc"){
  		$result = $obj->GetFailureDesc($_POST);
		echo $result;
	}
     if($_POST['ajax'] == "GetFailureDescDetails"){
  		$result = $obj->GetFailureDescDetails($_POST);
		echo $result;
	 }
	 if($_POST['ajax'] == "NewAssetType"){
		$result = $obj->NewAssetType($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetAssetType"){
  		$result = $obj->GetAssetType($_POST);
		echo $result;
	}
     if($_POST['ajax'] == "GetAssetTypeDetails"){
  		$result = $obj->GetAssetTypeDetails($_POST);
		echo $result;
	 }
	 if($_POST['ajax'] == "NewBOLClass"){
		$result = $obj->NewBOLClass($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetBOLClass"){
  		$result = $obj->GetBOLClass($_POST);
		echo $result;
	}
     if($_POST['ajax'] == "GetBOLClassDetails"){
  		$result = $obj->GetBOLClassDetails($_POST);
		echo $result;
	 }

	if($_POST['ajax'] == "NewBOLType"){
		$result = $obj->NewBOLType($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetBOLType"){
  		$result = $obj->GetBOLType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetBOLTypeDetails"){
		$result = $obj->GetBOLTypeDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetCustomerTypes"){
  		$result = $obj->GetCustomerTypes();
		echo $result;
	}
	if($_POST['ajax'] == "SelectCustomerTypes"){
		$result = $obj->SelectCustomerTypes();
		echo $result;
	}

	if($_GET['ajax'] == "USAStates"){
  		$result = $obj->USAStates();
		echo $result;
	}

	if($_POST['ajax'] == "GetCustomerTargets"){
		$result = $obj->GetCustomerTargets($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SaveCustomerTarget"){
		$result = $obj->SaveCustomerTarget($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCategoryPrice"){
		$result = $obj->GetCategoryPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetCategoryPriceReference"){
		$result = $obj->GetCategoryPriceReference($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveCategoryPrice"){
		$result = $obj->SaveCategoryPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveCategoryPriceReference"){
		$result = $obj->SaveCategoryPriceReference($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UploadCustomerLogo"){
		$result = $obj->UploadCustomerLogo($_FILES);
		echo $result;
	}

	if($_POST['ajax'] == "UploadManagerSignature"){
		$result = $obj->UploadManagerSignature($_FILES);
		echo $result;
	}

	if($_POST['ajax'] == "GetSalesUsers"){
  		$result = $obj->GetSalesUsers();
		echo $result;
	 }

	 if($_POST['ajax'] == "ManageLabel"){
  		$result = $obj->ManageLabel($_POST);
		echo $result;
	 }

	 if($_POST['ajax'] == "GetLabelDetails"){
  		$result = $obj->GetLabelDetails();
		echo $result;
	 }

	 if($_POST['ajax'] == "UploadSiteLogo"){
		$result = $obj->UploadSiteLogo($_FILES);
		echo $result;
	 }

	 if($_POST['ajax'] == "GetLogos"){
		$result = $obj->GetLogos();
		echo $result;
	 }

	 if($_POST['ajax'] == "DeleteSiteLogo"){
		$result = $obj->DeleteSiteLogo();
		echo $result;
	 }

	 if($_POST['ajax'] == "UploadReportLogo"){
		$result = $obj->UploadReportLogo($_FILES);
		echo $result;
	 }

	 if($_POST['ajax'] == "DeleteReportLogo"){
		$result = $obj->DeleteReportLogo();
		echo $result;
	 }

	  if($_POST['ajax'] == "UploadSignatureLogo"){
		$result = $obj->UploadSignatureLogo($_FILES);
		echo $result;
	 }

	 if($_POST['ajax'] == "DeleteSignatureLogo"){
		$result = $obj->DeleteSignatureLogo();
		echo $result;
	 }
	if($_POST['ajax'] == "CommodityCat"){
		$result = $obj->CommodityCat($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetCommodityCats"){
  		$result = $obj->GetCommodityCats($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCommodityCatDetails"){
  		$result = $obj->GetCommodityCatDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetDepartmentDetails"){
  		$result = $obj->GetDepartmentDetails($_POST);
		echo $result;
	 }
	if($_POST['ajax'] == "department"){
		$result = $obj->Department($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSelectedLanguage"){
		$result = $obj->GetSelectedLanguage();
		echo $result;
	}
	if($_GET['ajax'] == "GetStates"){
		$result = $obj->GetStates();
		echo $result;
	}
	if($_POST['ajax'] == "GetStatesInfo"){
  		$result = $obj->GetStatesInfo($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveClassResult"){
  		$result = $obj->SaveClassResult($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetStateProfiletype"){
  		$result = $obj->GetStateProfiletype();
		echo $result;
	}
	if($_POST['ajax'] == "SaveProfileType"){
  		$result = $obj->SaveProfileType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetStatesCustomInfo"){
  		$result = $obj->GetStatesCustomInfo($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveCustomResult"){
  		$result = $obj->SaveCustomResult($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSiteDetails"){
  		$result = $obj->GetSiteDetails($_POST);
		echo $result;
	 }

	if($_POST['ajax'] == "fleet_risk_serials"){
  		$result = $obj->fleet_risk_serials($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetfleetriskDetails"){
  		$result = $obj->GetfleetriskDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetFleetriskList"){
  		$result = $obj->GetFleetriskList($_POST);
		echo $result;
	}
	 if($_POST['ajax'] == "HarvestType"){
  		$result = $obj->HarvestType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetHarvestTypeDetails"){
  		$result = $obj->GetHarvestTypeDetails($_POST);
		echo $result;
	 }
	 if($_POST['ajax'] == "GetHarvestTypeList"){
  		$result = $obj->GetHarvestTypeList($_POST);
		echo $result;
	}
	 if($_POST['ajax'] == "SanitizationType"){
  		$result = $obj->SanitizationType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSanitizationTypeDetails"){
  		$result = $obj->GetSanitizationTypeDetails($_POST);
		echo $result;
	 }
	 if($_POST['ajax'] == "GetSanitizationTypeList"){
  		$result = $obj->GetSanitizationTypeList($_POST);
		echo $result;
	}
	 if($_POST['ajax'] == "RepairType"){
  		$result = $obj->RepairType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetRepairTypeDetails"){
  		$result = $obj->GetRepairTypeDetails($_POST);
		echo $result;
	 }
	 if($_POST['ajax'] == "GetRepairTypeList"){
  		$result = $obj->GetRepairTypeList($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetUserFacility") {
  		//$result = $obj->GetUserFacility();
		echo $_SESSION['user']['FacilityID'];
	 }

	if($_POST['ajax'] == "GetAllTabs") {
  		$result = $obj->GetAllTabs();
		echo $result;
	}

	if($_POST['ajax'] == "UpdateTabs") {
  		$result = $obj->UpdateTabs($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetUserAccountID") {
  		echo $_SESSION['user']['AccountID'];
	}

	if($_POST['ajax'] == "selectproductclass"){
		$result = $obj->SelectProductClass();
		echo $result;
	}
	if($_POST['ajax'] == "selectproductcat"){
		$result = $obj->SelectProductCat($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "selectproductatt"){
		$result = $obj->SelectProductAtt($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "productcreate"){
		$result = $obj->ProductCreate($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetProductDetails"){
  		$result = $obj->GetProductDetails($_POST);
		echo $result;
	 }
	 if($_GET['ajax'] == "GetProducts"){
		$result = $obj->GetProducts();
		echo $result;
	}

	if($_POST['ajax'] == "GetCustomFields"){
		$result = $obj->GetCustomFields($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateNewField"){
		$result = $obj->CreateNewField($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ChangeAccess"){
		$result = $obj->ChangeAccess($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ChangeFieldStatus"){
		$result = $obj->ChangeFieldStatus($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetPageLocations"){
		$result = $obj->GetPageLocations($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCountryStates"){
		$result = $obj->GetCountryStates($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CreateNewDivision") {
		$result = $obj->CreateNewDivision($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetDivisions") {
		$result = $obj->GetDivisions($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetRefCustomers") {
		$result = $obj->GetRefCustomers($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AssignDivisionToRefCustomer") {
		$result = $obj->AssignDivisionToRefCustomer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteDivisionToRefCustomer") {
		$result = $obj->DeleteDivisionToRefCustomer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ChangeValueStatus"){
		$result = $obj->ChangeValueStatus($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetDivisionReferenceCustomers"){
  		$result = $obj->GetDivisionReferenceCustomers($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdateCategoryPrice"){
		$result = $obj->UpdateCategoryPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdaterefCategoryPrice"){
		$result = $obj->UpdateRefCategoryPrice($_POST);
		echo $result;
	}
	if($_POST['method'] == "OKTALogin"){
  		$result = $obj->OKTALogin($_POST);
		echo $result;
	 }
	 if($_GET['ajax'] == "GetPaymentTerms"){
		$result = $obj->GetPaymentTerms();
		echo $result;
	}
	if($_POST['ajax'] == "PaymentTermsDetails"){
		$result = $obj->PaymentTermsDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "PaymentTerms"){
		$result = $obj->PaymentTerms($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetVendorTypes"){
  		$result = $obj->GetVendorTypes($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetVendorCat"){
  		$result = $obj->GetVendorCat($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetIndustries"){
  		$result = $obj->GetIndustries();
		echo $result;
	}
	if($_POST['ajax'] == "UpdateLabourPrice"){
		$result = $obj->UpdateLabourPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdateDataCapturePrice"){
		$result = $obj->UpdateDataCapturePrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdatePartsHarvestPrice"){
		$result = $obj->UpdatePartsHarvestPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetDestructionPrice"){
		$result = $obj->GetDestructionPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdateDataDestructionPrice"){
		$result = $obj->UpdateDataDestructionPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "ManageMetal"){
		$result = $obj->ManageMetal($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetMetalDetails"){
		$result = $obj->GetMetalDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAllPreciousMetals"){
		$result = $obj->GetAllPreciousMetals($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddMetal"){
		$result = $obj->AddMetal($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAssignedMetals"){
  		$result = $obj->GetAssignedMetals($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteCommodityPart"){
  		$result = $obj->DeleteCommodityPart($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetLoadPrice"){
		$result = $obj->GetLoadPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdateLoadPrice"){
		$result = $obj->UpdateLoadPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdateMillFee"){
		$result = $obj->UpdateMillFee($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdateChemicalFee"){
		$result = $obj->UpdateChemicalFee($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveJetFee"){
		$result = $obj->SaveJetFee($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetJetFee"){
		$result = $obj->GetJetFee();
		echo $result;
	}
	if($_POST['ajax'] == "SaveUnitPrice"){
		$result = $obj->SaveUnitPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveCommodity_Price"){
		$result = $obj->SaveCommodity_Price($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveDownstreamUnitPrice"){
		$result = $obj->SaveDownstreamUnitPrice($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveDownstreamLBPrice"){
		$result = $obj->SaveDownstreamLBPrice($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UploadCategoryImage"){
		$result = $obj->UploadCategoryImage($_FILES,$_POST);
		echo $result;
	}
	if($_POST['ajax'] == "DeleteCategoryImage"){
		$result = $obj->DeleteCategoryImage($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAccountUsers"){
  		$result = $obj->GetAccountUsers();
		echo $result;
	 }
	 if($_POST['ajax'] == "GetVendorTypeDetails"){
  		$result = $obj->GetVendorTypeDetails($_POST);
		echo $result;
	 }
	 if($_POST['ajax'] == "NewVendorType"){
		$result = $obj->NewVendorType($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetVendorTier"){
  		$result = $obj->GetVendorTier($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetVendorTierDetails"){
  		$result = $obj->GetVendorTierDetails($_POST);
		echo $result;
	 }
	 if($_POST['ajax'] == "NewVendorTier"){
		$result = $obj->NewVendorTier($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "VerifieUser"){
		$result = $obj->VerifieUser($_POST);
		echo $result;
	}



	//Ticket project code start here

	if($_POST['ajax'] == "GetBenefits"){
		$result = $obj->GetBenefits($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CreateBenefit"){
		$result = $obj->CreateBenefit($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetBenefitDetails"){
		$result = $obj->GetBenefitDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetApplications"){
		$result = $obj->GetApplications($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateApplication"){
		$result = $obj->CreateApplication($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetApplicationDetails"){
		$result = $obj->GetApplicationDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetModules"){
		$result = $obj->GetModules($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCustomerApplications"){
		$result = $obj->GetCustomerApplications($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateModule"){
		$result = $obj->CreateModule($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetModuleDetails"){
		$result = $obj->GetModuleDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSections"){
		$result = $obj->GetSections($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetApplicationModules"){
		$result = $obj->GetApplicationModules($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSectionDetails"){
		$result = $obj->GetSectionDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateSection"){
		$result = $obj->CreateSection($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSubSections"){
		$result = $obj->GetSubSections($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetModuleSections"){
		$result = $obj->GetModuleSections($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSubSectionDetails"){
		$result = $obj->GetSubSectionDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateSubSection"){
		$result = $obj->CreateSubSection($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAllActions"){
		$result = $obj->GetAllActions($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCategoryActions"){
		$result = $obj->GetCategoryActions($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateCategoryAction"){
		$result = $obj->CreateCategoryAction($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteCategoryAction"){
		$result = $obj->DeleteCategoryAction($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAllActionsList"){
		$result = $obj->GetAllActionsList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAllStatusses"){
		$result = $obj->GetAllStatusses($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetActionDetails"){
		$result = $obj->GetActionDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ManageLines"){
		$result = $obj->ManageLines($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetLineDetails"){
		$result = $obj->GetLineDetails($_POST);
		echo $result;
	}

	if($_GET['ajax'] == "GetLinesList"){
		$result = $obj->GetLinesList();
		echo $result;
	}

	if($_POST['ajax'] == "AddStationToLine") {
		$result = $obj->AddStationToLine($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteLineStation") {
		$result = $obj->DeleteLineStation($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CreateLocationGroup"){
		$result = $obj->CreateLocationGroup($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "LocationGroupDetails"){
		$result = $obj->LocationGroupDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetLocationGroupList"){
		$result = $obj->GetLocationGroupList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdateInputDefault"){
		$result = $obj->UpdateInputDefault($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Getstates"){
		$result = $obj->Getstates();
		echo $result;
	}

	if($_POST['ajax'] == "Rig"){
  		$result = $obj->Rig($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetRig"){
  		$result = $obj->GetRig();
		echo $result;
	}
	if($_POST['ajax'] == "GetRigList"){
  		$result = $obj->GetRigList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetRigDetails"){
  		$result = $obj->GetRigDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SelectWorkflowlabortracking"){
		$result = $obj->SelectWorkflowlabortracking();
		echo $result;
	}

	if($_POST['ajax'] == "Workflowlabortracking"){
  		$result = $obj->Workflowlabortracking($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetGetWorkflowlabortracking"){
  		$result = $obj->GetGetWorkflowlabortracking();
		echo $result;
	}
	if($_POST['ajax'] == "GetWorkflowlabortrackingList"){
  		$result = $obj->GetWorkflowlabortrackingList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetPendingLaborTracker"){
  		$result = $obj->GetPendingLaborTracker($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SubmitPendingLaborTracking"){
  		$result = $obj->SubmitPendingLaborTracking($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetWorkflowlabortrackingDetails"){
  		$result = $obj->GetWorkflowlabortrackingDetails($_POST);
		echo $result;
	}
	 if($_POST['ajax'] == "GetWorkflowlabortrackingdesc"){
		$result = $obj->GetWorkflowlabortrackingdesc($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SelectWorkstation"){
		$result = $obj->SelectWorkstation();
		echo $result;
	}

	if($_POST['ajax'] == "SelectRepairType"){
		$result = $obj->SelectRepairType();
		echo $result;
	}
	if($_GET['ajax'] == "GetRepairType"){
  		$result = $obj->GetRepairType($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ChangeWorkflowInputStatus"){
		$result = $obj->ChangeWorkflowInputStatus($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSubDispositionList"){
		$result = $obj->GetSubDispositionList($_POST);
	  echo $result;
  	}

	if($_POST['ajax'] == "ManageSubDisposition"){
		$result = $obj->ManageSubDisposition($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetSubDispositionDetails") {
		$result = $obj->GetSubDispositionDetails($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "DeleteMPNAttributeFile") {
		$result = $obj->DeleteMPNAttributeFile($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "RecordUserSideMenuTransaction") {
		$result = $obj->RecordUserSideMenuTransaction($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetUserActivityList") {
		$result = $obj->GetUserActivityList($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GenerateUserActivityxls") {
		$result = $obj->GenerateUserActivityxls($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "RecordUserAdminActivity") {
		$result = $obj->RecordUserAdminActivity($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "Logout") {
		$result = $obj->Logout($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "UnlockStation") {
		$result = $obj->UnlockStation($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetWarrantySerialList") {
		$result = $obj->GetWarrantySerialList($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "ManageWarrantySerial"){
		$result = $obj->ManageWarrantySerial($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetWarrantySerialDetails"){
		$result = $obj->GetWarrantySerialDetails($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetStationMappedBINS") {
		$result = $obj->GetStationMappedBINS($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "DeleteMappedBIN") {
		$result = $obj->DeleteMappedBIN($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetAllDispositions") {
		$result = $obj->GetAllDispositions($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "AddDispositionToWorkflow") {
		$result = $obj->AddDispositionToWorkflow($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetMappedDisposition") {
		$result = $obj->GetMappedDisposition($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "DeleteMappedDisposition") {
		$result = $obj->DeleteMappedDisposition($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetLockedSourceBinList") {
		$result = $obj->GetLockedSourceBinList($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "UnlockSourceBIN") {
		$result = $obj->UnlockSourceBIN($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetWorkflows") {
		$result = $obj->GetWorkflows($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "AddDispositionToStation") {
		$result = $obj->AddDispositionToStation($_POST);
		echo $result;
  	}

	  if($_POST['ajax'] == "GetMappedDispositionsForStation") {
		$result = $obj->GetMappedDispositionsForStation($_POST);
		echo $result;
  	}

	  if($_POST['ajax'] == "DeleteMappedDispositionForStation") {
		$result = $obj->DeleteMappedDispositionForStation($_POST);
		echo $result;
  	}

  	if($_POST['ajax'] == "GenerateContainerTypeListXLS") {
		$result = $obj->GenerateContainerTypeListXLS($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "BulkMediaGetLockedSourceBinList") {
		$result = $obj->BulkMediaGetLockedSourceBinList($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "UnlockBulkMediaSourceBIN") {
		$result = $obj->UnlockBulkMediaSourceBIN($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetAllRegions") {
		$result = $obj->GetAllRegions($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "AddMoreLocations"){
		$result = $obj->AddMoreLocations($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetPageGroupLocations"){
		$result = $obj->GetPageGroupLocations($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateLocationGroupListXLS") {
		$result = $obj->GenerateLocationGroupListXLS($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetLocationTypeLabels") {
		$result = $obj->GetLocationTypeLabels($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateBinLocationGroup") {
		$result = $obj->UpdateBinLocationGroup($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetBinTypes") {
		$result = $obj->GetBinTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetBinTypes1") {
		$result = $obj->GetBinTypes1($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateBinPartTypeSummary") {
		$result = $obj->UpdateBinPartTypeSummary($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveMaterialType"){
  		$result = $obj->SaveMaterialType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetMaterialTypeDetails"){
  		$result = $obj->GetMaterialTypeDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetMaterialTypeList"){
  		$result = $obj->GetMaterialTypeList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateMaterialTypeListxls") {
		$result = $obj->GenerateMaterialTypeListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveSourceType"){
  		$result = $obj->SaveSourceType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSourceTypeDetails"){
  		$result = $obj->GetSourceTypeDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSourceTypeList"){
  		$result = $obj->GetSourceTypeList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateSourceTypeListxls") {
		$result = $obj->GenerateSourceTypeListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveAWSCustomer"){
  		$result = $obj->SaveAWSCustomer($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAWSCustomerDetails"){
  		$result = $obj->GetAWSCustomerDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAWSCustomerList"){
  		$result = $obj->GetAWSCustomerList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateAWSCustomerListxls") {
		$result = $obj->GenerateAWSCustomerListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "AWSCustomers") {
		$result = $obj->AWSCustomers($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateCustomerListXLS") {
		$result = $obj->GenerateCustomerListXLS($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateSourceTypeConfigurationListXLS") {
		$result = $obj->GenerateSourceTypeConfigurationListXLS($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SelectRecoveryType") {
		$result = $obj->SelectRecoveryType($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "parttype"){
		$result = $obj->parttype($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetparttypeDetails"){
  		$result = $obj->GetparttypeDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetparttypeList"){
  		$result = $obj->GetparttypeList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateparttypeListxls") {
		$result = $obj->GenerateparttypeListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAllParttypecoo") {
		$result = $obj->GetAllParttypecoo($_POST);
		echo $result;
  	}
	if($_POST['ajax'] == "AddParttypecoomapping") {
		$result = $obj->AddParttypecoomapping($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "ChangeparttypecooStatus"){
		$result = $obj->ChangeparttypecooStatus($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdateParttypeDefault"){
		$result = $obj->UpdateParttypeDefault($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetRecoveryParttypesDetails"){
		$result = $obj->GetRecoveryParttypesDetails($_POST);
	  echo $result;
  	}
	if($_POST['ajax'] == "GetParttypeseriallized"){
		$result = $obj->GetParttypeseriallized($_POST);
	  echo $result;
  	}
  	if($_POST['ajax'] == "SelectRemovalcodeparttype"){
		$result = $obj->SelectRemovalcodeparttype($_POST);
	echo $result;
	}
	if($_POST['ajax'] == "Recoverytype"){
		$result = $obj->Recoverytype($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetRecoverytypeDetails"){
		$result = $obj->GetRecoverytypeDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetRecoverytypeList"){
		$result = $obj->GetRecoverytypeList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateRecoverytypeListxls") {
		$result = $obj->GenerateRecoverytypeListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetserialRecoverypart") {
		$result = $obj->GetserialRecoverypart($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "DeleteUnserialRecoveryPart"){
		$result = $obj->DeleteUnserialRecoveryPart($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetUnserializedPartTypes"){
		$result = $obj->GetUnserializedPartTypes();
		echo $result;
	}
	if($_GET['ajax'] == "GetSerialDisposition"){
  		$result = $obj->GetSerialDisposition($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SelectUnserializedparttype"){
		$result = $obj->SelectUnserializedparttype($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SelectUnserializedparttypeNo"){
		$result = $obj->SelectUnserializedparttypeNo();
		echo $result;
	}
	if($_POST['ajax'] == "Selectdispositions"){
		$result = $obj->Selectdispositions();
		echo $result;
	}
	if($_POST['ajax'] == "unserailaizedparttypedispositon"){
		$result = $obj->unserailaizedparttypedispositon($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetunserailaizedparttypedispositonList"){
  		$result = $obj->GetunserailaizedparttypedispositonList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Deleteunserailaizedparttypedispositon"){
		$result = $obj->Deleteunserailaizedparttypedispositon($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetunserailaizedparttypedispositonDetails"){
  		$result = $obj->GetunserailaizedparttypedispositonDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "AddRecoveryParttypes") {
		$result = $obj->AddRecoveryParttypes($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "GetRecoveryParttypemapping") {
		$result = $obj->GetRecoveryParttypemapping($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "DeleteMappedRecoveryParttype") {
		$result = $obj->DeleteMappedRecoveryParttype($_POST);
		echo $result;
  	}

  	if($_POST['ajax'] == "GetSortDisposition"){
		$result = $obj->GetSortDisposition($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SelectSortBin"){
  		$result = $obj->SelectSortBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSortLocation"){
  		$result = $obj->GetSortLocation($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SortConfigurationSave"){
		$result = $obj->SortConfigurationSave($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetsortconfigurationList"){
  		$result = $obj->GetsortconfigurationList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Deletesortconfiguration"){
		$result = $obj->Deletesortconfiguration($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetsortconfigurationDetails"){
  		$result = $obj->GetsortconfigurationDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GeneratesortconfigurationListxls") {
		$result = $obj->GeneratesortconfigurationListxls($_POST);
		echo $result;
	}
	
	
	if($_POST['ajax'] == "EvaluationResult"){
		$result = $obj->EvaluationResult($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetEvaluationResultDetails"){
  		$result = $obj->GetEvaluationResultDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetEvaluationResultList"){
  		$result = $obj->GetEvaluationResultList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateEvaluationResultListxls") {
		$result = $obj->GenerateEvaluationResultListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAllEvaluationResults") {
		$result = $obj->GetAllEvaluationResults($_POST);
		echo $result;
  	}

	  if($_POST['ajax'] == "GetPartTypesByRecoverytype") {
		$result = $obj->GetPartTypesByRecoverytype($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateUnserialRecoverypart") {
		$result = $obj->CreateUnserialRecoverypart($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAllRecoveryTypes") {
		$result = $obj->GetAllRecoveryTypes($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "SelectWorkstationConfiguration"){
		$result = $obj->SelectWorkstationConfiguration($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveWorkstationConfiguration"){
		$result = $obj->SaveWorkstationConfiguration($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetWorkstationConfigurationDetails"){
		$result = $obj->GetWorkstationConfigurationDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetWorkstationConfigurationList"){
  		$result = $obj->GetWorkstationConfigurationList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "LaborTrackerReasonCodeSave"){
		$result = $obj->LaborTrackerReasonCodeSave($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetLaborReasonCodeList"){
  		$result = $obj->GetLaborReasonCodeList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetLaborReasonCodeDetails"){
  		$result = $obj->GetLaborReasonCodeDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateLaborReasonCodeListxls") {
		$result = $obj->GenerateLaborReasonCodeListxls($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetAcManager"){
  		$result = $obj->GetAcManager($_POST);
		echo $result;
	 }
	 if($_POST['ajax'] == "SubmitManagerPendingLaborTracking"){
		$result = $obj->SubmitManagerPendingLaborTracking($_POST);
	  echo $result;
  	}
	if($_POST['ajax'] == "GetManagerPendingLaborTracker"){
		$result = $obj->GetManagerPendingLaborTracker($_POST);
	  echo $result;
  	}
  	if($_POST['ajax'] == "ProgramConfigurationSave"){
		$result = $obj->ProgramConfigurationSave($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetProgramConfigurationList"){
  		$result = $obj->GetProgramConfigurationList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetProgramConfigurationDetails"){
  		$result = $obj->GetProgramConfigurationDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateProgramConfigurationListxls") {
		$result = $obj->GenerateProgramConfigurationListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetPrograms"){
		$result = $obj->GetPrograms($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Generatemanagerpendinglabortrackerxls") {
		$result = $obj->Generatemanagerpendinglabortrackerxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetLabourTrackingReport"){
		$result = $obj->GetLabourTrackingReport($_POST);
	  echo $result;
  	}
	if($_POST['ajax'] == "GenerateLabourTrackingReportXLS"){
		$result = $obj->GenerateLabourTrackingReportXLS($_POST);
	  echo $result;
  	}
  	if($_POST['ajax'] == "GetLabourTrackingDetails"){
  		$result = $obj->GetLabourTrackingDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UpdateLaborTracking"){
		$result = $obj->UpdateLaborTracking($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "DeleteLabourTrackingReport"){
		$result = $obj->DeleteLabourTrackingReport($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "selectOverrideReasonCode"){
		$result = $obj->selectOverrideReasonCode($_POST);
		echo $result;
	}
	if($_GET['ajax'] == "GetMatchingLocationGroups") {
		$result = $obj->GetMatchingLocationGroups($_GET);
		echo $result;
	}
	if($_GET['ajax'] == "GetMatchingLocationGroupsBin") {
		$result = $obj->GetMatchingLocationGroupsBin($_GET);
		echo $result;
	}
	if($_POST['ajax'] == "CreateMoveBin") {
		$result = $obj->CreateMoveBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetProfileList"){
		$result = $obj->GetProfileList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateProfileListXLS") {
		$result = $obj->GenerateProfileListXLS($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SelectSortManualBin"){
  		$result = $obj->SelectSortManualBin($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "GetAssetFromScanSN") {
		$result = $obj->GetAssetFromScanSN($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "Selectstatusses"){
		$result = $obj->Selectstatusses($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CreateReassign"){
		$result = $obj->CreateReassign($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSerialNumberValues"){
		$result = $obj->GetSerialNumberValues($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSerialNumberValuesReDisposition"){
		$result = $obj->GetSerialNumberValuesReDisposition($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetDispositionOverride"){
		$result = $obj->GetDispositionOverride($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SelectReassignDisposition"){
		$result = $obj->SelectReassignDisposition($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetInboundContainers") {
		$result = $obj->GetInboundContainers($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "SelectUserFacility"){
		$result = $obj->SelectUserFacility($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CreateContainerAttribute"){
		$result = $obj->CreateContainerAttribute($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAssetFromScanSNwithBRE") {
		$result = $obj->GetAssetFromScanSNwithBRE($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "GetDispositionByBRE"){
		$result = $obj->GetDispositionByBRE($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SelectEvaluationResult") {
		$result = $obj->SelectEvaluationResult($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "CreateReDisposition"){
		$result = $obj->CreateReDisposition($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateRigListXLS") {
		$result = $obj->GenerateRigListXLS($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSourceType") {
		$result = $obj->GetSourceType($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "GetSourceType1") {
		$result = $obj->GetSourceType1($_POST);
		echo $result;
  	}
  	
?>
