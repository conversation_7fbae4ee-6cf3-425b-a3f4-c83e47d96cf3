<style>
  /*
.selectdemoSelectHeader {
   Please note: All these selectors are only applied to children of elements with the 'selectdemoSelectHeader' class
}*/
.selectdemoSelectHeader .demo-header-searchbox {
  border: none;
  outline: none;
  height: 100%;
  width: 100%;
  padding: 0;
}
.selectdemoSelectHeader .demo-select-header {
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(0, 0, 0, 0.14), 0 0 0 0 rgba(0, 0, 0, 0.12);
  padding-left: 16px;
  height: 40px;
  cursor: pointer;
  position: relative;
  display: flex;
  width: auto;
}
.selectdemoSelectHeader md-content._md {
  max-height: 240px;
}
.selectdemoSelectHeader md-input-container {
  min-width: 112px;
}
md-virtual-repeat-container{top:180px !important;}

</style>
<div class="row page  page-invoice" data-ng-controller="ChangeLocationGroup" id="popupContainer" ng-cloak >
    <div class="col-md-12">
        <article class="article">

            <div class="row ui-section">

                <div class="col-md-12">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="LocationGroupInformationPannel = true;">
                            <div class="md-toolbar-tools">
                                <i ng-click="LocationGroupInformationPannel = !LocationGroupInformationPannel" class="material-icons md-primary" ng-show="LocationGroupInformationPannel">keyboard_arrow_up</i>
                                <i ng-click="LocationGroupInformationPannel = !LocationGroupInformationPannel" class="material-icons md-primary" ng-show="!LocationGroupInformationPannel">keyboard_arrow_down</i>
                                <span ng-click="LocationGroupInformationPannel = !LocationGroupInformationPannel">Change Location Group</span>
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="LocationGroupInformationPannel">
                            <div class="col-md-12">

                                <div class="col-md-4">                                  
                                    <div class="autocomplete insideuse">
                                        <md-autocomplete required flex style="margin-bottom:0px !important; margin-top:0px !important; padding-top: 0px !important;"
                                            md-input-name="FromLocationGroup"
                                            md-input-maxlength="100"
                                            md-no-cache="noCache"
                                            md-search-text-change="LocationChange1(FromLocationGroup)"
                                            md-search-text="FromLocationGroup"
                                            md-items="item in queryLocationSearch1(FromLocationGroup)"
                                            md-item-text="item.GroupName"
                                            md-selected-item-change="selectedLocationChange1(item)"
                                            md-min-length="2"
                                            ng-model-options='{ debounce: 1000 }'
                                            md-escape-options="clear"
                                            md-floating-label="Search From Location Group"                                          
                                            >
                                            <md-item-template>
                                                <span md-highlight-text="FromLocationGroup" md-highlight-flags="^i">{{item.GroupName}}</span>
                                            </md-item-template>
                                            <md-not-found>
                                                No Records matching "{{FromLocationGroup}}" were found.
                                            </md-not-found>
                                        </md-autocomplete>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="autocomplete insideuse">
                                        <md-autocomplete required 
                                            md-no-cache="noCache"
                                            md-search-text-change="ContainerLocationChange1(ToLocationGroup)"
                                            md-search-text="ToLocationGroup"
                                            md-items="item in queryContainerLocationSearch1(ToLocationGroup)"
                                            md-item-text="item.GroupName"
                                            md-selected-item-change="selectedContainerLocationChange1(item)"
                                            md-min-length="0"
                                            ng-model-options='{ debounce: 1000 }'
                                            placeholder="Search To Location Group">
                                            <md-item-template>
                                                <span md-highlight-text="ToLocationGroup" md-highlight-flags="^i">{{item.GroupName}}</span>
                                            </md-item-template>
                                            <md-not-found>
                                                No Records matching "{{ToLocationGroup}}" were found.
                                            </md-not-found>
                                        </md-autocomplete>
                                    </div>
                                </div>
                                <div class="col-md-12 btns-row">
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md" ng-click="ShowPalletConfirmPopup($event)" ng-disabled="!(AllLocations.length > 0 && ToLocationGroup)">TRANSFER</md-button>
                                </div>

                            </div>
                        </div>

                    </md-card>

                    <md-card class="no-margin-h pt-0" ng-if="AllLocations.length > 0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="LocationsPannel = true;">
                            <div class="md-toolbar-tools">
                                <i ng-click="LocationsPannel = !LocationsPannel" class="material-icons md-primary" ng-show="LocationsPannel">keyboard_arrow_up</i>
                                <i ng-click="LocationsPannel = !LocationsPannel" class="material-icons md-primary" ng-show="!LocationsPannel">keyboard_arrow_down</i>
                                <span ng-click="LocationsPannel = !LocationsPannel">Locations</span>
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="LocationsPannel">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div class="table-responsive" style="overflow: auto;">
                                        <table md-table class="table">
                                            <thead md-head>
                                                <tr md-row class="bg-grey">
                                                    <th md-column>Location</th>
                                                    <th md-column>Facility</th>
                                                    <th md-column>Item</th>
                                                </tr>
                                            </thead>
                                            <tbody md-body>

                                                <tr md-row ng-repeat= "loc in AllLocations">
                                                    <td md-cell>{{loc.LocationName}}</td>
                                                    <td md-cell>{{loc.FacilityName}}</td>
                                                    <td md-cell>{{loc.currentItemType}} ({{loc.currentItemID}})</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </md-card>

                </div>

            </div>
        </article>
    </div>
</div>
