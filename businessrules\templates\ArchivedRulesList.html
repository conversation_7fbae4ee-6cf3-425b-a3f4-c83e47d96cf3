<style>
.rule-summary-cell {
    transition: all 0.2s ease;
}

.rule-summary-text {
    font-size: 13px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.rule-summary-text:hover {
    background-color: #f5f5f5;
    border-radius: 3px;
}

.rule-summary-toggle {
    transition: all 0.2s ease;
}

.rule-summary-toggle:hover {
    transform: scale(1.05);
}

.rule-summary-truncated {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.archived-badge {
    background-color: #ff9800;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
}
</style>

<div ng-controller="ArchivedRulesList" class="page">
    <div class="row ui-section mb-0">
        <div class="col-md-12">
            <article class="article">
                <div class="body_inner_content">
                    <div class="row">
                        <div class="col-md-12">
                            <md-toolbar class="md-table-toolbar md-default">
                                <div class="md-toolbar-tools">
                                    <h2 class="md-title">
                                        <i class="material-icons" style="margin-right: 8px;">archive</i>
                                        Archived Business Rules
                                    </h2>
                                    <div flex></div>
                                    <div class="md-toolbar-tools-bottom">
                                        <a href="#" ng-click="ExportArchivedRulesList()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex;">
                                            <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg"></md-icon>
                                            <span>Export to Excel</span>
                                        </a>
                                        <a href="#" ng-click="ExportArchivedRulesList()" class="md-button md-raised md-default dis_open_v" style="display: none; min-width: 40px;">
                                            <md-icon class="mt-10 excel_icon" md-svg-src="../assets/images/excel.svg"></md-icon>
                                        </a>
                                    </div>
                                </div>
                            </md-toolbar>
                        </div>
                    </div>



                    <!-- Table Section -->
                    <div class="row" style="margin-top: 10px;">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body" style="padding: 0;">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th ng-click="sort('version_name')" style="cursor: pointer;">
                                                        Version Name
                                                        <i class="material-icons" ng-show="OrderBy == 'version_name' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'version_name' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('priority')" style="cursor: pointer;">
                                                        Priority
                                                        <i class="material-icons" ng-show="OrderBy == 'priority' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'priority' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('rule_name')" style="cursor: pointer;">
                                                        Rule Name
                                                        <i class="material-icons" ng-show="OrderBy == 'rule_name' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'rule_name' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('rule_description')" style="cursor: pointer;">
                                                        Description
                                                        <i class="material-icons" ng-show="OrderBy == 'rule_description' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'rule_description' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('CustomerName')" style="cursor: pointer;">
                                                        Customer
                                                        <i class="material-icons" ng-show="OrderBy == 'CustomerName' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'CustomerName' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('FacilityName')" style="cursor: pointer;">
                                                        Facility
                                                        <i class="material-icons" ng-show="OrderBy == 'FacilityName' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'FacilityName' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('workflow')" style="cursor: pointer;">
                                                        Workflow
                                                        <i class="material-icons" ng-show="OrderBy == 'workflow' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'workflow' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('part_types')" style="cursor: pointer;">
                                                        Part Type
                                                        <i class="material-icons" ng-show="OrderBy == 'part_types' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'part_types' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('rule_summary')" style="cursor: pointer;">
                                                        Rule Summary
                                                        <i class="material-icons" ng-show="OrderBy == 'rule_summary' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'rule_summary' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('disposition')" style="cursor: pointer;">
                                                        Disposition
                                                        <i class="material-icons" ng-show="OrderBy == 'disposition' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'disposition' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('rule_id')" style="cursor: pointer;">
                                                        Rule ID
                                                        <i class="material-icons" ng-show="OrderBy == 'rule_id' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'rule_id' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('SourceTypeName')" style="cursor: pointer;">
                                                        Source Type
                                                        <i class="material-icons" ng-show="OrderBy == 'SourceTypeName' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'SourceTypeName' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('MaterialTypeName')" style="cursor: pointer;">
                                                        Material Type
                                                        <i class="material-icons" ng-show="OrderBy == 'MaterialTypeName' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'MaterialTypeName' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('created_date')" style="cursor: pointer;">
                                                        Created Date
                                                        <i class="material-icons" ng-show="OrderBy == 'created_date' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'created_date' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('created_by')" style="cursor: pointer;">
                                                        Created By
                                                        <i class="material-icons" ng-show="OrderBy == 'created_by' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'created_by' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('updated_date')" style="cursor: pointer;">
                                                        Updated Date
                                                        <i class="material-icons" ng-show="OrderBy == 'updated_date' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'updated_date' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                    <th ng-click="sort('updated_by')" style="cursor: pointer;">
                                                        Updated By
                                                        <i class="material-icons" ng-show="OrderBy == 'updated_by' && OrderByType == 'asc'">keyboard_arrow_up</i>
                                                        <i class="material-icons" ng-show="OrderBy == 'updated_by' && OrderByType == 'desc'">keyboard_arrow_down</i>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="rule in pagedItems">
                                                    <td>{{rule.version_name}}</td>
                                                    <td>{{rule.priority}}</td>
                                                    <td>{{rule.rule_name}}</td>
                                                    <td>{{rule.rule_description}}</td>
                                                    <td>{{getCustomerNamesFromIds(rule.AWSCustomerID)}}</td>
                                                    <td>{{rule.FacilityName || (rule.FacilityID == 'All' ? 'All' : rule.FacilityID)}}</td>
                                                    <td>{{rule.workflow || (rule.workflow_id == 'All' ? 'All' : rule.workflow_id)}}</td>
                                                    <td>{{rule.part_types}}</td>
                                                    <td class="rule-summary-cell">
                                                        <div class="rule-summary-text" ng-click="showRuleDetails(rule, $event)">
                                                            <div class="rule-summary-truncated">{{rule.rule_summary}}</div>
                                                        </div>
                                                    </td>
                                                    <td>{{rule.disposition}}</td>
                                                    <td>{{rule.rule_id}}</td>
                                                    <td>{{rule.SourceTypeName || (rule.idCustomertype == 'All' ? 'All' : rule.idCustomertype)}}</td>
                                                    <td>{{rule.MaterialTypeName || (rule.MaterialType == 'All' ? 'All' : rule.MaterialType)}}</td>
                                                    <td>{{rule.created_date | date:'MM/dd/yyyy HH:mm'}}</td>
                                                    <td>{{rule.created_by}}</td>
                                                    <td>{{rule.updated_date | date:'MM/dd/yyyy HH:mm'}}</td>
                                                    <td>{{rule.updated_by}}</td>
                                                </tr>
                                            </tbody>
                                            <tbody ng-show="pagedItems.length == 0 && !busy">
                                                <tr>
                                                    <td colspan="17" class="text-center" style="padding: 40px;">
                                                        <i class="material-icons" style="font-size: 48px; color: #ccc;">archive</i>
                                                        <h4 style="color: #999; margin-top: 10px;">No archived rules found</h4>
                                                        <p style="color: #999;">There are no archived rules matching your current filters.</p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Pagination -->
                                    <div class="row" ng-show="pagedItems.length > 0" style="padding: 15px;">
                                        <div class="col-md-12 text-center">
                                            <md-button class="md-raised" ng-disabled="currentPage == 0" ng-click="currentPage = currentPage - 1">
                                                <i class="material-icons">chevron_left</i> Previous
                                            </md-button>
                                            <md-button class="md-raised" ng-disabled="(currentPage + 1) * itemsPerPage >= totalCount" ng-click="currentPage = currentPage + 1">
                                                Next <i class="material-icons">chevron_right</i>
                                            </md-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </div>
</div>

<!-- Rule Details Modal Template -->
<script type="text/ng-template" id="archivedRuleDetailsModal.html">
    <md-dialog aria-label="Archived Rule Details" style="min-width: 600px; max-width: 800px;">
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>
                    <i class="material-icons" style="margin-right: 8px;">archive</i>
                    Archived Rule Details
                </h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="cancel()">
                    <md-icon class="material-icons">close</md-icon>
                </md-button>
            </div>
        </md-toolbar>
        <md-dialog-content style="padding: 20px;">
            <div class="md-dialog-content">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Rule Name:</strong> {{ruleData.rule_name}}
                    </div>
                    <div class="col-md-6">
                        <strong>Version:</strong> {{ruleData.version_name}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-12">
                        <strong>Description:</strong> {{ruleData.rule_description}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-6">
                        <strong>Priority:</strong> {{ruleData.priority}}
                    </div>
                    <div class="col-md-6">
                        <strong>Disposition:</strong> {{ruleData.disposition}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-6">
                        <strong>Customer:</strong> {{getCustomerNamesFromIds(ruleData.AWSCustomerID)}}
                    </div>
                    <div class="col-md-6">
                        <strong>Facility:</strong> {{ruleData.FacilityName || (ruleData.FacilityID == 'All' ? 'All' : ruleData.FacilityID)}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-6">
                        <strong>Workflow:</strong> {{ruleData.workflow || (ruleData.workflow_id == 'All' ? 'All' : ruleData.workflow_id)}}
                    </div>
                    <div class="col-md-6">
                        <strong>Part Types:</strong> {{ruleData.part_types}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-12">
                        <strong>Rule Summary:</strong>
                        <div style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; margin-top: 5px;">
                            {{ruleData.rule_summary}}
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-6">
                        <strong>Created:</strong> {{ruleData.created_date | date:'MM/dd/yyyy HH:mm'}}
                    </div>
                    <div class="col-md-6">
                        <strong>Created By:</strong> {{ruleData.created_by}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;" ng-show="ruleData.updated_date">
                    <div class="col-md-6">
                        <strong>Updated:</strong> {{ruleData.updated_date | date:'MM/dd/yyyy HH:mm'}}
                    </div>
                    <div class="col-md-6">
                        <strong>Updated By:</strong> {{ruleData.updated_by}}
                    </div>
                </div>
            </div>
        </md-dialog-content>
        <md-dialog-actions layout="row">
            <span flex></span>
            <md-button ng-click="cancel()" class="md-primary">
                Close
            </md-button>
        </md-dialog-actions>
    </md-dialog>
</script>
