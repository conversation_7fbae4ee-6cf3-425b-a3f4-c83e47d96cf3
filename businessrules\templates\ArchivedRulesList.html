<style>
.rule-summary-cell {
    transition: all 0.2s ease;
}

.rule-summary-text {
    font-size: 13px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.rule-summary-text:hover {
    background-color: #f5f5f5;
    border-radius: 3px;
}

.rule-summary-toggle {
    transition: all 0.2s ease;
}

.rule-summary-toggle:hover {
    transform: scale(1.05);
}

.rule-summary-truncated {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.archived-badge {
    background-color: #ff9800;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
}
</style>

<div ng-controller="ArchivedRulesList" class="page">
    <div class="row ui-section mb-0">
        <div class="col-md-12">
            <article class="article">
                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">
                        <md-toolbar class="md-table-toolbar md-default">
                            <div class="md-toolbar-tools">
                                <i class="material-icons" style="margin-right: 8px;">archive</i>
                                <span>Archived Business Rules</span>
                                <div flex></div>

                                <a href="#" ng-click="ExportArchivedRulesList()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex;">
                                    <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg"></md-icon>
                                    <span>Export to Excel</span>
                                </a>
                                <a href="#" ng-click="ExportArchivedRulesList()" class="md-button md-raised md-default dis_open_v" style="display: none; min-width: 40px;">
                                    <md-icon class="mt-10 excel_icon" md-svg-src="../assets/images/excel.svg"></md-icon>
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div class="tablemovebtns" style="top:70%;">
                                        <a class="md-button md-raised md-default" id="left-button"><i class="material-icons">keyboard_arrow_left</i></a>
                                    </div>
                                    <div class="tablemovebtns" style="top:70%;">
                                        <a class="md-button md-raised md-default" id="right-button"><i class="material-icons">keyboard_arrow_right</i></a>
                                    </div>

                                    <div ng-show="pagedItems" class="pull-right pageditems">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                        to <span style="font-weight:bold;" ng-show="totalCount >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="totalCount < (currentPage * itemsPerPage) + itemsPerPage">{{totalCount}}</span>
                                        of <span style="font-weight:bold;">{{totalCount}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-container table-responsive" style="overflow: auto;">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr class="th_sorting">
                                                    <!-- 1. Version Name -->
                                                    <th style="cursor:pointer;" ng-click="sort('version_name')" ng-class="{'orderby' : OrderBy == 'version_name'}">
                                                        <div>
                                                            Version Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'version_name'"></i>
                                                            <span ng-show="OrderBy == 'version_name'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 2. Priority -->
                                                    <th style="cursor:pointer;" ng-click="sort('priority')" ng-class="{'orderby' : OrderBy == 'priority'}">
                                                        <div>
                                                            Priority <i class="fa fa-sort pull-right" ng-show="OrderBy != 'priority'"></i>
                                                            <span ng-show="OrderBy == 'priority'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 3. Rule Name -->
                                                    <th style="cursor:pointer;" ng-click="sort('rule_name')" ng-class="{'orderby' : OrderBy == 'rule_name'}">
                                                        <div>
                                                            Rule Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'rule_name'"></i>
                                                            <span ng-show="OrderBy == 'rule_name'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 4. Description -->
                                                    <th style="cursor:pointer;" ng-click="sort('rule_description')" ng-class="{'orderby' : OrderBy == 'rule_description'}">
                                                        <div>
                                                            Description <i class="fa fa-sort pull-right" ng-show="OrderBy != 'rule_description'"></i>
                                                            <span ng-show="OrderBy == 'rule_description'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 5. Customer -->
                                                    <th style="cursor:pointer;" ng-click="sort('CustomerName')" ng-class="{'orderby' : OrderBy == 'CustomerName'}">
                                                        <div>
                                                            Customer <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CustomerName'"></i>
                                                            <span ng-show="OrderBy == 'CustomerName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <!-- 6. Facility -->
                                                    <th style="cursor:pointer;" ng-click="sort('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">
                                                        <div>
                                                            Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>
                                                            <span ng-show="OrderBy == 'FacilityName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 7. Workflow -->
                                                    <th style="cursor:pointer;" ng-click="sort('workflow')" ng-class="{'orderby' : OrderBy == 'workflow'}">
                                                        <div>
                                                            Workflow <i class="fa fa-sort pull-right" ng-show="OrderBy != 'workflow'"></i>
                                                            <span ng-show="OrderBy == 'workflow'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 8. Part Type -->
                                                    <th style="cursor:pointer;" ng-click="sort('part_types')" ng-class="{'orderby' : OrderBy == 'part_types'}">
                                                        <div>
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'part_types'"></i>
                                                            <span ng-show="OrderBy == 'part_types'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 9. Rule Summary -->
                                                    <th style="cursor:pointer;" ng-click="sort('rule_summary')" ng-class="{'orderby' : OrderBy == 'rule_summary'}">
                                                        <div style="min-width: 300px; max-width: 350px;">
                                                            Rule Summary <i class="fa fa-sort pull-right" ng-show="OrderBy != 'rule_summary'"></i>
                                                            <span ng-show="OrderBy == 'rule_summary'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 10. Disposition -->
                                                    <th style="cursor:pointer;" ng-click="sort('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">
                                                        <div>
                                                            Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>
                                                            <span ng-show="OrderBy == 'disposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 11. Rule ID -->
                                                    <th style="cursor:pointer;" ng-click="sort('rule_id')" ng-class="{'orderby' : OrderBy == 'rule_id'}">
                                                        <div style="min-width: 80px;">
                                                            Rule ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'rule_id'"></i>
                                                            <span ng-show="OrderBy == 'rule_id'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                    <!-- 12. Source Type -->
                                                    <th style="cursor:pointer;" ng-click="sort('SourceTypeName')" ng-class="{'orderby' : OrderBy == 'SourceTypeName'}">
                                                        <div>
                                                            Source Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'SourceTypeName'"></i>
                                                            <span ng-show="OrderBy == 'SourceTypeName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 13. Material Type -->
                                                    <th style="cursor:pointer;" ng-click="sort('MaterialTypeName')" ng-class="{'orderby' : OrderBy == 'MaterialTypeName'}">
                                                        <div>
                                                            Material Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'MaterialTypeName'"></i>
                                                            <span ng-show="OrderBy == 'MaterialTypeName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 14. Created Date -->
                                                    <th style="cursor:pointer;" ng-click="sort('created_date')" ng-class="{'orderby' : OrderBy == 'created_date'}">
                                                        <div style="min-width: 120px;">
                                                            Created Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'created_date'"></i>
                                                            <span ng-show="OrderBy == 'created_date'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 15. Created By -->
                                                    <th style="cursor:pointer;" ng-click="sort('created_by')" ng-class="{'orderby' : OrderBy == 'created_by'}">
                                                        <div style="min-width: 100px;">
                                                            Created By <i class="fa fa-sort pull-right" ng-show="OrderBy != 'created_by'"></i>
                                                            <span ng-show="OrderBy == 'created_by'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 16. Updated Date -->
                                                    <th style="cursor:pointer;" ng-click="sort('updated_date')" ng-class="{'orderby' : OrderBy == 'updated_date'}">
                                                        <div style="min-width: 120px;">
                                                            Updated Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'updated_date'"></i>
                                                            <span ng-show="OrderBy == 'updated_date'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 17. Updated By -->
                                                    <th style="cursor:pointer;" ng-click="sort('updated_by')" ng-class="{'orderby' : OrderBy == 'updated_by'}">
                                                        <div style="min-width: 100px;">
                                                            Updated By <i class="fa fa-sort pull-right" ng-show="OrderBy != 'updated_by'"></i>
                                                            <span ng-show="OrderBy == 'updated_by'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                </tr>

                                                <tr class="errornone">
                                                    <!-- 1. Version Name -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="version_name" ng-model="filter_text[0].version_name" ng-change="MakeFilter()" aria-label="Version Name Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 2. Priority -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="priority" ng-model="filter_text[0].priority" ng-change="MakeFilter()" aria-label="Priority Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 3. Rule Name -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="rule_name" ng-model="filter_text[0].rule_name" ng-change="MakeFilter()" aria-label="Rule Name Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 4. Description -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="rule_description" ng-model="filter_text[0].rule_description" ng-change="MakeFilter()" aria-label="Description Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 5. Customer -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="CustomerName" ng-model="filter_text[0].CustomerName" ng-change="MakeFilter()" aria-label="Customer Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].CustomerName='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].CustomerName=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 6. Facility -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="Facility Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].FacilityName='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].FacilityName=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 7. Workflow -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="workflow" ng-model="filter_text[0].workflow" ng-change="MakeFilter()" aria-label="Workflow Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].workflow='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].workflow=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 8. Part Type -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="part_types" ng-model="filter_text[0].part_types" ng-change="MakeFilter()" aria-label="Part Types Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].part_types='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].part_types=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 9. Rule Summary -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="rule_summary" ng-model="filter_text[0].rule_summary" ng-change="MakeFilter()" aria-label="Rule Summary Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 10. Disposition -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()" aria-label="Disposition Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 11. Rule ID -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="rule_id_text" ng-model="filter_text[0].rule_id_text" ng-change="MakeFilter()" aria-label="Rule ID Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 12. Source Type -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="SourceTypeName" ng-model="filter_text[0].SourceTypeName" ng-change="MakeFilter()" aria-label="Source Type Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].SourceTypeName='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].SourceTypeName=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 13. Material Type -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="MaterialType" ng-model="filter_text[0].MaterialType" ng-change="MakeFilter()" aria-label="Material Type Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].MaterialType='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].MaterialType=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 14. Created Date -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="created_date" ng-model="filter_text[0].created_date" ng-change="MakeFilter()" aria-label="Created Date Filter" placeholder="YYYY-MM-DD" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 15. Created By -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="created_by" ng-model="filter_text[0].created_by" ng-change="MakeFilter()" aria-label="Created By Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 16. Updated Date -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="updated_date" ng-model="filter_text[0].updated_date" ng-change="MakeFilter()" aria-label="Updated Date Filter" placeholder="YYYY-MM-DD" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 17. Updated By -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="updated_by" ng-model="filter_text[0].updated_by" ng-change="MakeFilter()" aria-label="Updated By Filter" />
                                                        </md-input-container>
                                                    </td>
                                                </tr>
                                            </thead>
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="rule in pagedItems">
                                                    <td>{{rule.version_name}}</td>
                                                    <td>{{rule.priority}}</td>
                                                    <td>{{rule.rule_name}}</td>
                                                    <td>{{rule.rule_description}}</td>
                                                    <td>{{getCustomerNamesFromIds(rule.AWSCustomerID)}}</td>
                                                    <td>{{rule.FacilityName || (rule.FacilityID == 'All' ? 'All' : rule.FacilityID)}}</td>
                                                    <td>{{rule.workflow || (rule.workflow_id == 'All' ? 'All' : rule.workflow_id)}}</td>
                                                    <td>{{rule.part_types}}</td>
                                                    <td class="rule-summary-cell">
                                                        <div class="rule-summary-text" ng-click="showRuleDetails(rule, $event)">
                                                            <div class="rule-summary-truncated">{{rule.rule_summary}}</div>
                                                        </div>
                                                    </td>
                                                    <td>{{rule.disposition}}</td>
                                                    <td>{{rule.rule_id}}</td>
                                                    <td>{{rule.SourceTypeName || (rule.idCustomertype == 'All' ? 'All' : rule.idCustomertype)}}</td>
                                                    <td>{{rule.MaterialTypeName || (rule.MaterialType == 'All' ? 'All' : rule.MaterialType)}}</td>
                                                    <td>{{rule.created_date | date:'MM/dd/yyyy HH:mm'}}</td>
                                                    <td>{{rule.created_by}}</td>
                                                    <td>{{rule.updated_date | date:'MM/dd/yyyy HH:mm'}}</td>
                                                    <td>{{rule.updated_by}}</td>
                                                </tr>
                                            </tbody>
                                            <tbody ng-show="pagedItems.length == 0 && !busy">
                                                <tr>
                                                    <td colspan="17" class="text-center" style="padding: 40px;">
                                                        <i class="material-icons" style="font-size: 48px; color: #ccc;">archive</i>
                                                        <h4 style="color: #999; margin-top: 10px;">No archived rules found</h4>
                                                        <p style="color: #999;">There are no archived rules matching your current filters.</p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="17">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </md-card>

                </div>

            </article>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $("#left-button").css("display", "none");
        });
    </script>

    <script>

        $("#right-button").click(function() {
        event.preventDefault();
        $(".table-responsive").animate(
            {
            scrollLeft: "+=300px"
            },
            "slow"
        );
        });

        $("#left-button").click(function() {
        event.preventDefault();
        $(".table-responsive").animate(
            {
            scrollLeft: "-=300px"
            },
            "slow"
        );
        });


        let rightBtn = $('#right-button');
        let leftBtn = $('#left-button');

        $('.table-container').on('scroll', function (e) {
            let el = e.currentTarget;
            let sl = el.scrollLeft;
            let cw = el.clientWidth;
            let sw = el.scrollWidth;

            let showRightBtn = sw !== sl + cw;
            let showLeftBtn = sl !== 0;

            showRightBtn ? rightBtn.fadeIn() : rightBtn.fadeOut();
            showLeftBtn ? leftBtn.fadeIn() : leftBtn.fadeOut();
        });


    </script>

</div>

<!-- Rule Details Modal Template -->
<script type="text/ng-template" id="archivedRuleDetailsModal.html">
    <md-dialog aria-label="Archived Rule Details" style="min-width: 600px; max-width: 800px;">
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>
                    <i class="material-icons" style="margin-right: 8px;">archive</i>
                    Archived Rule Details
                </h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="cancel()">
                    <md-icon class="material-icons">close</md-icon>
                </md-button>
            </div>
        </md-toolbar>
        <md-dialog-content style="padding: 20px;">
            <div class="md-dialog-content">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Rule Name:</strong> {{ruleData.rule_name}}
                    </div>
                    <div class="col-md-6">
                        <strong>Version:</strong> {{ruleData.version_name}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-12">
                        <strong>Description:</strong> {{ruleData.rule_description}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-6">
                        <strong>Priority:</strong> {{ruleData.priority}}
                    </div>
                    <div class="col-md-6">
                        <strong>Disposition:</strong> {{ruleData.disposition}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-6">
                        <strong>Customer:</strong> {{getCustomerNamesFromIds(ruleData.AWSCustomerID)}}
                    </div>
                    <div class="col-md-6">
                        <strong>Facility:</strong> {{ruleData.FacilityName || (ruleData.FacilityID == 'All' ? 'All' : ruleData.FacilityID)}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-6">
                        <strong>Workflow:</strong> {{ruleData.workflow || (ruleData.workflow_id == 'All' ? 'All' : ruleData.workflow_id)}}
                    </div>
                    <div class="col-md-6">
                        <strong>Part Types:</strong> {{ruleData.part_types}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-12">
                        <strong>Rule Summary:</strong>
                        <div style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; margin-top: 5px;">
                            {{ruleData.rule_summary}}
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-6">
                        <strong>Created:</strong> {{ruleData.created_date | date:'MM/dd/yyyy HH:mm'}}
                    </div>
                    <div class="col-md-6">
                        <strong>Created By:</strong> {{ruleData.created_by}}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;" ng-show="ruleData.updated_date">
                    <div class="col-md-6">
                        <strong>Updated:</strong> {{ruleData.updated_date | date:'MM/dd/yyyy HH:mm'}}
                    </div>
                    <div class="col-md-6">
                        <strong>Updated By:</strong> {{ruleData.updated_by}}
                    </div>
                </div>
            </div>
        </md-dialog-content>
        <md-dialog-actions layout="row">
            <span flex></span>
            <md-button ng-click="cancel()" class="md-primary">
                Close
            </md-button>
        </md-dialog-actions>
    </md-dialog>
</script>
