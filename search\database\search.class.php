<?php
session_start();
include_once("../../connection.php");
include_once("../../common_functions.php");
class SearchClass extends CommonClass {
	public $responseParameters;
	public $connectionlink;
	public function __construct(){
		$this->connectionlink = Connection::DBConnect1();
	}

	public function GetDispositionList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			$query = "select disposition_id as dispositionid,disposition from disposition where status = 'Active' order by disposition ASC";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Disposition available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetManufactureList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			$query = "select idManufacturer,ManufacturerName from manufacturer";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Manufacturer available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetBinList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			$query = "select CustomPalletID,BinName from custompallet";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Bins available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function ValidateInputTicketID($data){
		$json = array(
			'Success' => false,
			'Result' => $data['input']
		);
		if($data['input'] != ''){
			$query = "select LoadId from loads where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['input'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_affected_rows($this->connectionlink) > 0){
				$json = array(
					'InputSuccess' => true,
					'Result' => $data['input']
				);
			}else{
				$query = "select ShippingID from shipping where ShippingID='".mysqli_real_escape_string($this->connectionlink,$data['input'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_affected_rows($this->connectionlink) > 0){
					$json = array(
						'OutputSuccess' => true,
						'Result' => $data['input']
					);
				}
			}
		}
		return json_encode($json);
	}

	public function ValidateOutboundTicketID($data){
		$json = array(
			'Success' => false,
			'Result' => $data['input']
		);
		if($data['input'] != ''){
			$query = "select ShippingID from shipping where ShippingID='".mysqli_real_escape_string($this->connectionlink,$data['input'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_affected_rows($this->connectionlink) > 0){
				$json = array(
					'OBSuccess' => true,
					'Result' => $data['input']
				);
			}else{
				$query = "select LoadId from loads where LoadId='".mysqli_real_escape_string($this->connectionlink,$data['input'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_affected_rows($this->connectionlink) > 0){
					$json = array(
						'IBSuccess' => true,
						'Result' => $data['input']
					);
				}
			}
		}
		return json_encode($json);
	}

	public function ValidateIBContainerID($data){
		$json = array(
			'Success' => false,
			'Result' => $data['input']
		);
		if($data['input'] != ''){
			$query = "select idPallet from pallets where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['input'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_affected_rows($this->connectionlink) > 0){
				$json = array(
					'IBSuccess' => true,
					'Result' => $data['input']
				);
			}else{
				$query = "select ShippingContainerID from shipping_containers where ShippingContainerID='".mysqli_real_escape_string($this->connectionlink,$data['input'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_affected_rows($this->connectionlink) > 0){
					$json = array(
						'OBSuccess' => true,
						'Result' => $data['input']
					);
				}
			}
		}
		return json_encode($json);
	}

	public function ValidateOBContainerID($data){
		$json = array(
			'Success' => false,
			'Result' => $data['input']
		);
		if($data['input'] != ''){
			$query = "select ShippingContainerID from shipping_containers where ShippingContainerID='".mysqli_real_escape_string($this->connectionlink,$data['input'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_affected_rows($this->connectionlink) > 0){
				$json = array(
					'OBSuccess' => true,
					'Result' => $data['input']
				);
			}else{
				$query = "select idPallet from pallets where idPallet='".mysqli_real_escape_string($this->connectionlink,$data['input'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_affected_rows($this->connectionlink) > 0){
					$json = array(
						'IBSuccess' => true,
						'Result' => $data['input']
					);
				}
			}
		}
		return json_encode($json);
	}

	public function SearchInStock($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['assetscanid']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			$CurrentDate = date('Y-m-d');
			if($data['ARDatefrom'] != '')
			{
				$chkdt = $data['ARDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ARDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ARDateto'] != '')
			{
				$chkdt = $data['ARDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ARDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ASDatefrom'] != '')
			{
				$chkdt = $data['ASDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ASDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ASDateto'] != '')
			{
				$chkdt = $data['ASDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ASDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ARDateto'] == '')
			{
				//$data['ARDateto1'] = date('Y-m-d H:i', strtotime($data['ARDatefrom'] . ' + 1 day'));
				$data['ARDateto1'] = date('Y-m-d',strtotime($CurrentDate .' + 1 day'));
			}
			else
			{
				$data['ARDateto1'] = date('Y-m-d H:i', strtotime($data['ARDateto'] . ' + 1 day'));
			}
			if($data['ASDateto'] == '')
			{
				//$data['ASDateto1'] = date('Y-m-d H:i', strtotime($data['ASDatefrom'] . ' + 1 day'));
				$data['ASDateto1'] = date('Y-m-d',strtotime($CurrentDate .' + 1 day'));
			}
			else
			{
				$data['ASDateto1'] = date('Y-m-d H:i', strtotime($data['ASDateto'] . ' + 1 day'));
			}

			//prepare select items for query
			$SELECT = "";
			/*if($data['assetsnstatus'] == 'true')
			{
				$SELECT = $SELECT.", s.StatusName as `SN Status`";
			}*/
			if($data['assetbintype'] == 'true')
			{
				$SELECT = $SELECT.", CP.BinType as `Bin Type`";
			}
			if($data['assetbinid'] == 'true')
			{
				$SELECT = $SELECT.", CP.BinName as `Bin ID`";
			}
			if($data['assetlocationid'] == 'true')
			{
				$SELECT = $SELECT.", LOC.LocationName as `Location ID`";
			}
			if($data['assetsourcecus'] == 'true')
			{
				$SELECT = $SELECT.", SC.CustomerName as `Source`";
			}
			if($data['assetsourcetype'] == 'true')
			{
				$SELECT = $SELECT.", SCT.Cumstomertype as `Source Type`";
			}
			/*if($data['assetparttype'] == 'true')
			{
				$sql1 = $sql1.", pt.parttype as `Part Type`";
			}*/
			if($data['assetTicketid'] == 'true')
			{
				$SELECT = $SELECT.", L.LoadId as `Inbound Ticket ID`";
			}
			if($data['assetcid'] == 'true')
			{
				$SELECT = $SELECT.", P.idPallet as `Inbound Container ID`";
			}
			/*if($data['assetinboundremovalcode'] == 'true')
			{
				$sql1 = $sql1.", P.idPallet as `Inbound Container ID`";
			}*/
			if($data['assetouboundticket'] == 'true')
			{
				$SELECT = $SELECT.", SCO.ShippingID as `Outbound Ticket ID`";
			}
			if($data['assetouboundcontainer'] == 'true')
			{
				$SELECT = $SELECT.", SCO.ShippingContainerID as `Outbound Container ID`";
			}
			if($data['assetouboundRemovalType'] == 'true')
			{
				$SELECT = $SELECT.", DI.disposition as `Outbound Removal Type`";
			}
			if($data['assetobcstatus'] == 'true')
			{
				$SELECT = $SELECT.", ShS.Status as `Outbound Container Status`";
			}
			if($data['assetmpn'] == 'true')
			{
				$SELECT = $SELECT.", A.UniversalModelNumber as `MPN`";
			}
			if($data['assetdisposition'] == 'true')
			{
				$SELECT = $SELECT.", DI.disposition as `Disposition`";
			}
			if($data['assetfacility'] == 'true')
			{
				$SELECT = $SELECT.", F.FacilityName as `Facility`";
			}
			/*if($data['assetcreateddate'] == 'true')
			{
				$sql1 = $sql1.", A.CommittedDate as `Created Date`";
			}*/
			if($data['assetcreatedby'] == 'true')
			{
				$SELECT = $SELECT.", U.FirstName as `CreatedBy FirstName`, U.LastName as `CreatedBy LastName`";
			}
			if($data['assetlastdate'] == 'true')
			{
				$SELECT = $SELECT.", A.DateUpdated as `Last Touch Date`";
			}
			if($data['assetlasttouchby'] == 'true')
			{
				$SELECT = $SELECT.", UM.FirstName as `LastTouchBy FirstName`, UM.LastName as `LastTouchBy LastName`";
			}
			if($data['assetshippingdate'] == 'true')
			{
				$SELECT = $SELECT.", SH.ShippedDate as `Shipping Date`";
			}
			if($data['assetshippingby'] == 'true')
			{
				$SELECT = $SELECT.", su.FirstName as `ShippedBy FirstName`, su.LastName as `ShippedBy LastName`";
			}

			$SELECTServer = "";
			/*if($data['assetsnstatus'] == 'true')
			{
				$SELECT = $SELECT.", s.StatusName as `SN Status`";
			}*/
			if($data['assetbintype'] == 'true')
			{
				$SELECTServer = $SELECTServer.", CP.BinType as `Bin Type`";
			}
			if($data['assetbinid'] == 'true')
			{
				$SELECTServer = $SELECTServer.", CP.BinName as `Bin ID`";
			}
			if($data['assetlocationid'] == 'true')
			{
				$SELECTServer = $SELECTServer.", LOC.LocationName as `Location ID`";
			}
			if($data['assetsourcecus'] == 'true')
			{
				$SELECTServer = $SELECTServer.", SC.CustomerName as `Source`";
			}
			if($data['assetsourcetype'] == 'true')
			{
				$SELECTServer = $SELECTServer.", SCT.Cumstomertype as `Source Type`";
			}
			/*if($data['assetparttype'] == 'true')
			{
				$sql1 = $sql1.", pt.parttype as `Part Type`";
			}*/
			if($data['assetTicketid'] == 'true')
			{
				$SELECTServer = $SELECTServer.", L.LoadId as `Inbound Ticket ID`";
			}
			if($data['assetcid'] == 'true')
			{
				$SELECTServer = $SELECTServer.", P.idPallet as `Inbound Container ID`";
			}
			/*if($data['assetinboundremovalcode'] == 'true')
			{
				$sql1 = $sql1.", P.idPallet as `Inbound Container ID`";
			}*/
			if($data['assetouboundticket'] == 'true')
			{
				$SELECTServer = $SELECTServer.", SCO.ShippingID as `Outbound Ticket ID`";
			}
			if($data['assetouboundcontainer'] == 'true')
			{
				$SELECTServer = $SELECTServer.", SCO.ShippingContainerID as `Outbound Container ID`";
			}
			if($data['assetouboundRemovalType'] == 'true')
			{
				$SELECTServer = $SELECTServer.", DI.disposition as `Outbound Removal Type`";
			}
			if($data['assetobcstatus'] == 'true')
			{
				$SELECTServer = $SELECTServer.", ShS.Status as `Outbound Container Status`";
			}
			if($data['assetmpn'] == 'true')
			{
				$SELECTServer = $SELECTServer.", A.MPN as `MPN`";
			}
			if($data['assetdisposition'] == 'true')
			{
				$SELECTServer = $SELECTServer.", DI.disposition as `Disposition`";
			}
			if($data['assetfacility'] == 'true')
			{
				$SELECTServer = $SELECTServer.", F.FacilityName as `Facility`";
			}
			/*if($data['assetcreateddate'] == 'true')
			{
				$sql1 = $sql1.", A.CommittedDate as `Created Date`";
			}*/
			if($data['assetcreatedby'] == 'true')
			{
				$SELECTServer = $SELECTServer.", U.FirstName as `CreatedBy FirstName`, U.LastName as `CreatedBy LastName`";
			}
			if($data['assetlastdate'] == 'true')
			{
				$SELECTServer = $SELECTServer.", A.UpdatedDate as `Last Touch Date`";
			}
			if($data['assetlasttouchby'] == 'true')
			{
				$SELECTServer = $SELECTServer.", UM.FirstName as `LastTouchBy FirstName`, UM.LastName as `LastTouchBy LastName`";
			}
			if($data['assetshippingdate'] == 'true')
			{
				$SELECTServer = $SELECTServer.", SH.ShippedDate as `Shipping Date`";
			}
			if($data['assetshippingby'] == 'true')
			{
				$SELECTServer = $SELECTServer.", su.FirstName as `ShippedBy FirstName`, su.LastName as `ShippedBy LastName`";
			}

			$SELECTMedia = "";
			/*if($data['assetsnstatus'] == 'true')
			{
				$SELECT = $SELECT.", s.StatusName as `SN Status`";
			}*/
			if($data['assetbintype'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", CP.BinType as `Bin Type`";
			}
			if($data['assetbinid'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", CP.BinName as `Bin ID`";
			}
			if($data['assetlocationid'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", LOC.LocationName as `Location ID`";
			}
			if($data['assetsourcecus'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", SC.CustomerName as `Source`";
			}
			if($data['assetsourcetype'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", SCT.Cumstomertype as `Source Type`";
			}
			/*if($data['assetparttype'] == 'true')
			{
				$sql1 = $sql1.", pt.parttype as `Part Type`";
			}*/
			if($data['assetTicketid'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", L.LoadId as `Inbound Ticket ID`";
			}
			if($data['assetcid'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", P.idPallet as `Inbound Container ID`";
			}
			/*if($data['assetinboundremovalcode'] == 'true')
			{
				$sql1 = $sql1.", P.idPallet as `Inbound Container ID`";
			}*/
			if($data['assetouboundticket'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", SCO.ShippingID as `Outbound Ticket ID`";
			}
			if($data['assetouboundcontainer'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", SCO.ShippingContainerID as `Outbound Container ID`";
			}
			if($data['assetouboundRemovalType'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", DI.disposition as `Outbound Removal Type`";
			}
			if($data['assetobcstatus'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", ShS.Status as `Outbound Container Status`";
			}
			if($data['assetmpn'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", A.MediaMPN as `MPN`";
			}
			if($data['assetdisposition'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", DI.disposition as `Disposition`";
			}
			if($data['assetfacility'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", F.FacilityName as `Facility`";
			}
			/*if($data['assetcreateddate'] == 'true')
			{
				$sql1 = $sql1.", A.CommittedDate as `Created Date`";
			}*/
			if($data['assetcreatedby'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", U.FirstName as `CreatedBy FirstName`, U.LastName as `CreatedBy LastName`";
			}
			if($data['assetlastdate'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", A.UpdatedDate as `Last Touch Date`";
			}
			if($data['assetlasttouchby'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", UM.FirstName as `LastTouchBy FirstName`, UM.LastName as `LastTouchBy LastName`";
			}
			if($data['assetshippingdate'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", SH.ShippedDate as `Shipping Date`";
			}
			if($data['assetshippingby'] == 'true')
			{
				$SELECTMedia = $SELECTMedia.", su.FirstName as `ShippedBy FirstName`, su.LastName as `ShippedBy LastName`";
			}

			// prepare where condition for query
			$WHERE = "";
			if($data['LoadID'] != '')
				$WHERE = $WHERE." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";


			if($data['idCustomer'] != '')
			{
				$idCustomer = '';
				for($i=0;$i<count($data['idCustomer']);$i++) {
					$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
				}
				$idCustomer = rtrim($idCustomer, ",");
				$WHERE = $WHERE." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
			}

			if($data['FacilityID'] != '')
			{
				$FacilityID = '';
				for($i=0;$i<count($data['FacilityID']);$i++) {
					$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
				}
				$FacilityID = rtrim($FacilityID, ",");
				$WHERE = $WHERE." AND A.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
			}

			if($data['ContainerID'] != '')
				$WHERE = $WHERE." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";

			if($data['ShippingID'] != '')
				$WHERE = $WHERE." AND SH.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";

			if($data['OBContainerID'] != '')
				$WHERE = $WHERE." AND SCO.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['OBContainerID'])."'";

			if($data['locationid'] != '')
				$WHERE = $WHERE." AND LOC.LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['locationid'])."'";

			if($data['ARDatefrom'] != '')
				$WHERE = $WHERE." AND A.DateCreated between '".mysqli_real_escape_string($this->connectionlink,$data['ARDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ARDateto1'])."'";

			if($data['ASDatefrom'] != '')
				$WHERE = $WHERE." AND A.ShippedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ASDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ASDateto1'])."'";

			if($data['SerialNumber'] != ''){
				// Split the comma-separated values into an array
				$SerialsArray = explode(' ', $data['SerialNumber']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$SerialValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $SerialsArray);
				// Join the array back into a string suitable for the IN clause
				$SerialNumbers = implode(',', $SerialValues);
				$WHERE = $WHERE." AND A.SerialNumber IN ( ".$SerialNumbers.")";
			}

			if($data['UniversalModelNumber'] != ''){
				// Split the comma-separated values into an array
				$MPNArray = explode(' ', $data['UniversalModelNumber']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$MPNValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $MPNArray);
				// Join the array back into a string suitable for the IN clause
				$MPNs = implode(',', $MPNValues);

				$WHERE = $WHERE." AND A.UniversalModelNumber IN ( ".$MPNs.")";
			}

			if($data['DispositionID'] != '')
			{
				$dispositionID = '';
				for($i=0;$i<count($data['DispositionID']);$i++) {
					$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
				}
				$dispositionID = rtrim($dispositionID, ",");
				$WHERE = $WHERE." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
			}


			if($data['binid'] != ''){
				// Split the comma-separated values into an array
				$BinArray = explode(' ', $data['binid']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$BinValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $BinArray);
				// Join the array back into a string suitable for the IN clause
				$BinIDs = implode(',', $BinValues);
				$WHERE = $WHERE." AND CP.BinName IN ( ".$BinIDs.")";
			}

			if($data['CustomerType'] != '')
			{
				$CustomerType = '';
				for($i=0;$i<count($data['CustomerType']);$i++) {
					$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
				}
				$CustomerType = rtrim($CustomerType, ",");
				$WHERE = $WHERE." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
			}

			if($data['Parttype'] != '')
			{
				$Parttype = '';
				for($i=0;$i<count($data['Parttype']);$i++) {
					$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
				}
				$Parttype = rtrim($Parttype, ",");
				//$Parttype = str_replace($Parttype, '\'');
				//$Parttype = str_replace('\'',"'",$Parttype);
				$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
				$WHERE = $WHERE." AND A.part_type IN ( ".$Parttype.")";
			}

			$WHEREServer = "";
			if($data['LoadID'] != '')
				$WHEREServer = $WHEREServer." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";


			if($data['idCustomer'] != '')
			{
				$idCustomer = '';
				for($i=0;$i<count($data['idCustomer']);$i++) {
					$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
				}
				$idCustomer = rtrim($idCustomer, ",");
				$WHEREServer = $WHEREServer." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
			}

			if($data['FacilityID'] != '')
			{
				$FacilityID = '';
				for($i=0;$i<count($data['FacilityID']);$i++) {
					$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
				}
				$FacilityID = rtrim($FacilityID, ",");
				$WHEREServer = $WHEREServer." AND P.PalletFacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
			}

			if($data['ContainerID'] != '')
				$WHEREServer = $WHEREServer." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";

			if($data['ShippingID'] != '')
				$WHEREServer = $WHEREServer." AND SH.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";

			if($data['OBContainerID'] != '')
				$WHEREServer = $WHEREServer." AND SCO.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['OBContainerID'])."'";

			if($data['locationid'] != '')
				$WHEREServer = $WHEREServer." AND LOC.LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['locationid'])."'";

			if($data['ARDatefrom'] != '')
				$WHEREServer = $WHEREServer." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ARDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ARDateto1'])."'";

			if($data['ASDatefrom'] != '')
				$WHEREServer = $WHEREServer." AND A.ShippedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ASDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ASDateto1'])."'";

			if($data['SerialNumber'] != ''){
				// Split the comma-separated values into an array
				$SerialsArray = explode(' ', $data['SerialNumber']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$SerialValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $SerialsArray);
				// Join the array back into a string suitable for the IN clause
				$SerialNumbers = implode(',', $SerialValues);
				$WHEREServer = $WHEREServer." AND A.ServerSerialNumber IN ( ".$SerialNumbers.")";
			}

			if($data['UniversalModelNumber'] != ''){
				// Split the comma-separated values into an array
				$MPNArray = explode(' ', $data['UniversalModelNumber']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$MPNValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $MPNArray);
				// Join the array back into a string suitable for the IN clause
				$MPNs = implode(',', $MPNValues);

				$WHEREServer = $WHEREServer." AND A.MPN IN ( ".$MPNs.")";
			}

			if($data['DispositionID'] != '')
			{
				$dispositionID = '';
				for($i=0;$i<count($data['DispositionID']);$i++) {
					$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
				}
				$dispositionID = rtrim($dispositionID, ",");
				$WHEREServer = $WHEREServer." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
			}


			if($data['binid'] != ''){
				// Split the comma-separated values into an array
				$BinArray = explode(' ', $data['binid']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$BinValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $BinArray);
				// Join the array back into a string suitable for the IN clause
				$BinIDs = implode(',', $BinValues);
				$WHEREServer = $WHEREServer." AND CP.BinName IN ( ".$BinIDs.")";
			}

			if($data['CustomerType'] != '')
			{
				$CustomerType = '';
				for($i=0;$i<count($data['CustomerType']);$i++) {
					$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
				}
				$CustomerType = rtrim($CustomerType, ",");
				$WHEREServer = $WHEREServer." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
			}

			if($data['Parttype'] != '')
			{
				$Parttype = '';
				for($i=0;$i<count($data['Parttype']);$i++) {
					$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
				}
				$Parttype = rtrim($Parttype, ",");
				//$Parttype = str_replace($Parttype, '\'');
				//$Parttype = str_replace('\'',"'",$Parttype);
				$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
				$WHEREServer = $WHEREServer." AND A.Type IN ( ".$Parttype.")";
			}

			$WHEREMedia = "";
			if($data['LoadID'] != '')
				$WHEREMedia = $WHEREMedia." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";


			if($data['idCustomer'] != '')
			{
				$idCustomer = '';
				for($i=0;$i<count($data['idCustomer']);$i++) {
					$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
				}
				$idCustomer = rtrim($idCustomer, ",");
				$WHEREMedia = $WHEREMedia." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
			}

			if($data['FacilityID'] != '')
			{
				$FacilityID = '';
				for($i=0;$i<count($data['FacilityID']);$i++) {
					$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
				}
				$FacilityID = rtrim($FacilityID, ",");
				$WHEREMedia = $WHEREMedia." AND P.PalletFacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
			}

			if($data['ContainerID'] != '')
				$WHEREMedia = $WHEREMedia." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";

			if($data['ShippingID'] != '')
				$WHEREMedia = $WHEREMedia." AND SH.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";

			if($data['OBContainerID'] != '')
				$WHEREMedia = $WHEREMedia." AND SCO.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['OBContainerID'])."'";

			if($data['locationid'] != '')
				$WHEREMedia = $WHEREMedia." AND LOC.LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['locationid'])."'";

			if($data['ARDatefrom'] != '')
				$WHEREMedia = $WHEREMedia." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ARDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ARDateto1'])."'";

			if($data['ASDatefrom'] != '')
				$WHEREMedia = $WHEREMedia." AND A.ShippedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ASDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ASDateto1'])."'";

			if($data['SerialNumber'] != ''){
				// Split the comma-separated values into an array
				$SerialsArray = explode(' ', $data['SerialNumber']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$SerialValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $SerialsArray);
				// Join the array back into a string suitable for the IN clause
				$SerialNumbers = implode(',', $SerialValues);
				$WHEREMedia = $WHEREMedia." AND A.MediaSerialNumber IN ( ".$SerialNumbers.")";
			}

			if($data['UniversalModelNumber'] != ''){
				// Split the comma-separated values into an array
				$MPNArray = explode(' ', $data['UniversalModelNumber']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$MPNValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $MPNArray);
				// Join the array back into a string suitable for the IN clause
				$MPNs = implode(',', $MPNValues);

				$WHEREMedia = $WHEREMedia." AND A.MediaMPN IN ( ".$MPNs.")";
			}

			if($data['DispositionID'] != '')
			{
				$dispositionID = '';
				for($i=0;$i<count($data['DispositionID']);$i++) {
					$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
				}
				$dispositionID = rtrim($dispositionID, ",");
				$WHEREMedia = $WHEREMedia." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
			}


			if($data['binid'] != ''){
				// Split the comma-separated values into an array
				$BinArray = explode(' ', $data['binid']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$BinValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $BinArray);
				// Join the array back into a string suitable for the IN clause
				$BinIDs = implode(',', $BinValues);
				$WHEREMedia = $WHEREMedia." AND CP.BinName IN ( ".$BinIDs.")";
			}

			if($data['CustomerType'] != '')
			{
				$CustomerType = '';
				for($i=0;$i<count($data['CustomerType']);$i++) {
					$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
				}
				$CustomerType = rtrim($CustomerType, ",");
				$WHEREMedia = $WHEREMedia." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
			}

			if($data['Parttype'] != '')
			{
				$Parttype = '';
				for($i=0;$i<count($data['Parttype']);$i++) {
					$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
				}
				$Parttype = rtrim($Parttype, ",");
				//$Parttype = str_replace($Parttype, '\'');
				//$Parttype = str_replace('\'',"'",$Parttype);
				$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
				$WHEREMedia = $WHEREMedia." AND A.MediaType IN ( ".$Parttype.")";
			}

				// get data from Rack records
				$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`,A.disposition_id,wi.workflow as EvaluationResultID";
				if($data['assetsourcecus'] != 'true')
				{
					$RackRecoverySQL = $RackRecoverySQL.",SC.CustomerName as `Source`";
				}
				if($data['assetmpn'] != 'true')
				{
					$RackRecoverySQL = $RackRecoverySQL.",A.UniversalModelNumber as `MPN`";
				}
				$RackRecoverySQL = $RackRecoverySQL.$SELECT;
				$RackRecoveryServerSQL = "select A.ServerSerialNumber as `SerialNumber`, A.Type as `Part Type`, A.CreatedDate as `Created Date`,A.disposition_id,'Rack Recovery' as EvaluationResultID";
				if($data['assetsourcecus'] != 'true')
				{
					$RackRecoveryServerSQL = $RackRecoveryServerSQL.",SC.CustomerName as `Source`";
				}
				if($data['assetmpn'] != 'true')
				{
					$RackRecoveryServerSQL = $RackRecoveryServerSQL.",A.MPN as `MPN`";
				}
				$RackRecoveryServerSQL = $RackRecoveryServerSQL.$SELECTServer;
				$RackRecoveryMediaSQL = "select A.MediaSerialNumber as `SerialNumber`, A.MediaType as `Part Type`, A.CreatedDate as `Created Date`,A.disposition_id,'Assembly Recovery' as EvaluationResultID";
				if($data['assetsourcecus'] != 'true')
				{
					$RackRecoveryMediaSQL = $RackRecoveryMediaSQL.",SC.CustomerName as `Source`";
				}
				if($data['assetmpn'] != 'true')
				{
					$RackRecoveryMediaSQL = $RackRecoveryMediaSQL.",A.MediaMPN as `MPN`";
				}
				$RackRecoveryMediaSQL = $RackRecoveryMediaSQL.$SELECTMedia;
				if($data['SearchType'] == 'In Stock'){
					$RackRecoveryMainBodyQuery = " From asset A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN workflow wi ON wi.workflow_id = A.RecentWorkflowID
								where (SH.ShipmentStatusID != 3 OR SH.ShipmentStatusID is NULL)";

					$RackRecoveryMainRackBodyQuery = " From speed_server_recovery A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = P.PalletFacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.ServerSerialNumber = A.ServerSerialNumber
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								where (SH.ShipmentStatusID != 3 OR SH.ShipmentStatusID is NULL)";

					$RackRecoveryMainMediaBodyQuery = " From speed_media_recovery A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = P.PalletFacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.MediaSerialNumber = A.MediaSerialNumber
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								where (SH.ShipmentStatusID != 3 OR SH.ShipmentStatusID is NULL)";

				}elseif($data['SearchType'] == 'Removed Shipments'){
					$RackRecoveryMainBodyQuery = " From asset A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN workflow wi ON wi.workflow_id = A.RecentWorkflowID
								where SH.ShipmentStatusID=3
								";

						$RackRecoveryMainRackBodyQuery = " From speed_server_recovery A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = P.PalletFacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.ServerSerialNumber = A.ServerSerialNumber
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								where (SH.ShipmentStatusID != 3 OR SH.ShipmentStatusID is NULL)";

					$RackRecoveryMainMediaBodyQuery = " From speed_media_recovery A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = P.PalletFacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.MediaSerialNumber = A.MediaSerialNumber
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								where (SH.ShipmentStatusID != 3 OR SH.ShipmentStatusID is NULL)";
				}else{
					$RackRecoveryMainBodyQuery = " From asset A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN workflow wi ON wi.workflow_id = A.RecentWorkflowID
								where 1";
				}
				$sqlfinalquery = "select * from (";
				$RackRecoverySQL1 = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE;
				$RackRecoveryServerSQL1 = $RackRecoveryServerSQL.$RackRecoveryMainRackBodyQuery.$WHEREServer;
				$RackRecoveryMediaSQL1 = $RackRecoveryMediaSQL.$RackRecoveryMainMediaBodyQuery.$WHEREMedia;
				$sqlfinalquery = $sqlfinalquery."(".$RackRecoverySQL1.") UNION "."(".$RackRecoveryServerSQL1.") UNION "."(".$RackRecoveryMediaSQL1.")) AS CombinedResults";
				$sqlunionquery = "".$RackRecoverySQL1." UNION "." ".$RackRecoveryServerSQL1." UNION "." ".$RackRecoveryMediaSQL1.") AS CombinedResults";
				
				$RackRecoveryPartTypeCountQuery = "select count(*) as count,`Part Type` FROM (".$sqlunionquery." Group By `Part Type`";
				$RackRecoveryListOfMpnsQuery = "select count(*) as count,MPN FROM (".$sqlunionquery." Group By MPN";
				$RackRecoveryEvaluationResultQuery = "select count(*) as count,EvaluationResultID FROM (".$sqlunionquery." Group By EvaluationResultID";
				$RackRecoveryDispositionQuery = "select count(*) as count,disposition_id FROM (".$sqlunionquery." Group By disposition_id";
				$RackRecoverySourcetypeQuery = "select count(*) as count,`Source` FROM (".$sqlunionquery." Group By `Source`";
							
				if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
					$sql = "select SQL_CALC_FOUND_ROWS * from (".$sqlfinalquery.") as result order by `Created Date` DESC";
					$PartTypeWidgetSql = "select sum(count) as count,`Part Type` as parttype from (".$RackRecoveryPartTypeCountQuery." ) as result group by `Part Type` order by count desc";
					$MPNWidgetSql = "select sum(count) as count,MPN from (".$RackRecoveryListOfMpnsQuery.") as result group by MPN order by count desc";
					$EvaluationResultWidgetSql = "select sum(count) as count,EvaluationResultID as input from (".$RackRecoveryEvaluationResultQuery.") as result group by EvaluationResultID order by count desc";
					$DispositionWidgetSql = "select sum(count) as count,disposition_id from (".$RackRecoveryDispositionQuery.") as result group by disposition_id order by count desc";
					$SourcetypeWidgetSql = "select sum(count) as count,`Source` as Cumstomertype from (".$RackRecoverySourcetypeQuery.") as result group by `Source` order by count desc";
				}else{
					$sql = "select SQL_CALC_FOUND_ROWS * from (".$sqlfinalquery.") as result order by `Created Date` DESC";
					$PartTypeWidgetSql = "select sum(count) as count,`Part Type` as parttype from (".$RackRecoveryPartTypeCountQuery.") as result group by `Part Type` order by count desc";
					$MPNWidgetSql = "select sum(count) as count,MPN from (".$RackRecoveryListOfMpnsQuery.") as result group by MPN order by count desc";
					$EvaluationResultWidgetSql = "select sum(count) as count,EvaluationResultID as input from (".$RackRecoveryEvaluationResultQuery.") as result group by EvaluationResultID order by count desc";
					$DispositionWidgetSql = "select sum(count) as count,disposition_id from (".$RackRecoveryDispositionQuery.") as result group by disposition_id order by count desc";
					$SourcetypeWidgetSql = "select sum(count) as count,`Source` as Cumstomertype from (".$RackRecoverySourcetypeQuery.") as result group by `Source` order by count desc";
				}
				//echo $EvaluationResultWidgetSql;exit;
				//$limit = 10*$data['Currentpage'];
				unset($_SESSION['searchreport_query']);
				$_SESSION['searchreport_query'] = $sql;
				$sql = $sql." LIMIT 0,3";
				//echo $sql;exit;
				/*$json['Success'] = false;
				$json['Result'] = $sql;
				return json_encode($json);*/
				mysqli_query($this->connectionlink,"SET SQL_BIG_SELECTS=1");
				$query = mysqli_query($this->connectionlink,$sql);

				if(mysqli_error($this->connectionlink))
				{
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				else
				{
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$totalcountQ = mysqli_query($this->connectionlink,"select FOUND_ROWS() AS total_records");
						$totalCountRow = mysqli_fetch_assoc($totalcountQ);
						$TotalCount = $totalCountRow['total_records'];
						$i = 0;
						while($row = mysqli_fetch_assoc($query)) {
							$_SESSION['searchcustomer'] = '';
							if($data['CustomerID'] != '')
							{
								$_SESSION['searchcustomer'] = $data['CustomerID'];
							}
							if($data['assetcreatedby'] == 'true')
							{
								//$row['Created By'] = $row['FirstName']." ".$row['LastName'];
								//$row['Created By'] = strtolower($row['Created By']);
							}
							if($data['assetcreateddate'] == 'true')
							{
								$date1 = explode(" ",$row['Created Date']);
								$date2 = explode("-",$date1[0]);
								$date = $date2[1]."/".$date2[2]."/".$date2[0];
								$date = date("M j, Y g:i a", strtotime($row['Created Date']));
								//$time = date("g:i a", strtotime($row['DateCreated']));
								//$row['Created Date'] = $date." ".$time;
								$row['Created Date'] = $date;
								//unset($row['DateCreated']);
							}
							if($data['assetcreatedby'] == 'true')
							{
								$row['Created By'] = $row['CreatedBy FirstName']." ".$row['CreatedBy LastName'];
								$row['Created By'] = strtolower($row['Created By']);
								unset($row['CreatedBy FirstName']);
								unset($row['CreatedBy LastName']);
							}
							if($data['assetlasttouchby'] == 'true')
							{
								$row['Last Touch By'] = $row['LastTouchBy FirstName']." ".$row['LastTouchBy LastName'];
								$row['Last Touch By'] = strtolower($row['Last Touch By']);
								unset($row['LastTouchBy FirstName']);
								unset($row['LastTouchBy LastName']);
							}
							if($data['assetshippingby'] == 'true')
							{
								$row['Shipping By'] = $row['ShippedBy FirstName']." ".$row['ShippedBy LastName'];
								$row['Shipping By'] = strtolower($row['Shipping By']);
								unset($row['ShippedBy FirstName']);
								unset($row['ShippedBy LastName']);
							}
							if($data['assetscanid'] != 'true')
							{
								unset($row['Scan ID']);
							}
							unset($row['disposition_id']);
							unset($row['EvaluationResultID']);
							
							if($data['assetsourcecus'] != 'true')
							{
								unset($row['Source']);
								
							}
							if($data['assetmpn'] != 'true')
							{
								unset($row['MPN']);
								
							}
							//unset($row['AuditResultID']);
							//unset($row['RefCustomerName1']);
							//unset($row['FirstName']);
							//unset($row['LastName']);
							$result[$i] = $row;
							$resultpdf[$i] = $row;
							$i++;
						}
						/*$asset_array = array('Scan ID'=>'1' ,'Serial Number'=>'2' ,'Missing Description'=>'3' ,'Load ID'=>'4','Processed Date'=>'5','Product Class'=>'6','Received Date'=>'7','Product Category'=>'8','Asset Tag'=>'9','Configuration Note'=>'10','Customer'=>'11','Ref Customer'=>'12','Manufacturer'=>'13','Short Description'=>'14','Functional Status'=>'15','Failure Description'=>'16','Cosmetic Grade'=>'17','Cosmetic Description'=>'18','Location'=>'19','Item Sold'=>'20', 'Method of Erase'=>'21', 'Technician'=>'22', 'Facility'=>'23', 'Sale Price'=>'24', 'Slot'=>'25', 'BuildType'=>'26', 'IPAddress'=>'27','RackGrid'=>'28','FinalTkt'=>'29','Pallet ID'=>'30','User'=>'31','Model'=>'32','Additional Notes'=>'33','Erasure Date'=>'34','Mod Cosmetic Grade'=>'35','Weight'=>'36','Product Name'=>'37','Package Type'=>'38','Reporting State'=>'39','Disposition'=>'40','Custom Pallet ID'=>'41','Sale Date'=>42,'Updated Time'=>43,'Created Time'=>44,'Division'=>45,'Payment Code'=>'46','Invoice Code'=>'47','AWS ID'=>'48','Custom Pallet ID'=>'49','Shipping ID'=>'50');*/

						// execute the query for parttype count
						$PartTypeWidgetResult = array();
						$PartTypeResultCount = 0;
						
						$ParttypeWidgetEx = mysqli_query($this->connectionlink,$PartTypeWidgetSql);
						if(mysqli_error($this->connectionlink))
						{
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink)>0){
							while($ParttypeRow = mysqli_fetch_assoc($ParttypeWidgetEx)){
								$PartTypeResultCount += $ParttypeRow['count'];
								$PartTypeWidgetResult[] = $ParttypeRow;
							}
						}
						// execute the query for MPN count
						$MpnWidgetResult = array();
						$MpnResultCount = 0;
						$MpnWidgetEx = mysqli_query($this->connectionlink,$MPNWidgetSql);
						if(mysqli_error($this->connectionlink))
						{
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink)>0){
							while($MpnWidgetRow = mysqli_fetch_assoc($MpnWidgetEx)){
								$MpnResultCount += $MpnWidgetRow['count'];
								$MpnWidgetResult[] = $MpnWidgetRow;
							}
						}
						// execute the query for Evaluation Result count
						$EvaluationResultWidgetResult = array();
						$EvaluationResultCount = 0;
						$EvaluationResultWidgetEx = mysqli_query($this->connectionlink,$EvaluationResultWidgetSql);
						if(mysqli_error($this->connectionlink))
						{
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink)>0){
							while($EvaluationResultWidgetRow = mysqli_fetch_assoc($EvaluationResultWidgetEx)){
								$EvaluationResultCount += $EvaluationResultWidgetRow['count'];
								$EvaluationResultWidgetResult[] = $EvaluationResultWidgetRow;
							}
						}
						// execute the query for Disposition count
						$DispositionWidgetResult = array();
						$DispositionResultCount = 0;
						$DispositionWidgetEx = mysqli_query($this->connectionlink,$DispositionWidgetSql);
						if(mysqli_error($this->connectionlink))
						{
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink)>0){
							while($DispositionWidgetRow = mysqli_fetch_assoc($DispositionWidgetEx)){
								$DispositionResultCount += $DispositionWidgetRow['count'];
								$DispositionWidgetResult[] = $DispositionWidgetRow;
							}
						}
						// execute the query for Source type count
						$SourcetypeWidgetResult = array();
						$SourcetypeResultCount = 0;
						$SourcetypeWidgetEx = mysqli_query($this->connectionlink,$SourcetypeWidgetSql);
						if(mysqli_error($this->connectionlink))
						{
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink)>0){
							while($SourcetypeWidgetRow = mysqli_fetch_assoc($SourcetypeWidgetEx)){
								$SourcetypeResultCount += $SourcetypeWidgetRow['count'];
								$SourcetypeWidgetResult[] = $SourcetypeWidgetRow;
							}
						}
						$json['Success'] = true;
						$json['Result'] = $result;
						$json['PartTypeResult'] = $PartTypeWidgetResult;
						$json['PartTypeTotalCount'] = $PartTypeResultCount;
						$json['MPNResult'] = $MpnWidgetResult;
						$json['MPNResultTotalCount'] = $MpnResultCount;
						$json['EvaluationResult'] = $EvaluationResultWidgetResult;
						$json['EvaluationResultTotalCount'] = $EvaluationResultCount;
						$json['DispositionResult'] = $DispositionWidgetResult;
						$json['DispositionTotalCount'] = $DispositionResultCount;
						$json['SourcetypeResult'] = $SourcetypeWidgetResult;
						$json['SourcetypeTotalCount'] = $SourcetypeResultCount;
						$json['assetcount'] = $TotalCount;
						//sleep(10);
						return json_encode($json);
					}
					else {
						$json['Success'] = false;
						$json['Result'] = "No Records Available";
						return json_encode($json);
					}
				}
			/*}*/
		}
		catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function assetsearchWorkflow($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['assetscanid']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'History Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to History Search Page';
				return json_encode($json);
			}
			$CurrentDate = date('Y-m-d');
			if($data['CRDatefrom'] != '')
			{
				$chkdt = $data['CRDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['CRDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['CRDateto'] != '')
			{
				$chkdt = $data['CRDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['CRDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['WCDatefrom'] != '')
			{
				$chkdt = $data['WCDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['WCDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['WCDateto'] != '')
			{
				$chkdt = $data['WCDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['WCDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['CRDateto'] == '')
			{
				//$data['ARDateto1'] = date('Y-m-d H:i', strtotime($data['ARDatefrom'] . ' + 1 day'));
				$data['CRDateto1'] = date('Y-m-d',strtotime($CurrentDate .' + 1 day'));
			}
			else
			{
				$data['CRDateto1'] = date('Y-m-d H:i', strtotime($data['CRDateto'] . ' + 1 day'));
			}
			if($data['WCDateto'] == '')
			{
				//$data['ASDateto1'] = date('Y-m-d H:i', strtotime($data['ASDatefrom'] . ' + 1 day'));
				$data['WCDateto1'] = date('Y-m-d',strtotime($CurrentDate .' + 1 day'));
			}
			else
			{
				$data['WCDateto1'] = date('Y-m-d H:i', strtotime($data['WCDateto'] . ' + 1 day'));
			}


			//prepare select items for query
			$SELECT = "";
			/*if($data['assetsnstatus'] == 'true')
			{
				$SELECT = $SELECT.", s.StatusName as `SN Status`";
			}*/
			if($data['assetbintype'] == 'true')
			{
				$SELECT = $SELECT.", CP.BinType as `Bin Type`";
			}
			if($data['assetbinid'] == 'true')
			{
				$SELECT = $SELECT.", CP.BinName as `Bin ID`";
			}
			if($data['assetlocationid'] == 'true')
			{
				$SELECT = $SELECT.", LOC.LocationName as `Location ID`";
			}
			if($data['assetsourcecus'] == 'true')
			{
				$SELECT = $SELECT.", SC.CustomerName as `Source`";
			}
			if($data['assetsourcetype'] == 'true')
			{
				$SELECT = $SELECT.", SCT.Cumstomertype as `Source Type`";
			}
			/*if($data['assetparttype'] == 'true')
			{
				$sql1 = $sql1.", pt.parttype as `Part Type`";
			}*/
			if($data['assetTicketid'] == 'true')
			{
				$SELECT = $SELECT.", L.LoadId as `Inbound Ticket ID`";
			}
			if($data['assetcid'] == 'true')
			{
				$SELECT = $SELECT.", P.idPallet as `Inbound Container ID`";
			}
			/*if($data['assetinboundremovalcode'] == 'true')
			{
				$sql1 = $sql1.", P.idPallet as `Inbound Container ID`";
			}*/
			if($data['assetouboundticket'] == 'true')
			{
				$SELECT = $SELECT.", SCO.ShippingID as `Outbound Ticket ID`";
			}
			if($data['assetouboundcontainer'] == 'true')
			{
				$SELECT = $SELECT.", SCO.ShippingContainerID as `Outbound Container ID`";
			}
			if($data['assetouboundRemovalType'] == 'true')
			{
				$SELECT = $SELECT.", DI.disposition as `Outbound Removal Type`";
			}
			if($data['assetobcstatus'] == 'true')
			{
				$SELECT = $SELECT.", ShS.Status as `Outbound Container Status`";
			}
			if($data['assetmpn'] == 'true')
			{
				$SELECT = $SELECT.", A.UniversalModelNumber as `MPN`";
			}
			if($data['assetdisposition'] == 'true')
			{
				$SELECT = $SELECT.", DI.disposition as `Disposition`";
			}
			if($data['assetfacility'] == 'true')
			{
				$SELECT = $SELECT.", F.FacilityName as `Facility`";
			}
			/*if($data['assetcreateddate'] == 'true')
			{
				$sql1 = $sql1.", A.CommittedDate as `Created Date`";
			}*/
			if($data['assetcreatedby'] == 'true')
			{
				$SELECT = $SELECT.", U.FirstName as `CreatedBy FirstName`, U.LastName as `CreatedBy LastName`";
			}
			if($data['assetlastdate'] == 'true')
			{
				$SELECT = $SELECT.", A.DateUpdated as `Last Touch Date`";
			}
			if($data['assetlasttouchby'] == 'true')
			{
				$SELECT = $SELECT.", UM.FirstName as `LastTouchBy FirstName`, UM.LastName as `LastTouchBy LastName`";
			}
			if($data['assetshippingdate'] == 'true')
			{
				$SELECT = $SELECT.", SH.ShippedDate as `Shipping Date`";
			}
			if($data['assetshippingby'] == 'true')
			{
				$SELECT = $SELECT.", su.FirstName as `ShippedBy FirstName`, su.LastName as `ShippedBy LastName`";
			}
			if($data['assetouboundDestination'] == 'true')
			{
				$SELECT = $SELECT.", SHV.VendorName as `Outbound Destination`";
			}
			if($data['assetouboundFacility'] == 'true')
			{
				$SELECT = $SELECT.", SHF.FacilityName as `Outbound Facility`";
			}
			if($data['assetouboundSource'] == 'true')
			{
				$SELECT = $SELECT.", SC.CustomerName as `Outbound Source`";
			}
			if($data['assetouboundContainerType'] == 'true')
			{
				$SELECT = $SELECT.", SHP.packageName as `Outbound Container Type`";
			}
			if($data['assetouboundMPN'] == 'true')
			{
				$SELECT = $SELECT.", SCS.UniversalModelNumber as `Outbound MPN`";
			}
			if($data['WorkflowInput'] == 'true')
			{
				$SELECT = $SELECT.", bwi.input as `Input`";
			}
			if($data['WorkflowInputType'] == 'true')
			{
				$SELECT = $SELECT.", bwi.input_type as `Input Type`";
			}

			// prepare where condition for query
			$WHERE = "";
			if($data['LoadID'] != '')
				$WHERE = $WHERE." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";


			if($data['idCustomer'] != '')
			{
				$idCustomer = '';
				for($i=0;$i<count($data['idCustomer']);$i++) {
					$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
				}
				$idCustomer = rtrim($idCustomer, ",");
				$WHERE = $WHERE." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
			}

			if($data['FacilityID'] != '')
			{
				$FacilityID = '';
				for($i=0;$i<count($data['FacilityID']);$i++) {
					$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
				}
				$FacilityID = rtrim($FacilityID, ",");
				$WHERE = $WHERE." AND A.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
			}

			if($data['ContainerID'] != '')
				$WHERE = $WHERE." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";

			if($data['ShippingID'] != '')
				$WHERE = $WHERE." AND SH.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";

			if($data['OBContainerID'] != '')
				$WHERE = $WHERE." AND SCO.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['OBContainerID'])."'";

			if($data['locationid'] != '')
				$WHERE = $WHERE." AND LOC.LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['locationid'])."'";

			if($data['CRDatefrom'] != '')
				$WHERE = $WHERE." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";

			if($data['Other'] == 'Transfer Assets')
				$WHERE = $WHERE." AND L.SourceFacilityID != ''";

			if($data['Other'] == 'Re-Receive Parts')
				$WHERE = $WHERE." AND P.FacilityTransferContainer = '1' AND P.ASNContainer = '0'";

			if($data['SerialNumber'] != ''){
				// Split the comma-separated values into an array
				$SerialsArray = explode(' ', $data['SerialNumber']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$SerialValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $SerialsArray);
				// Join the array back into a string suitable for the IN clause
				$SerialNumbers = implode(',', $SerialValues);
				$WHERE = $WHERE." AND A.SerialNumber IN ( ".$SerialNumbers.")";
			}

			if($data['UniversalModelNumber'] != ''){
				// Split the comma-separated values into an array
				$MPNArray = explode(' ', $data['UniversalModelNumber']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$MPNValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $MPNArray);
				// Join the array back into a string suitable for the IN clause
				$MPNs = implode(',', $MPNValues);

				$WHERE = $WHERE." AND A.UniversalModelNumber IN ( ".$MPNs.")";
			}

			if($data['DispositionID'] != '')
			{
				$dispositionID = '';
				for($i=0;$i<count($data['DispositionID']);$i++) {
					$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
				}
				$dispositionID = rtrim($dispositionID, ",");
				$WHERE = $WHERE." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
			}


			if($data['binid'] != ''){
				// Split the comma-separated values into an array
				$BinArray = explode(' ', $data['binid']);
				// Escape each value and wrap it in quotes to prevent SQL injection
				$BinValues = array_map(function($value) {
						return "'" . trim($value) . "'";
				}, $BinArray);
				// Join the array back into a string suitable for the IN clause
				$BinIDs = implode(',', $BinValues);
				$WHERE = $WHERE." AND CP.BinName IN ( ".$BinIDs.")";
			}

			if($data['CustomerType'] != '')
			{
				$CustomerType = '';
				for($i=0;$i<count($data['CustomerType']);$i++) {
					$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
				}
				$CustomerType = rtrim($CustomerType, ",");
				$WHERE = $WHERE." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
			}

			if($data['Parttype'] != '')
			{
				$Parttype = '';
				for($i=0;$i<count($data['Parttype']);$i++) {
					$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
				}
				$Parttype = rtrim($Parttype, ",");
				//$Parttype = str_replace($Parttype, '\'');
				//$Parttype = str_replace('\'',"'",$Parttype);
				$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
				$WHERE = $WHERE." AND A.part_type IN ( ".$Parttype.")";
			}

			if($data['Input'] != '')
				$WHERE = $WHERE." AND bwi.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";

			if($data['InputType'] != '')
				$WHERE = $WHERE." AND bwi.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";

			for($i=0;$i<count($data['Workflow']);$i++) {
					//$workflow_id = $workflow_id.$data['Workflow'][$i].',';
					if($data['Workflow'][$i] == 3) { // Sanitization workflow
					// get data from Rack records
							
							$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Sanitization' as Workflow";
							if($data['assetTransactor'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
							}
							if($data['assetTransactorDate'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
							}
							$RackRecoverySQL = $RackRecoverySQL.$SELECT;
							$RackRecoveryMainBodyQuery = " From asset_sanitization AA
								LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN users AAU ON AAU.UserId = AA.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
								LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN business_rule BR ON BR.rule_id = AA.sanitization_rule_id
								LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
								LEFT JOIN workflow_input bwi ON bwi.input_id = AA.sanitization_input_id
								where 1";
								if($data['WCDatefrom'] != '')
									$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['WCDateto1'])."'";
							$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
							//echo $RackRecoverySQL;exit;

							$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
							$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
							$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
							$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
							$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
							
								if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
									$sql10 = $RackRecoverySQL;
									$PartTypeWidgetSql10 = $RackRecoveryPartTypeCountQuery;
									$MPNWidgetSql10 = $RackRecoveryListOfMpnsQuery;
									$EvaluationResultWidgetSql10 = $RackRecoveryEvaluationResultQuery;
									$DispositionWidgetSql10 = $RackRecoveryDispositionQuery;
									$SourcetypeWidgetSql10 = $RackRecoverySourcetypeQuery;
								} else {
									$sql10 = $RackRecoverySQL;
									$PartTypeWidgetSql10 = $RackRecoveryPartTypeCountQuery;
									$MPNWidgetSql10 = $RackRecoveryListOfMpnsQuery;
									$EvaluationResultWidgetSql10 = $RackRecoveryEvaluationResultQuery;
									$DispositionWidgetSql10 = $RackRecoveryDispositionQuery;
									$SourcetypeWidgetSql10 = $RackRecoverySourcetypeQuery;
								}

						}elseif($data['Workflow'][$i] == 2){ // Failure Analysis workflow
							$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'FALabTesting' as Workflow";
							if($data['assetTransactor'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
							}
							if($data['assetTransactorDate'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
							}
							$RackRecoverySQL = $RackRecoverySQL.$SELECT;
							$RackRecoveryMainBodyQuery = " From asset_failure_analysis AA
								LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN users AAU ON AAU.UserId = AA.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
								LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN business_rule BR ON BR.rule_id = AA.fa_rule_id
								LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
								LEFT JOIN workflow_input bwi ON bwi.input_id = AA.fa_input_id
								where 1";
							if($data['WCDatefrom'] != '')
								$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['WCDateto1'])."'";

							$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
							//echo $RackRecoverySQL;exit;

							$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
							$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
							$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
							$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
							$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
							
								if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
									$sql20 = $RackRecoverySQL;
									$PartTypeWidgetSql20 = $RackRecoveryPartTypeCountQuery;
									$MPNWidgetSql20 = $RackRecoveryListOfMpnsQuery;
									$EvaluationResultWidgetSql20 = $RackRecoveryEvaluationResultQuery;
									$DispositionWidgetSql20 = $RackRecoveryDispositionQuery;
									$SourcetypeWidgetSql20 = $RackRecoverySourcetypeQuery;
								} else {
									$sql20 = $RackRecoverySQL;
									$PartTypeWidgetSql20 = $RackRecoveryPartTypeCountQuery;
									$MPNWidgetSql20 = $RackRecoveryListOfMpnsQuery;
									$EvaluationResultWidgetSql20 = $RackRecoveryEvaluationResultQuery;
									$DispositionWidgetSql20 = $RackRecoveryDispositionQuery;
									$SourcetypeWidgetSql20 = $RackRecoverySourcetypeQuery;
								}

						}
						elseif($data['Workflow'][$i] == 4){
							// get data from RMA
							
							$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'RMAInvestigation' as Workflow";
							if($data['assetTransactor'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
							}
							if($data['assetTransactorDate'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
							}
							$RackRecoverySQL = $RackRecoverySQL.$SELECT;
							$RackRecoveryMainBodyQuery = " From asset_rma_investigation AA
								LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN users AAU ON AAU.UserId = AA.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
								LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN business_rule BR ON BR.rule_id = AA.rma_rule_id
								LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
								LEFT JOIN workflow_input bwi ON bwi.input_id = AA.rma_input_id
								where 1";

							if($data['WCDatefrom'] != '')
								$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['WCDateto1'])."'";

							$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
							//echo $RackRecoverySQL;exit;

							$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
							$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
							$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
							$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
							$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
							
							if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
								$sql21 = $RackRecoverySQL;
								$PartTypeWidgetSql21 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql21 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql21 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql21 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql21 = $RackRecoverySourcetypeQuery;
							} else {
								$sql21 = $RackRecoverySQL;
								$PartTypeWidgetSql21 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql21 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql21 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql21 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql21 = $RackRecoverySourcetypeQuery;
							}
						}
						elseif($data['Workflow'][$i] == 5){
							// get data from Harvest
							
							$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Harvest' as Workflow";
							if($data['assetTransactor'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
							}
							if($data['assetTransactorDate'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
							}
							$RackRecoverySQL = $RackRecoverySQL.$SELECT;
							$RackRecoveryMainBodyQuery = " From asset_harvest AA
								LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN users AAU ON AAU.UserId = AA.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
								LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN business_rule BR ON BR.rule_id = AA.harvest_rule_id
								LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
								LEFT JOIN workflow_input bwi ON bwi.input_id = AA.harvest_input_id
								where 1";
							if($data['WCDatefrom'] != '')
								$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['WCDateto1'])."'";
							$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
							//echo $RackRecoverySQL;exit;

							$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
							$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
							$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
							$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
							$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
							
							if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
								$sql6 = $RackRecoverySQL;
								$PartTypeWidgetSql6 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql6 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql6 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql6 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql6 = $RackRecoverySourcetypeQuery;
							} else {
								$sql6 = $RackRecoverySQL;
								$PartTypeWidgetSql6 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql6 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql6 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql6 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql6 = $RackRecoverySourcetypeQuery;
							}
						}
						elseif($data['Workflow'][$i] == 7){
							// get data from Repair
							
							$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Repair' as Workflow";
							if($data['assetTransactor'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
							}
							if($data['assetTransactorDate'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
							}
							$RackRecoverySQL = $RackRecoverySQL.$SELECT;
							$RackRecoveryMainBodyQuery = " From asset_repair AA
								LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN users AAU ON AAU.UserId = AA.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
								LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN business_rule BR ON BR.rule_id = AA.repair_rule_id
								LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
								LEFT JOIN workflow_input bwi ON bwi.input_id = AA.repair_input_id
								where 1";
							if($data['WCDatefrom'] != '')
								$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['WCDateto1'])."'";
							$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
							//echo $RackRecoverySQL;exit;

							$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
							$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
							$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
							$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
							$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
							
							if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
								$sql65 = $RackRecoverySQL;
								$PartTypeWidgetSql65 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql65 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql65 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql65 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql65 = $RackRecoverySourcetypeQuery;
							} else {
								$sql65 = $RackRecoverySQL;
								$PartTypeWidgetSql65 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql65 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql65 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql65 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql65 = $RackRecoverySourcetypeQuery;
							}
						}
						elseif($data['Workflow'][$i] == 1){
							// get data from Rack records
							
							$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Receive' as Workflow";
							if($data['assetTransactor'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
							}
							if($data['assetTransactorDate'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", A.DateCreated as `Transaction Date`";
							}
							$RackRecoverySQL = $RackRecoverySQL.$SELECT;
							$RackRecoveryMainBodyQuery = " From asset A 
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
								LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
								LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
								LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
								where 1";
							if($data['WCDatefrom'] != '')
								$WHERE1 = " AND A.DateCreated between '".mysqli_real_escape_string($this->connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['WCDateto1'])."'";
							$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
							//echo $RackRecoverySQL;exit;

							$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
							$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
							$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
							$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
							$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
							
							if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
								$sql66 = $RackRecoverySQL;
								$PartTypeWidgetSql66 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql66 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql66 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql66 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql66 = $RackRecoverySourcetypeQuery;
							} else {
								$sql66 = $RackRecoverySQL;
								$PartTypeWidgetSql66 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql66 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql66 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql66 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql66 = $RackRecoverySourcetypeQuery;
							}
						}
						elseif($data['Workflow'][$i] == 6){
							// get data from Rack records
							
							$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Removal' as Workflow";
							if($data['assetTransactor'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
							}
							if($data['assetTransactorDate'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
							}
							$RackRecoverySQL = $RackRecoverySQL.$SELECT;
							$RackRecoveryMainBodyQuery = " From shipping_container_serials AA 
								LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.FirstReceivedCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
								LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
								LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
								LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
								where 1";
							if($data['WCDatefrom'] != '')
								$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['WCDateto1'])."'";
							$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
							//echo $RackRecoverySQL;exit;

							$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
							$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
							$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
							$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
							$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
							
							if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
								$sql67 = $RackRecoverySQL;
								$PartTypeWidgetSql67 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql67 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql67 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql67 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql67 = $RackRecoverySourcetypeQuery;
							} else {
								$sql67 = $RackRecoverySQL;
								$PartTypeWidgetSql67 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql67 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql67 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql67 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql67 = $RackRecoverySourcetypeQuery;
							}
						}
						elseif($data['Workflow'][$i] == 10){//Parts Recovery
							// get data from Rack records
							
							$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Parts Recovery' as Workflow";
							if($data['assetTransactor'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
							}
							if($data['assetTransactorDate'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", A.DateCreated as `Transaction Date`";
							}
							$RackRecoverySQL = $RackRecoverySQL.$SELECT;
							$RackRecoveryMainBodyQuery = " From asset A 
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
								LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
								LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
								LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
								where 1";
							if($data['WCDatefrom'] != '')
								$WHERE1 = " AND A.DateCreated between '".mysqli_real_escape_string($this->connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['WCDateto1'])."'";
							$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
							//echo $RackRecoverySQL;exit;

							$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
							$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
							$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
							$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
							$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
							
							if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
								$sql107 = $RackRecoverySQL;
								$PartTypeWidgetSql107 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql107 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql107 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql107 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql107 = $RackRecoverySourcetypeQuery;
							} else {
								$sql107 = $RackRecoverySQL;
								$PartTypeWidgetSql107 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql107 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql107 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql107 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql107 = $RackRecoverySourcetypeQuery;
							}
						}
						elseif($data['Workflow'][$i] == 20){//Parts Sort
							// get data from Rack records
							
							$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Parts Recovery' as Workflow";
							if($data['assetTransactor'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
							}
							if($data['assetTransactorDate'] == 'true')
							{
								$RackRecoverySQL = $RackRecoverySQL.", A.DateCreated as `Transaction Date`";
							}
							$RackRecoverySQL = $RackRecoverySQL.$SELECT;
							$RackRecoveryMainBodyQuery = " From parts_sort_history PSH
								LEFT JOIN asset A ON A.AssetScanID = PSH.AssetScanID
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
								LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
								LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
								LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
								LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
								LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
								LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
								LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
								LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
								LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
								LEFT JOIN users su ON su.UserId = SH.UpdatedBy
								LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
								LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
								LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
								where 1";
							if($data['WCDatefrom'] != '')
								$WHERE1 = " AND PSH.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['WCDateto1'])."'";
							$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
							//echo $RackRecoverySQL;exit;

							$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
							$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
							$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
							$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
							$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
							
							if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
								$sql207 = $RackRecoverySQL;
								$PartTypeWidgetSql207 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql207 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql207 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql207 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql207 = $RackRecoverySourcetypeQuery;
							} else {
								$sql207 = $RackRecoverySQL;
								$PartTypeWidgetSql207 = $RackRecoveryPartTypeCountQuery;
								$MPNWidgetSql207 = $RackRecoveryListOfMpnsQuery;
								$EvaluationResultWidgetSql207 = $RackRecoveryEvaluationResultQuery;
								$DispositionWidgetSql207 = $RackRecoveryDispositionQuery;
								$SourcetypeWidgetSql207 = $RackRecoverySourcetypeQuery;
							}
						}

					}
						$sql = "select SQL_CALC_FOUND_ROWS * from (";
						$PartTypeWidgetSql = "select sum(count) as count,part_type,part_type from (";
						$MPNWidgetSql = "select sum(count) as count,UniversalModelNumber from (";
						$EvaluationResultWidgetSql = "select sum(count) as count,EvaluationResultID,input from (";
						$DispositionWidgetSql = "select sum(count) as count,disposition_id,disposition from (";
						$SourcetypeWidgetSql = "select sum(count) as count,CustomerType,Cumstomertype from (";
						if($sql10 != '')
						{
							$sql = $sql."(".$sql10.") UNION ALL ";
							$PartTypeWidgetSql = $PartTypeWidgetSql."(".$PartTypeWidgetSql10.") UNION ALL ";
							$MPNWidgetSql = $MPNWidgetSql."(".$MPNWidgetSql10.") UNION ALL ";
							$EvaluationResultWidgetSql = $EvaluationResultWidgetSql."(".$EvaluationResultWidgetSql10.") UNION ALL ";
							$DispositionWidgetSql = $DispositionWidgetSql."(".$DispositionWidgetSql10.") UNION ALL ";
							$SourcetypeWidgetSql = $SourcetypeWidgetSql."(".$SourcetypeWidgetSql10.") UNION ALL ";
						}
						if($sql20 != '')
						{
							$sql = $sql."(".$sql20.") UNION ALL ";
							$PartTypeWidgetSql = $PartTypeWidgetSql."(".$PartTypeWidgetSql20.") UNION ALL ";
							$MPNWidgetSql = $MPNWidgetSql."(".$MPNWidgetSql20.") UNION ALL ";
							$EvaluationResultWidgetSql = $EvaluationResultWidgetSql."(".$EvaluationResultWidgetSql20.") UNION ALL ";
							$DispositionWidgetSql = $DispositionWidgetSql."(".$DispositionWidgetSql20.") UNION ALL ";
							$SourcetypeWidgetSql = $SourcetypeWidgetSql."(".$SourcetypeWidgetSql20.") UNION ALL ";
						}
						if($sql21 != '')
						{
							$sql = $sql."(".$sql21.") UNION ALL ";
							$PartTypeWidgetSql = $PartTypeWidgetSql."(".$PartTypeWidgetSql21.") UNION ALL ";
							$MPNWidgetSql = $MPNWidgetSql."(".$MPNWidgetSql21.") UNION ALL ";
							$EvaluationResultWidgetSql = $EvaluationResultWidgetSql."(".$EvaluationResultWidgetSql21.") UNION ALL ";
							$DispositionWidgetSql = $DispositionWidgetSql."(".$DispositionWidgetSql21.") UNION ALL ";
							$SourcetypeWidgetSql = $SourcetypeWidgetSql."(".$SourcetypeWidgetSql21.") UNION ALL ";
						}
						if($sql6 != '')
						{
							$sql = $sql."(".$sql6.") UNION ALL ";
							$PartTypeWidgetSql = $PartTypeWidgetSql."(".$PartTypeWidgetSql6.") UNION ALL ";
							$MPNWidgetSql = $MPNWidgetSql."(".$MPNWidgetSql6.") UNION ALL ";
							$EvaluationResultWidgetSql = $EvaluationResultWidgetSql."(".$EvaluationResultWidgetSql6.") UNION ALL ";
							$DispositionWidgetSql = $DispositionWidgetSql."(".$DispositionWidgetSql6.") UNION ALL ";
							$SourcetypeWidgetSql = $SourcetypeWidgetSql."(".$SourcetypeWidgetSql6.") UNION ALL ";
						}
						if($sql65 != '')
						{
							$sql = $sql."(".$sql65.") UNION ALL ";
							$PartTypeWidgetSql = $PartTypeWidgetSql."(".$PartTypeWidgetSql65.") UNION ALL ";
							$MPNWidgetSql = $MPNWidgetSql."(".$MPNWidgetSql65.") UNION ALL ";
							$EvaluationResultWidgetSql = $EvaluationResultWidgetSql."(".$EvaluationResultWidgetSql65.") UNION ALL ";
							$DispositionWidgetSql = $DispositionWidgetSql."(".$DispositionWidgetSql65.") UNION ALL ";
							$SourcetypeWidgetSql = $SourcetypeWidgetSql."(".$SourcetypeWidgetSql65.") UNION ALL ";
						}
						if($sql66 != '')
						{
							$sql = $sql."(".$sql66.") UNION ALL ";
							$PartTypeWidgetSql = $PartTypeWidgetSql."(".$PartTypeWidgetSql66.") UNION ALL ";
							$MPNWidgetSql = $MPNWidgetSql."(".$MPNWidgetSql66.") UNION ALL ";
							$EvaluationResultWidgetSql = $EvaluationResultWidgetSql."(".$EvaluationResultWidgetSql66.") UNION ALL ";
							$DispositionWidgetSql = $DispositionWidgetSql."(".$DispositionWidgetSql66.") UNION ALL ";
							$SourcetypeWidgetSql = $SourcetypeWidgetSql."(".$SourcetypeWidgetSql66.") UNION ALL ";
						}
						if($sql67 != '')
						{
							$sql = $sql."(".$sql67.") UNION ALL ";
							$PartTypeWidgetSql = $PartTypeWidgetSql."(".$PartTypeWidgetSql67.") UNION ALL ";
							$MPNWidgetSql = $MPNWidgetSql."(".$MPNWidgetSql67.") UNION ALL ";
							$EvaluationResultWidgetSql = $EvaluationResultWidgetSql."(".$EvaluationResultWidgetSql67.") UNION ALL ";
							$DispositionWidgetSql = $DispositionWidgetSql."(".$DispositionWidgetSql67.") UNION ALL ";
							$SourcetypeWidgetSql = $SourcetypeWidgetSql."(".$SourcetypeWidgetSql67.") UNION ALL ";
						}
						if($sql107 != '')
						{
							$sql = $sql."(".$sql107.") UNION ALL ";
							$PartTypeWidgetSql = $PartTypeWidgetSql."(".$PartTypeWidgetSql107.") UNION ALL ";
							$MPNWidgetSql = $MPNWidgetSql."(".$MPNWidgetSql107.") UNION ALL ";
							$EvaluationResultWidgetSql = $EvaluationResultWidgetSql."(".$EvaluationResultWidgetSql107.") UNION ALL ";
							$DispositionWidgetSql = $DispositionWidgetSql."(".$DispositionWidgetSql107.") UNION ALL ";
							$SourcetypeWidgetSql = $SourcetypeWidgetSql."(".$SourcetypeWidgetSql107.") UNION ALL ";
						}
						if($sql207 != '')
						{
							$sql = $sql."(".$sql207.") UNION ALL ";
							$PartTypeWidgetSql = $PartTypeWidgetSql."(".$PartTypeWidgetSql207.") UNION ALL ";
							$MPNWidgetSql = $MPNWidgetSql."(".$MPNWidgetSql207.") UNION ALL ";
							$EvaluationResultWidgetSql = $EvaluationResultWidgetSql."(".$EvaluationResultWidgetSql207.") UNION ALL ";
							$DispositionWidgetSql = $DispositionWidgetSql."(".$DispositionWidgetSql207.") UNION ALL ";
							$SourcetypeWidgetSql = $SourcetypeWidgetSql."(".$SourcetypeWidgetSql207.") UNION ALL ";
						}
						if($sql10 == '' && $sql20 == '' && $sql21 == '' && $sql6 == '' && $sql65 == '' && $sql66 == '' && $sql67 == '' && $sql107 == '' && $sql207 == '')
						{
							$json['Success'] = false;
							$json['Result'] = 'Please Select atleast one Service Workflow.';
							return json_encode($json);
						}
						$sql = substr($sql, 0, -11);
						$sql = $sql.") as result order by `Created Date` DESC";

						$PartTypeWidgetSql = substr($PartTypeWidgetSql, 0, -11);
						$PartTypeWidgetSql = $PartTypeWidgetSql.") as result group by part_type order by count desc";

						$MPNWidgetSql = substr($MPNWidgetSql, 0, -11);
						$MPNWidgetSql = $MPNWidgetSql.") as result group by UniversalModelNumber order by count desc";

						$EvaluationResultWidgetSql = substr($EvaluationResultWidgetSql, 0, -11);
						$EvaluationResultWidgetSql = $EvaluationResultWidgetSql.") as result group by EvaluationResultID order by count desc";

						$DispositionWidgetSql = substr($DispositionWidgetSql, 0, -11);
						$DispositionWidgetSql = $DispositionWidgetSql.") as result group by disposition_id order by count desc";

						$SourcetypeWidgetSql = substr($SourcetypeWidgetSql, 0, -11);
						$SourcetypeWidgetSql = $SourcetypeWidgetSql.") as result group by CustomerType order by count desc";

					//echo $sql;exit;
					//$limit = 10*$data['Currentpage'];
					unset($_SESSION['workflowsearchreport_query']);
					$_SESSION['workflowsearchreport_query'] = $sql;
					$sql = $sql." LIMIT 0,3";
				//echo $sql;exit;
				/*$json['Success'] = false;
				$json['Result'] = $sql;
				return json_encode($json);*/
				mysqli_query($this->connectionlink,"SET SQL_BIG_SELECTS=1");
				$query = mysqli_query($this->connectionlink,$sql);

				if(mysqli_error($this->connectionlink))
				{
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				else
				{
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$totalcountQ = mysqli_query($this->connectionlink,"select FOUND_ROWS() AS total_records");
						$totalCountRow = mysqli_fetch_assoc($totalcountQ);
						$TotalCount = $totalCountRow['total_records'];
						$i = 0;
						while($row = mysqli_fetch_assoc($query)) {
							$_SESSION['searchcustomer'] = '';
							if($data['CustomerID'] != '')
							{
								$_SESSION['searchcustomer'] = $data['CustomerID'];
							}
							if($data['assetcreatedby'] == 'true')
							{
								//$row['Created By'] = $row['FirstName']." ".$row['LastName'];
								//$row['Created By'] = strtolower($row['Created By']);
							}
							if($data['assetcreateddate'] == 'true')
							{
								$date = date("M j, Y g:i a", strtotime($row['Created Date']));
								//$time = date("g:i a", strtotime($row['DateCreated']));
								//$row['Created Date'] = $date." ".$time;
								$row['Created Date'] = $date;
								//unset($row['DateCreated']);
							}
							if($data['assetcreatedby'] == 'true')
							{
								$row['Created By'] = $row['CreatedBy FirstName']." ".$row['CreatedBy LastName'];
								$row['Created By'] = strtolower($row['Created By']);
								unset($row['CreatedBy FirstName']);
								unset($row['CreatedBy LastName']);
							}
							if($data['assetlasttouchby'] == 'true')
							{
								$row['Last Touch By'] = $row['LastTouchBy FirstName']." ".$row['LastTouchBy LastName'];
								$row['Last Touch By'] = strtolower($row['Last Touch By']);
								unset($row['LastTouchBy FirstName']);
								unset($row['LastTouchBy LastName']);
							}
							if($data['assetshippingby'] == 'true')
							{
								$row['Shipping By'] = $row['ShippedBy FirstName']." ".$row['ShippedBy LastName'];
								$row['Shipping By'] = strtolower($row['Shipping By']);
								unset($row['ShippedBy FirstName']);
								unset($row['ShippedBy LastName']);
							}
							if($data['assetscanid'] != 'true')
							{
								unset($row['Scan ID']);
							}
							//unset($row['AuditResultID']);
							//unset($row['RefCustomerName1']);
							//unset($row['FirstName']);
							//unset($row['LastName']);
							$result[$i] = $row;
							$resultpdf[$i] = $row;
							$i++;
						}
						/*$asset_array = array('Scan ID'=>'1' ,'Serial Number'=>'2' ,'Missing Description'=>'3' ,'Load ID'=>'4','Processed Date'=>'5','Product Class'=>'6','Received Date'=>'7','Product Category'=>'8','Asset Tag'=>'9','Configuration Note'=>'10','Customer'=>'11','Ref Customer'=>'12','Manufacturer'=>'13','Short Description'=>'14','Functional Status'=>'15','Failure Description'=>'16','Cosmetic Grade'=>'17','Cosmetic Description'=>'18','Location'=>'19','Item Sold'=>'20', 'Method of Erase'=>'21', 'Technician'=>'22', 'Facility'=>'23', 'Sale Price'=>'24', 'Slot'=>'25', 'BuildType'=>'26', 'IPAddress'=>'27','RackGrid'=>'28','FinalTkt'=>'29','Pallet ID'=>'30','User'=>'31','Model'=>'32','Additional Notes'=>'33','Erasure Date'=>'34','Mod Cosmetic Grade'=>'35','Weight'=>'36','Product Name'=>'37','Package Type'=>'38','Reporting State'=>'39','Disposition'=>'40','Custom Pallet ID'=>'41','Sale Date'=>42,'Updated Time'=>43,'Created Time'=>44,'Division'=>45,'Payment Code'=>'46','Invoice Code'=>'47','AWS ID'=>'48','Custom Pallet ID'=>'49','Shipping ID'=>'50');*/

						// execute the query for parttype count
						$PartTypeWidgetResult = array();
						$PartTypeResultCount = 0;
						
						$ParttypeWidgetEx = mysqli_query($this->connectionlink,$PartTypeWidgetSql);
						if(mysqli_error($this->connectionlink))
						{
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink)>0){
							while($ParttypeRow = mysqli_fetch_assoc($ParttypeWidgetEx)){
								$PartTypeResultCount += $ParttypeRow['count'];
								$PartTypeWidgetResult[] = $ParttypeRow;
							}
						}
						// execute the query for MPN count
						$MpnWidgetResult = array();
						$MpnResultCount = 0;
						$MpnWidgetEx = mysqli_query($this->connectionlink,$MPNWidgetSql);
						if(mysqli_error($this->connectionlink))
						{
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink)>0){
							while($MpnWidgetRow = mysqli_fetch_assoc($MpnWidgetEx)){
								$MpnResultCount += $MpnWidgetRow['count'];
								$MpnWidgetResult[] = $MpnWidgetRow;
							}
						}
						// execute the query for Evaluation Result count
						$EvaluationResultWidgetResult = array();
						$EvaluationResultCount = 0;
						$EvaluationResultWidgetEx = mysqli_query($this->connectionlink,$EvaluationResultWidgetSql);
						if(mysqli_error($this->connectionlink))
						{
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink)>0){
							while($EvaluationResultWidgetRow = mysqli_fetch_assoc($EvaluationResultWidgetEx)){
								$EvaluationResultCount += $EvaluationResultWidgetRow['count'];
								$EvaluationResultWidgetResult[] = $EvaluationResultWidgetRow;
							}
						}
						// execute the query for Disposition count
						$DispositionWidgetResult = array();
						$DispositionResultCount = 0;
						$DispositionWidgetEx = mysqli_query($this->connectionlink,$DispositionWidgetSql);
						if(mysqli_error($this->connectionlink))
						{
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink)>0){
							while($DispositionWidgetRow = mysqli_fetch_assoc($DispositionWidgetEx)){
								$DispositionResultCount += $DispositionWidgetRow['count'];
								$DispositionWidgetResult[] = $DispositionWidgetRow;
							}
						}
						// execute the query for Source type count
						$SourcetypeWidgetResult = array();
						$SourcetypeResultCount = 0;
						$SourcetypeWidgetEx = mysqli_query($this->connectionlink,$SourcetypeWidgetSql);
						if(mysqli_error($this->connectionlink))
						{
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink)>0){
							while($SourcetypeWidgetRow = mysqli_fetch_assoc($SourcetypeWidgetEx)){
								$SourcetypeResultCount += $SourcetypeWidgetRow['count'];
								$SourcetypeWidgetResult[] = $SourcetypeWidgetRow;
							}
						}
						$json['Success'] = true;
						$json['Result'] = $result;
						$json['PartTypeResult'] = $PartTypeWidgetResult;
						$json['PartTypeTotalCount'] = $PartTypeResultCount;
						$json['MPNResult'] = $MpnWidgetResult;
						$json['MPNResultTotalCount'] = $MpnResultCount;
						$json['EvaluationResult'] = $EvaluationResultWidgetResult;
						$json['EvaluationResultTotalCount'] = $EvaluationResultCount;
						$json['DispositionResult'] = $DispositionWidgetResult;
						$json['DispositionTotalCount'] = $DispositionResultCount;
						$json['SourcetypeResult'] = $SourcetypeWidgetResult;
						$json['SourcetypeTotalCount'] = $SourcetypeResultCount;
						$json['assetcount'] = $TotalCount;
						//sleep(10);
						return json_encode($json);
					}
					else {
						$json['Success'] = false;
						$json['Result'] = "No Records";
						return json_encode($json);
					}
				}
			/*}*/
		}
		catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function AssetSearch($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['assetscanid']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			if($data['ARDatefrom'] != '')
			{
				$chkdt = $data['ARDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ARDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ARDateto'] != '')
			{
				$chkdt = $data['ARDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ARDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ASDatefrom'] != '')
			{
				$chkdt = $data['ASDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ASDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ASDateto'] != '')
			{
				$chkdt = $data['ASDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ASDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ARDateto'] == '')
			{
				$data['ARDateto1'] = date('Y-m-d H:i', strtotime($data['ARDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['ARDateto1'] = date('Y-m-d H:i', strtotime($data['ARDateto'] . ' + 1 day'));
			}
			if($data['ASDateto'] == '')
			{
				$data['ASDateto1'] = date('Y-m-d H:i', strtotime($data['ASDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['ASDateto1'] = date('Y-m-d H:i', strtotime($data['ASDateto'] . ' + 1 day'));
			}
			if($data['Currentpage'] == 0)
			{

				// to get count from rack recovery records
				$sqlcount1 = "Select A.* From rack_recovery_records A
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = A.DispositionID
				";
				$sqlcount1 = $sqlcount1." where A.IsCommitted=1";
					if($data['LoadID'] != '')
						$sqlcount1 = $sqlcount1." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
					if($data['ShippingID'] != '')
						$sqlcount1 = $sqlcount1." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
					if($data['idCustomer'] != '')
					{
						$idCustomer = '';
						for($i=0;$i<count($data['idCustomer']);$i++) {
							$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
						}
						$idCustomer = rtrim($idCustomer, ",");
						$sqlcount1 = $sqlcount1." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
					}
					if($data['ARDatefrom'] != '')
						$sqlcount1 = $sqlcount1." AND A.CommittedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ARDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ARDateto1'])."'";
					if($data['FacilityID'] != '')
					{
						$FacilityID = '';
						for($i=0;$i<count($data['FacilityID']);$i++) {
							$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
						}
						$FacilityID = rtrim($FacilityID, ",");
						$sqlcount1 = $sqlcount1." AND A.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
					}
					if($data['ContainerID'] != '')
						$sqlcount1 = $sqlcount1." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
					if($data['ASDatefrom'] != '')
						$sqlcount1 = $sqlcount1." AND A.ShippedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ASDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ASDateto1'])."'";
					if($data['SerialNumber'] != '')
						$sqlcount1 = $sqlcount1." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
					if($data['UniversalModelNumber'] != '')
						$sqlcount1 = $sqlcount1." AND A.MPN = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
					if($data['DispositionID'] != '')
					{
						$dispositionID = '';
						for($i=0;$i<count($data['DispositionID']);$i++) {
							$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
						}
						$dispositionID = rtrim($dispositionID, ",");
						$sqlcount1 = $sqlcount1." AND A.DispositionID IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
					}
					if($data['CustomerType'] != '')
					{
						$CustomerType = '';
						for($i=0;$i<count($data['CustomerType']);$i++) {
							$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
						}
						$CustomerType = rtrim($CustomerType, ",");
						$sqlcount1 = $sqlcount1." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
					}
					if($data['binid'] != '')
						$sqlcount1 = $sqlcount1." AND A.DispositionBin = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
					/*if($data['workflow_id'] != '')
					{
						$workflow_id = '';
						for($i=0;$i<count($data['workflow_id']);$i++) {
							$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
						}
						$workflow_id = rtrim($workflow_id, ",");
						$sqlcount = $sqlcount." AND A.RecentWorkflowID IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
					}*/
					if($data['Parttype'] != '')
					{
						$Parttype = '';
						for($i=0;$i<count($data['Parttype']);$i++) {
							$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
						}
						$Parttype = rtrim($Parttype, ",");
						//$Parttype = str_replace($Parttype, '\'');
						//$Parttype = str_replace('\'',"'",$Parttype);
						$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
						$sqlcount1 = $sqlcount1." AND A.parttypeid IN ( ".$Parttype.")";
					}

					// to get count from assembly recovery records
					$sqlcount2 = "Select A.* From assembly_recovery_records A
					LEFT JOIN users U ON U.UserId = A.CreatedBy
					LEFT JOIN pallets P ON P.idPallet = A.idPallet
					LEFT JOIN facility F ON F.FacilityID = A.FacilityID
					LEFT JOIN loads L ON L.LoadId = P.LoadId
					LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
					LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
					LEFT JOIN disposition DI ON DI.disposition_id = A.DispositionID
					";
					$sqlcount2 = $sqlcount2." where A.IsCommitted=1";
					if($data['LoadID'] != '')
						$sqlcount2 = $sqlcount2." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
						if($data['idCustomer'] != '')
						{
							$idCustomer = '';
							for($i=0;$i<count($data['idCustomer']);$i++) {
								$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
							}
							$idCustomer = rtrim($idCustomer, ",");
							$sqlcount2 = $sqlcount2." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
						}
						if($data['CRDatefrom'] != '')
							$sqlcount2 = $sqlcount2." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
						if($data['FacilityID'] != '')
						{
							$FacilityID = '';
							for($i=0;$i<count($data['FacilityID']);$i++) {
								$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
							}
							$FacilityID = rtrim($FacilityID, ",");
							$sqlcount2 = $sqlcount2." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
						}
						if($data['ContainerID'] != '')
							$sqlcount2 = $sqlcount2." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
						if($data['ACDatefrom'] != '')
							$sqlcount2 = $sqlcount2." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
						if($data['SerialNumber'] != '')
							$sqlcount2 = $sqlcount2." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
						if($data['UniversalModelNumber'] != '')
							$sqlcount2 = $sqlcount2." AND A.MPN = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
						if($data['DispositionID'] != '')
						{
							$dispositionID = '';
							for($i=0;$i<count($data['DispositionID']);$i++) {
								$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
							}
							$dispositionID = rtrim($dispositionID, ",");
							$sqlcount2 = $sqlcount2." AND A.DispositionID IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
						}
						if($data['CustomerType'] != '')
						{
							$CustomerType = '';
							for($i=0;$i<count($data['CustomerType']);$i++) {
								$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
							}
							$CustomerType = rtrim($CustomerType, ",");
							$sqlcount2 = $sqlcount2." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
						}
						if($data['binid'] != '')
							$sqlcount2 = $sqlcount2." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
						/*if($data['workflow_id'] != '')
						{
							$workflow_id = '';
							for($i=0;$i<count($data['workflow_id']);$i++) {
								$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
							}
							$workflow_id = rtrim($workflow_id, ",");
							$sqlcount = $sqlcount." AND A.RecentWorkflowID IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
						}*/
						if($data['Parttype'] != '')
						{
							$Parttype = '';
							for($i=0;$i<count($data['Parttype']);$i++) {
								$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
							}
							$Parttype = rtrim($Parttype, ",");
							//$Parttype = str_replace($Parttype, '\'');
							//$Parttype = str_replace('\'',"'",$Parttype);
							$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
							$sqlcount2 = $sqlcount2." AND A.parttypeid IN ( ".$Parttype.")";
						}

						// to get count from component recovery records
						$sqlcount3 = "Select A.* From component_recovery_records A
						LEFT JOIN users U ON U.UserId = A.CreatedBy
						LEFT JOIN pallets P ON P.idPallet = A.idPallet
						LEFT JOIN facility F ON F.FacilityID = A.FacilityID
						LEFT JOIN loads L ON L.LoadId = P.LoadId
						LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
						LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
						LEFT JOIN disposition DI ON DI.disposition_id = A.DispositionID
						";
						$sqlcount3 = $sqlcount3." where A.IsCommitted=1";
						if($data['LoadID'] != '')
							$sqlcount3 = $sqlcount3." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
							if($data['idCustomer'] != '')
							{
								$idCustomer = '';
								for($i=0;$i<count($data['idCustomer']);$i++) {
									$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
								}
								$idCustomer = rtrim($idCustomer, ",");
								$sqlcount3 = $sqlcount3." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
							}
							if($data['CRDatefrom'] != '')
								$sqlcount3 = $sqlcount3." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
							if($data['FacilityID'] != '')
							{
								$FacilityID = '';
								for($i=0;$i<count($data['FacilityID']);$i++) {
									$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
								}
								$FacilityID = rtrim($FacilityID, ",");
								$sqlcount3 = $sqlcount3." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
							}
							if($data['ContainerID'] != '')
								$sqlcount3 = $sqlcount3." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
							if($data['ACDatefrom'] != '')
								$sqlcount3 = $sqlcount3." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
							if($data['SerialNumber'] != '')
								$sqlcount3 = $sqlcount3." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
							if($data['UniversalModelNumber'] != '')
								$sqlcount3 = $sqlcount3." AND A.MPN = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
							if($data['DispositionID'] != '')
							{
								$dispositionID = '';
								for($i=0;$i<count($data['DispositionID']);$i++) {
									$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
								}
								$dispositionID = rtrim($dispositionID, ",");
								$sqlcount3 = $sqlcount3." AND A.DispositionID IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
							}
							if($data['CustomerType'] != '')
							{
								$CustomerType = '';
								for($i=0;$i<count($data['CustomerType']);$i++) {
									$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
								}
								$CustomerType = rtrim($CustomerType, ",");
								$sqlcount3 = $sqlcount3." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
							}
							if($data['binid'] != '')
								$sqlcount3 = $sqlcount3." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
							/*if($data['workflow_id'] != '')
							{
								$workflow_id = '';
								for($i=0;$i<count($data['workflow_id']);$i++) {
									$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
								}
								$workflow_id = rtrim($workflow_id, ",");
								$sqlcount = $sqlcount." AND A.RecentWorkflowID IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}*/
							if($data['Parttype'] != '')
							{
								$Parttype = '';
								for($i=0;$i<count($data['Parttype']);$i++) {
									$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
								}
								$Parttype = rtrim($Parttype, ",");
								//$Parttype = str_replace($Parttype, '\'');
								//$Parttype = str_replace('\'',"'",$Parttype);
								$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
								$sqlcount3 = $sqlcount3." AND A.parttypeid IN ( ".$Parttype.")";
							}

						$query = "select count(*) from (".$sqlcount1." UNION ALL ".$sqlcount2." UNION ALL ".$sqlcount3.") As assetcount";

						//echo $query;exit;
					/*$json['Success'] = false;
					$json['Result'] = $sqlcount;
					return json_encode($json);*/
				$querycount = mysqli_query($this->connectionlink,$query);
				$rowcount = mysqli_fetch_assoc($querycount);
			}
				// get data from Rack records
				$sql1 = "select A.SerialNumber as `SerialNumber`";

				if($data['assetbinid'] == 'true')
				{
					$sql1 = $sql1.", CP.BinName as `Bin ID`";
				}
				if($data['assetTicketid'] == 'true')
				{
					$sql1 = $sql1.", L.LoadId as `Inbound Ticket ID`";
				}
				if($data['assetcid'] == 'true')
				{
					$sql1 = $sql1.", P.idPallet as `Inbound Container ID`";
				}
				if($data['assetsourcecus'] == 'true')
				{
					$sql1 = $sql1.", SC.CustomerName as `Source`";
				}
				if($data['assetmpn'] == 'true')
				{
					$sql1 = $sql1.", A.MPN as `MPN`";
				}
				if($data['assetdisposition'] == 'true')
				{
					$sql1 = $sql1.", DI.disposition as `Disposition`";
				}
				if($data['assetfacility'] == 'true')
				{
					$sql1 = $sql1.", F.FacilityName as `Facility`";
				}
				if($data['assetcreatedby'] == 'true')
				{
					$sql1 = $sql1.", U.FirstName,U.LastName";
				}
				if($data['assetcreateddate'] == 'true')
				{
					$sql1 = $sql1.", A.CreatedDate";
				}
				if($data['assetsourcetype'] == 'true')
				{
					$sql1 = $sql1.", SCT.Cumstomertype as `Source Type`";
				}
				if($data['assetouboundcontainer'] == 'true')
				{
					$sql1 = $sql1.", SCS.ShippingContainerID as `Outbound Container ID`";
				}
				if($data['assetouboundticket'] == 'true')
				{
					$sql1 = $sql1.", SCO.ShippingID as `Outbound Ticket ID`";
				}
				if($data['assetparttype'] == 'true')
				{
					$sql1 = $sql1.", pt.parttype as `Part Type`";
				}
				if($data['assetlastdate'] == 'true')
				{
					$sql1 = $sql1.", A.ModifiedDate as `Last Touch Date`";
				}
				if($data['assetouboundRemovalType'] == 'true')
				{
					$sql1 = $sql1.", SHDI.disposition as `Outbound Removal Type`";
				}
				/*if($data['assetouboundDestination'] == 'true')
				{
					$sql1 = $sql1.", SHV.VendorName as `Outbound Destination`";
				}*/
				/*if($data['assetouboundFacility'] == 'true')
				{
					$sql1 = $sql1.", SHF.FacilityName as `Outbound Facility`";
				}*/
				/*if($data['assetouboundSource'] == 'true')
				{
					$sql1 = $sql1.", SC.CustomerName as `Outbound Source`";
				}
				if($data['assetouboundSourceType'] == 'true')
				{
					$sql1 = $sql1.", SCT.Cumstomertype as `Outbound Source Type`";
				}*/
				if($data['assetouboundMPN'] == 'true')
				{
					$sql1 = $sql1.", SCS.UniversalModelNumber as `Outbound MPN`";
				}
				if($data['assetouboundContainerType'] == 'true')
				{
					$sql1 = $sql1.", SHP.packageName as `Outbound Container Type`";
				}
				/*if($data['assetouboundSanitizationVerID'] == 'true')
				{
					$sql = $sql.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
				}*/
				$sql1 = $sql1." From rack_recovery_records A
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid
							LEFT JOIN users U ON U.UserId = A.CreatedBy
							LEFT JOIN pallets P ON P.idPallet = A.idPallet
							LEFT JOIN facility F ON F.FacilityID = A.FacilityID
							LEFT JOIN loads L ON L.LoadId = P.LoadId
							LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
							LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
							LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
							LEFT JOIN disposition DI ON DI.disposition_id = A.DispositionID
							LEFT JOIN shipping_container_serials SCS ON SCS.RackRecoveryRecordID = A.RackRecoveryRecordID
							LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
							LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
							LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
							LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage";
							$sql1 = $sql1." where A.IsCommitted=1 ";
							if($data['LoadID'] != '')
								$sql1 = $sql1." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
							if($data['idCustomer'] != '')
							{
								$idCustomer = '';
								for($i=0;$i<count($data['idCustomer']);$i++) {
									$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
								}
								$idCustomer = rtrim($idCustomer, ",");
								$sql1 = $sql1." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
							}
							if($data['CRDatefrom'] != '')
								$sql1 = $sql1." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
							if($data['FacilityID'] != '')
							{
								$FacilityID = '';
								for($i=0;$i<count($data['FacilityID']);$i++) {
									$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
								}
								$FacilityID = rtrim($FacilityID, ",");
								$sql1 = $sql1." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
							}
							if($data['ContainerID'] != '')
								$sql1 = $sql1." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
							if($data['ACDatefrom'] != '')
								$sql1 = $sql1." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
							if($data['SerialNumber'] != '')
								$sql1 = $sql1." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
							if($data['UniversalModelNumber'] != '')
								$sql1 = $sql1." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
							if($data['DispositionID'] != '')
							{
								$dispositionID = '';
								for($i=0;$i<count($data['DispositionID']);$i++) {
									$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
								}
								$dispositionID = rtrim($dispositionID, ",");
								$sql1 = $sql1." AND A.DispositionID IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
							}
							if($data['CustomerType'] != '')
							{
								$CustomerType = '';
								for($i=0;$i<count($data['CustomerType']);$i++) {
									$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
								}
								$CustomerType = rtrim($CustomerType, ",");
								$sql1 = $sql1." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
							}
							if($data['binid'] != '')
								$sql1 = $sql1." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
							/*if($data['workflow_id'] != '')
							{
								$workflow_id = '';
								for($i=0;$i<count($data['workflow_id']);$i++) {
									$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
								}
								$workflow_id = rtrim($workflow_id, ",");
								$sql = $sql." AND A.RecentWorkflowID IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}*/
							if($data['Parttype'] != '')
							{
								$Parttype = '';
								for($i=0;$i<count($data['Parttype']);$i++) {
									$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
								}
								$Parttype = rtrim($Parttype, ",");
								//$Parttype = str_replace($Parttype, '\'');
								//$Parttype = str_replace('\'',"'",$Parttype);
								$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
								$sql1 = $sql1." AND pt.parttype IN ( ".$Parttype.")";
							}

							// get data from assembly records
							$sql2 = "select A.SerialNumber as `SerialNumber`";

							if($data['assetbinid'] == 'true')
							{
								$sql2 = $sql2.", CP.BinName as `Bin ID`";
							}
							if($data['assetTicketid'] == 'true')
							{
								$sql2 = $sql2.", L.LoadId as `Inbound Ticket ID`";
							}
							if($data['assetcid'] == 'true')
							{
								$sql2 = $sql2.", P.idPallet as `Inbound Container ID`";
							}
							if($data['assetsourcecus'] == 'true')
							{
								$sql2 = $sql2.", SC.CustomerName as `Source`";
							}
							if($data['assetmpn'] == 'true')
							{
								$sql2 = $sql2.", A.MPN as `MPN`";
							}
							if($data['assetdisposition'] == 'true')
							{
								$sql2 = $sql2.", DI.disposition as `Disposition`";
							}
							if($data['assetfacility'] == 'true')
							{
								$sql2 = $sql2.", F.FacilityName as `Facility`";
							}
							if($data['assetcreatedby'] == 'true')
							{
								$sql2 = $sql2.", U.FirstName,U.LastName";
							}
							if($data['assetcreateddate'] == 'true')
							{
								$sql2 = $sql2.", A.CreatedDate";
							}
							if($data['assetsourcetype'] == 'true')
							{
								$sql2 = $sql2.", SCT.Cumstomertype as `Source Type`";
							}
							if($data['assetouboundcontainer'] == 'true')
							{
								$sql2 = $sql2.", SCS.ShippingContainerID as `Outbound Container ID`";
							}
							if($data['assetouboundticket'] == 'true')
							{
								$sql2 = $sql2.", SCO.ShippingID as `Outbound Ticket ID`";
							}
							if($data['assetparttype'] == 'true')
							{
								$sql2 = $sql2.", pt.parttype as `Part Type`";
							}
							if($data['assetlastdate'] == 'true')
							{
								$sql2 = $sql2.", A.ModifiedDate as `Last Touch Date`";
							}
							if($data['assetouboundRemovalType'] == 'true')
							{
								$sql2 = $sql2.", SHDI.disposition as `Outbound Removal Type`";
							}
							/*if($data['assetouboundDestination'] == 'true')
							{
								$sql1 = $sql1.", SHV.VendorName as `Outbound Destination`";
							}*/
							/*if($data['assetouboundFacility'] == 'true')
							{
								$sql2 = $sql2.", SHF.FacilityName as `Outbound Facility`";
							}*/
							/*if($data['assetouboundSource'] == 'true')
							{
								$sql1 = $sql1.", SC.CustomerName as `Outbound Source`";
							}
							if($data['assetouboundSourceType'] == 'true')
							{
								$sql1 = $sql1.", SCT.Cumstomertype as `Outbound Source Type`";
							}*/
							if($data['assetouboundMPN'] == 'true')
							{
								$sql2 = $sql2.", SCS.UniversalModelNumber as `Outbound MPN`";
							}
							if($data['assetouboundContainerType'] == 'true')
							{
								$sql2 = $sql2.", SHP.packageName as `Outbound Container Type`";
							}
							/*if($data['assetouboundSanitizationVerID'] == 'true')
							{
								$sql = $sql.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
							}*/
							$sql2 = $sql2." From assembly_recovery_records A
										LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid
										LEFT JOIN users U ON U.UserId = A.CreatedBy
										LEFT JOIN pallets P ON P.idPallet = A.idPallet
										LEFT JOIN facility F ON F.FacilityID = A.FacilityID
										LEFT JOIN loads L ON L.LoadId = P.LoadId
										LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
										LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
										LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
										LEFT JOIN disposition DI ON DI.disposition_id = A.DispositionID
										LEFT JOIN shipping_container_serials SCS ON SCS.AssemblyRecoveryRecordID = A.AssemblyRecoveryRecordID
										LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
										LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
										LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
										LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage";
										$sql2 = $sql2." where A.IsCommitted=1 ";
										if($data['LoadID'] != '')
											$sql2 = $sql2." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
										if($data['idCustomer'] != '')
										{
											$idCustomer = '';
											for($i=0;$i<count($data['idCustomer']);$i++) {
												$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
											}
											$idCustomer = rtrim($idCustomer, ",");
											$sql2 = $sql2." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
										}
										if($data['CRDatefrom'] != '')
											$sql2 = $sql2." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
										if($data['FacilityID'] != '')
										{
											$FacilityID = '';
											for($i=0;$i<count($data['FacilityID']);$i++) {
												$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
											}
											$FacilityID = rtrim($FacilityID, ",");
											$sql2 = $sql2." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
										}
										if($data['ContainerID'] != '')
											$sql2 = $sql2." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
										if($data['ACDatefrom'] != '')
											$sql2 = $sql2." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
										if($data['SerialNumber'] != '')
											$sql2 = $sql2." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
										if($data['UniversalModelNumber'] != '')
											$sql2 = $sql2." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
										if($data['DispositionID'] != '')
										{
											$dispositionID = '';
											for($i=0;$i<count($data['DispositionID']);$i++) {
												$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
											}
											$dispositionID = rtrim($dispositionID, ",");
											$sql2 = $sql2." AND A.DispositionID IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
										}
										if($data['CustomerType'] != '')
										{
											$CustomerType = '';
											for($i=0;$i<count($data['CustomerType']);$i++) {
												$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
											}
											$CustomerType = rtrim($CustomerType, ",");
											$sql2 = $sql2." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
										}
										if($data['binid'] != '')
											$sql2 = $sql2." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
										/*if($data['workflow_id'] != '')
										{
											$workflow_id = '';
											for($i=0;$i<count($data['workflow_id']);$i++) {
												$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
											}
											$workflow_id = rtrim($workflow_id, ",");
											$sql = $sql." AND A.RecentWorkflowID IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}*/
										if($data['Parttype'] != '')
										{
											$Parttype = '';
											for($i=0;$i<count($data['Parttype']);$i++) {
												$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
											}
											$Parttype = rtrim($Parttype, ",");
											//$Parttype = str_replace($Parttype, '\'');
											//$Parttype = str_replace('\'',"'",$Parttype);
											$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
											$sql2 = $sql2." AND pt.parttype IN ( ".$Parttype.")";
										}


							// get data from component records
							$sql3 = "select A.SerialNumber as `SerialNumber`";

							if($data['assetbinid'] == 'true')
							{
								$sql3 = $sql3.", CP.BinName as `Bin ID`";
							}
							if($data['assetTicketid'] == 'true')
							{
								$sql3 = $sql3.", L.LoadId as `Inbound Ticket ID`";
							}
							if($data['assetcid'] == 'true')
							{
								$sql3 = $sql3.", P.idPallet as `Inbound Container ID`";
							}
							if($data['assetsourcecus'] == 'true')
							{
								$sql3 = $sql3.", SC.CustomerName as `Source`";
							}
							if($data['assetmpn'] == 'true')
							{
								$sql3 = $sql3.", A.MPN as `MPN`";
							}
							if($data['assetdisposition'] == 'true')
							{
								$sql3 = $sql3.", DI.disposition as `Disposition`";
							}
							if($data['assetfacility'] == 'true')
							{
								$sql3 = $sql3.", F.FacilityName as `Facility`";
							}
							if($data['assetcreatedby'] == 'true')
							{
								$sql3 = $sql3.", U.FirstName,U.LastName";
							}
							if($data['assetcreateddate'] == 'true')
							{
								$sql3 = $sql3.", A.CreatedDate";
							}
							if($data['assetsourcetype'] == 'true')
							{
								$sql3 = $sql3.", SCT.Cumstomertype as `Source Type`";
							}
							if($data['assetouboundcontainer'] == 'true')
							{
								$sql3 = $sql3.", SCS.ShippingContainerID as `Outbound Container ID`";
							}
							if($data['assetouboundticket'] == 'true')
							{
								$sql3 = $sql3.", SCO.ShippingID as `Outbound Ticket ID`";
							}
							if($data['assetparttype'] == 'true')
							{
								$sql3 = $sql3.", pt.parttype as `Part Type`";
							}
							if($data['assetlastdate'] == 'true')
							{
								$sql3 = $sql3.", A.ModifiedDate as `Last Touch Date`";
							}
							if($data['assetouboundRemovalType'] == 'true')
							{
								$sql3 = $sql3.", SHDI.disposition as `Outbound Removal Type`";
							}
							/*if($data['assetouboundDestination'] == 'true')
							{
								$sql1 = $sql1.", SHV.VendorName as `Outbound Destination`";
							}*/
							/*if($data['assetouboundFacility'] == 'true')
							{
								$sql3 = $sql3.", SHF.FacilityName as `Outbound Facility`";
							}*/
							/*if($data['assetouboundSource'] == 'true')
							{
								$sql1 = $sql1.", SC.CustomerName as `Outbound Source`";
							}
							if($data['assetouboundSourceType'] == 'true')
							{
								$sql1 = $sql1.", SCT.Cumstomertype as `Outbound Source Type`";
							}*/
							if($data['assetouboundMPN'] == 'true')
							{
								$sql3 = $sql3.", SCS.UniversalModelNumber as `Outbound MPN`";
							}
							if($data['assetouboundContainerType'] == 'true')
							{
								$sql3 = $sql3.", SHP.packageName as `Outbound Container Type`";
							}
							/*if($data['assetouboundSanitizationVerID'] == 'true')
							{
								$sql = $sql.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
							}*/
							$sql3 = $sql3." From component_recovery_records A
										LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid
										LEFT JOIN users U ON U.UserId = A.CreatedBy
										LEFT JOIN pallets P ON P.idPallet = A.idPallet
										LEFT JOIN facility F ON F.FacilityID = A.FacilityID
										LEFT JOIN loads L ON L.LoadId = P.LoadId
										LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
										LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
										LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
										LEFT JOIN disposition DI ON DI.disposition_id = A.DispositionID
										LEFT JOIN shipping_container_serials SCS ON SCS.ComponentRecoveryRecordID = A.ComponentRecoveryRecordID
										LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
										LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
										LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
										LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage";
										$sql3 = $sql3." where A.IsCommitted=1 ";
										if($data['LoadID'] != '')
											$sql3 = $sql3." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
										if($data['idCustomer'] != '')
										{
											$idCustomer = '';
											for($i=0;$i<count($data['idCustomer']);$i++) {
												$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
											}
											$idCustomer = rtrim($idCustomer, ",");
											$sql3 = $sql3." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
										}
										if($data['CRDatefrom'] != '')
											$sql3 = $sql3." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
										if($data['FacilityID'] != '')
										{
											$FacilityID = '';
											for($i=0;$i<count($data['FacilityID']);$i++) {
												$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
											}
											$FacilityID = rtrim($FacilityID, ",");
											$sql3 = $sql3." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
										}
										if($data['ContainerID'] != '')
											$sql3 = $sql3." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
										if($data['ACDatefrom'] != '')
											$sql3 = $sql3." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
										if($data['SerialNumber'] != '')
											$sql3 = $sql3." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
										if($data['UniversalModelNumber'] != '')
											$sql3 = $sql3." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
										if($data['DispositionID'] != '')
										{
											$dispositionID = '';
											for($i=0;$i<count($data['DispositionID']);$i++) {
												$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
											}
											$dispositionID = rtrim($dispositionID, ",");
											$sql3 = $sql3." AND A.DispositionID IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
										}
										if($data['CustomerType'] != '')
										{
											$CustomerType = '';
											for($i=0;$i<count($data['CustomerType']);$i++) {
												$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
											}
											$CustomerType = rtrim($CustomerType, ",");
											$sql3 = $sql3." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
										}
										if($data['binid'] != '')
											$sql3 = $sql3." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
										/*if($data['workflow_id'] != '')
										{
											$workflow_id = '';
											for($i=0;$i<count($data['workflow_id']);$i++) {
												$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
											}
											$workflow_id = rtrim($workflow_id, ",");
											$sql = $sql." AND A.RecentWorkflowID IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}*/
										if($data['Parttype'] != '')
										{
											$Parttype = '';
											for($i=0;$i<count($data['Parttype']);$i++) {
												$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
											}
											$Parttype = rtrim($Parttype, ",");
											//$Parttype = str_replace($Parttype, '\'');
											//$Parttype = str_replace('\'',"'",$Parttype);
											$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
											$sql3 = $sql3." AND pt.parttype IN ( ".$Parttype.")";
										}


													$sql = "select * from (".$sql1." UNION ALL ".$sql2." UNION ALL ".$sql3.") as result ";
				$limit = 10*$data['Currentpage'];
				$sql = $sql." LIMIT ".$limit.",10";
				//echo $sql;exit;
				/*$json['Success'] = false;
				$json['Result'] = $sql;
				return json_encode($json);*/
				mysqli_query($this->connectionlink,"SET SQL_BIG_SELECTS=1");
				$query = mysqli_query($this->connectionlink,$sql);

				if(mysqli_error($this->connectionlink))
				{
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				else
				{
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$i = 0;
						while($row = mysqli_fetch_assoc($query)) {
							$_SESSION['searchcustomer'] = '';
							if($data['CustomerID'] != '')
							{
								$_SESSION['searchcustomer'] = $data['CustomerID'];
							}
							if($data['assetcreatedby'] == 'true')
							{
								$row['Created By'] = $row['FirstName']." ".$row['LastName'];
								$row['Created By'] = strtolower($row['Created By']);
							}
							if($data['assetcreateddate'] == 'true')
							{
								$date1 = explode(" ",$row['DateCreated']);
								$date2 = explode("-",$date1[0]);
								$date = $date2[1]."/".$date2[2]."/".$date2[0];
								$date = date("M j, Y g:i a", strtotime($row['DateCreated']));
								//$time = date("g:i a", strtotime($row['DateCreated']));
								//$row['Created Date'] = $date." ".$time;
								$row['Created Date'] = $date;
								unset($row['DateCreated']);
							}
							if($data['assetscanid'] != 'true')
							{
								unset($row['Scan ID']);
							}
							//unset($row['AuditResultID']);
							unset($row['RefCustomerName1']);
							unset($row['FirstName']);
							unset($row['LastName']);
							$result[$i] = $row;
							$resultpdf[$i] = $row;
							$i++;
						}
						//$asset_array = array('Scan ID'=>'1' ,'Serial Number'=>'2' ,'Missing Description'=>'3' ,'Load ID'=>'4','Processed Date'=>'5','Product Class'=>'6','Received Date'=>'7','Product Category'=>'8','Asset Tag'=>'9','Configuration Note'=>'10','Customer'=>'11','Ref Customer'=>'12','Manufacturer'=>'13','Short Description'=>'14','Functional Status'=>'15','Failure Description'=>'16','Cosmetic Grade'=>'17','Cosmetic Description'=>'18','Location'=>'19','Item Sold'=>'20', 'Method of Erase'=>'21', 'Technician'=>'22', 'Facility'=>'23', 'Sale Price'=>'24', 'Slot'=>'25', 'BuildType'=>'26', 'IPAddress'=>'27','RackGrid'=>'28','FinalTkt'=>'29','Pallet ID'=>'30','User'=>'31','Model'=>'32','Additional Notes'=>'33','Erasure Date'=>'34','Mod Cosmetic Grade'=>'35','Weight'=>'36','Product Name'=>'37','Package Type'=>'38','Reporting State'=>'39','Disposition'=>'40','Custom Pallet ID'=>'41','Sale Date'=>42,'Updated Time'=>43,'Created Time'=>44,'Division'=>45,'Payment Code'=>'46','Invoice Code'=>'47','AWS ID'=>'48','Custom Pallet ID'=>'49','Shipping ID'=>'50');
						$json['Success'] = true;
						$json['Result'] = $result;
						$json['Result1'] = $asset;
						$json['assetcount'] = $rowcount['assetcount'];
						unset($_SESSION['search_data123']);
						$_SESSION['search_data123'] = $data;
						return json_encode($json);
					}
					else {
						$json['Success'] = false;
						$json['Result'] = "No Serials Available";
						return json_encode($json);
					}
				}
			/*}*/
		}
		catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function AssetSearchBak($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['assetscanid']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			if($data['CRDatefrom'] != '')
			{
				$chkdt = $data['CRDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['CRDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['CRDateto'] != '')
			{
				$chkdt = $data['CRDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['CRDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ACDatefrom'] != '')
			{
				$chkdt = $data['ACDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ACDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ACDateto'] != '')
			{
				$chkdt = $data['ACDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ACDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['CRDateto'] == '')
			{
				$data['CRDateto1'] = date('Y-m-d H:i', strtotime($data['CRDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['CRDateto1'] = date('Y-m-d H:i', strtotime($data['CRDateto'] . ' + 1 day'));
			}
			if($data['ACDateto'] == '')
			{
				$data['ACDateto1'] = date('Y-m-d H:i', strtotime($data['ACDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['ACDateto1'] = date('Y-m-d H:i', strtotime($data['ACDateto'] . ' + 1 day'));
			}
			if($data['Currentpage'] == 0)
			{
				$sqlcount = $sqlcount."Select count(distinct(A.AssetScanID)) as assetcount From asset A
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
				LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id";
				$sqlcount = $sqlcount." where
				A.AccountID = '".$_SESSION['user']['AccountID']."'";
				if($data['LoadID'] != '')
					$sqlcount = $sqlcount." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
					if($data['idCustomer'] != '')
					{
						$idCustomer = '';
						for($i=0;$i<count($data['idCustomer']);$i++) {
							$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
						}
						$idCustomer = rtrim($idCustomer, ",");
						$sqlcount = $sqlcount." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
					}
					if($data['CRDatefrom'] != '')
						$sqlcount = $sqlcount." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
					if($data['FacilityID'] != '')
					{
						$FacilityID = '';
						for($i=0;$i<count($data['FacilityID']);$i++) {
							$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
						}
						$FacilityID = rtrim($FacilityID, ",");
						$sqlcount = $sqlcount." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
					}
					if($data['ContainerID'] != '')
						$sqlcount = $sqlcount." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
					if($data['ACDatefrom'] != '')
						$sqlcount = $sqlcount." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
					if($data['SerialNumber'] != '')
						$sqlcount = $sqlcount." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
					if($data['UniversalModelNumber'] != '')
						$sqlcount = $sqlcount." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
					if($data['DispositionID'] != '')
					{
						$dispositionID = '';
						for($i=0;$i<count($data['DispositionID']);$i++) {
							$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
						}
						$dispositionID = rtrim($dispositionID, ",");
						$sqlcount = $sqlcount." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
					}
					if($data['CustomerType'] != '')
					{
						$CustomerType = '';
						for($i=0;$i<count($data['CustomerType']);$i++) {
							$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
						}
						$CustomerType = rtrim($CustomerType, ",");
						$sqlcount = $sqlcount." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
					}
					if($data['binid'] != '')
						$sqlcount = $sqlcount." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
					if($data['workflow_id'] != '')
					{
						$workflow_id = '';
						for($i=0;$i<count($data['workflow_id']);$i++) {
							$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
						}
						$workflow_id = rtrim($workflow_id, ",");
						$sqlcount = $sqlcount." AND A.RecentWorkflowID IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
					}
					if($data['Parttype'] != '')
					{
						$Parttype = '';
						for($i=0;$i<count($data['Parttype']);$i++) {
							$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
						}
						$Parttype = rtrim($Parttype, ",");
						//$Parttype = str_replace($Parttype, '\'');
						//$Parttype = str_replace('\'',"'",$Parttype);
						$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
						$sqlcount = $sqlcount." AND A.part_type IN ( ".$Parttype.")";
					}
					/*$json['Success'] = false;
					$json['Result'] = $sqlcount;
					return json_encode($json);*/
				$querycount = mysqli_query($this->connectionlink,$sqlcount);
				$rowcount = mysqli_fetch_assoc($querycount);
			}
				$sql = "Select ";
				$sql = $sql."distinct(A.AssetScanID) as `Scan ID`";
				if($data['assetsn'] == 'true')
				{
					$sql = $sql.", A.SerialNumber as `SN`";
				}
				if($data['assetbinid'] == 'true')
				{
					$sql = $sql.", CP.BinName as `Bin ID`";
				}
				if($data['assetTicketid'] == 'true')
				{
					$sql = $sql.", L.LoadId as `Inbound Ticket ID`";
				}
				if($data['assetcid'] == 'true')
				{
					$sql = $sql.", P.idPallet as `Inbound Container ID`";
				}
				if($data['assetsourcecus'] == 'true')
				{
					$sql = $sql.", SC.CustomerName as `Source`";
				}
				if($data['assetmpn'] == 'true')
				{
					$sql = $sql.", A.UniversalModelNumber as `MPN`";
				}
				if($data['assetdisposition'] == 'true')
				{
					$sql = $sql.", DI.disposition as `Disposition`";
				}
				if($data['assetfacility'] == 'true')
				{
					$sql = $sql.", F.FacilityName as `Facility`";
				}
				if($data['assetcreatedby'] == 'true')
				{
					$sql = $sql.", U.FirstName,U.LastName";
				}
				if($data['assetcreateddate'] == 'true')
				{
					$sql = $sql.", A.CreatedDate";
				}
				if($data['assetworkflow'] == 'true')
				{
					$sql = $sql.", BRW.workflow";
				}
				if($data['assetcustomid'] == 'true')
				{
					$sql = $sql.", A.custom_id";
				}
				if($data['assetsourcetype'] == 'true')
				{
					$sql = $sql.", SCT.Cumstomertype as `Source Type`";
				}
				if($data['assetouboundcontainer'] == 'true')
				{
					$sql = $sql.", SCS.ShippingContainerID as `Outbound Container ID`";
				}
				if($data['assetouboundticket'] == 'true')
				{
					$sql = $sql.", SCO.ShippingID as `Outbound Ticket ID`";
				}
				if($data['assetparttype'] == 'true')
				{
					$sql = $sql.", A.part_type as `Part Type`";
				}
				if($data['assetlastdate'] == 'true')
				{
					$sql = $sql.", A.ModifiedDate as `Last Touch Date`";
				}
				if($data['assetouboundRemovalType'] == 'true')
				{
					$sql = $sql.", SHDI.disposition as `Outbound Removal Type`";
				}
				if($data['assetouboundDestination'] == 'true')
				{
					$sql = $sql.", SHV.VendorName as `Outbound Destination`";
				}
				if($data['assetouboundFacility'] == 'true')
				{
					$sql = $sql.", SHF.FacilityName as `Outbound Facility`";
				}
				if($data['assetouboundSource'] == 'true')
				{
					$sql = $sql.", SC.CustomerName as `Outbound Source`";
				}
				if($data['assetouboundSourceType'] == 'true')
				{
					$sql = $sql.", SCT.Cumstomertype as `Outbound Source Type`";
				}
				if($data['assetouboundMPN'] == 'true')
				{
					$sql = $sql.", SCS.UniversalModelNumber as `Outbound MPN`";
				}
				if($data['assetouboundContainerType'] == 'true')
				{
					$sql = $sql.", SHP.packageName as `Outbound Container Type`";
				}
				if($data['assetouboundSanitizationVerID'] == 'true')
				{
					$sql = $sql.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
				}
				$sql = $sql." From asset A
							LEFT JOIN users U ON U.UserId = A.CreatedBy
							LEFT JOIN pallets P ON P.idPallet = A.idPallet
							LEFT JOIN facility F ON F.FacilityID = A.FacilityID
							LEFT JOIN loads L ON L.LoadId = P.LoadId
							LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
							LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
							LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
							LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
							LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
							LEFT JOIN workflow BRW ON BRW.workflow_id = A.RecentWorkflowID
							LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
							LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
							LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
							LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
							LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
							LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
							LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage";
							$sql = $sql." where
							A.AccountID = '".$_SESSION['user']['AccountID']."'";
							if($data['LoadID'] != '')
								$sql = $sql." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
							if($data['idCustomer'] != '')
							{
								$idCustomer = '';
								for($i=0;$i<count($data['idCustomer']);$i++) {
									$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
								}
								$idCustomer = rtrim($idCustomer, ",");
								$sql = $sql." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
							}
							if($data['CRDatefrom'] != '')
								$sql = $sql." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
							if($data['FacilityID'] != '')
							{
								$FacilityID = '';
								for($i=0;$i<count($data['FacilityID']);$i++) {
									$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
								}
								$FacilityID = rtrim($FacilityID, ",");
								$sql = $sql." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
							}
							if($data['ContainerID'] != '')
								$sql = $sql." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
							if($data['ACDatefrom'] != '')
								$sql = $sql." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
							if($data['SerialNumber'] != '')
								$sql = $sql." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
							if($data['UniversalModelNumber'] != '')
								$sql = $sql." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
							if($data['DispositionID'] != '')
							{
								$dispositionID = '';
								for($i=0;$i<count($data['DispositionID']);$i++) {
									$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
								}
								$dispositionID = rtrim($dispositionID, ",");
								$sql = $sql." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
							}
							if($data['CustomerType'] != '')
							{
								$CustomerType = '';
								for($i=0;$i<count($data['CustomerType']);$i++) {
									$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
								}
								$CustomerType = rtrim($CustomerType, ",");
								$sql = $sql." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
							}
							if($data['binid'] != '')
								$sql = $sql." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
							if($data['workflow_id'] != '')
							{
								$workflow_id = '';
								for($i=0;$i<count($data['workflow_id']);$i++) {
									$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
								}
								$workflow_id = rtrim($workflow_id, ",");
								$sql = $sql." AND A.RecentWorkflowID IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}
							if($data['Parttype'] != '')
							{
								$Parttype = '';
								for($i=0;$i<count($data['Parttype']);$i++) {
									$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
								}
								$Parttype = rtrim($Parttype, ",");
								//$Parttype = str_replace($Parttype, '\'');
								//$Parttype = str_replace('\'',"'",$Parttype);
								$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
								$sql = $sql." AND A.part_type IN ( ".$Parttype.")";
							}
				$limit = 10*$data['Currentpage'];
				$sql = $sql." LIMIT ".$limit.",10";
				/*$json['Success'] = false;
				$json['Result'] = $sql;
				return json_encode($json);*/
				mysqli_query($this->connectionlink,"SET SQL_BIG_SELECTS=1");
				$query = mysqli_query($this->connectionlink,$sql);

				if(mysqli_error($this->connectionlink))
				{
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				else
				{
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$i = 0;
						while($row = mysqli_fetch_assoc($query)) {
							$_SESSION['searchcustomer'] = '';
							if($data['CustomerID'] != '')
							{
								$_SESSION['searchcustomer'] = $data['CustomerID'];
							}
							if($data['assetcreatedby'] == 'true')
							{
								$row['Created By'] = $row['FirstName']." ".$row['LastName'];
								$row['Created By'] = strtolower($row['Created By']);
							}
							if($data['assetcreateddate'] == 'true')
							{
								$date1 = explode(" ",$row['DateCreated']);
								$date2 = explode("-",$date1[0]);
								$date = $date2[1]."/".$date2[2]."/".$date2[0];
								$date = date("M j, Y g:i a", strtotime($row['DateCreated']));
								//$time = date("g:i a", strtotime($row['DateCreated']));
								//$row['Created Date'] = $date." ".$time;
								$row['Created Date'] = $date;
								unset($row['DateCreated']);
							}
							if($data['assetscanid'] != 'true')
							{
								unset($row['Scan ID']);
							}
							//unset($row['AuditResultID']);
							unset($row['RefCustomerName1']);
							unset($row['FirstName']);
							unset($row['LastName']);
							$result[$i] = $row;
							$resultpdf[$i] = $row;
							$i++;
						}
						//$asset_array = array('Scan ID'=>'1' ,'Serial Number'=>'2' ,'Missing Description'=>'3' ,'Load ID'=>'4','Processed Date'=>'5','Product Class'=>'6','Received Date'=>'7','Product Category'=>'8','Asset Tag'=>'9','Configuration Note'=>'10','Customer'=>'11','Ref Customer'=>'12','Manufacturer'=>'13','Short Description'=>'14','Functional Status'=>'15','Failure Description'=>'16','Cosmetic Grade'=>'17','Cosmetic Description'=>'18','Location'=>'19','Item Sold'=>'20', 'Method of Erase'=>'21', 'Technician'=>'22', 'Facility'=>'23', 'Sale Price'=>'24', 'Slot'=>'25', 'BuildType'=>'26', 'IPAddress'=>'27','RackGrid'=>'28','FinalTkt'=>'29','Pallet ID'=>'30','User'=>'31','Model'=>'32','Additional Notes'=>'33','Erasure Date'=>'34','Mod Cosmetic Grade'=>'35','Weight'=>'36','Product Name'=>'37','Package Type'=>'38','Reporting State'=>'39','Disposition'=>'40','Custom Pallet ID'=>'41','Sale Date'=>42,'Updated Time'=>43,'Created Time'=>44,'Division'=>45,'Payment Code'=>'46','Invoice Code'=>'47','AWS ID'=>'48','Custom Pallet ID'=>'49','Shipping ID'=>'50');
						$json['Success'] = true;
						$json['Result'] = $result;
						$json['Result1'] = $asset;
						$json['assetcount'] = $rowcount['assetcount'];
						unset($_SESSION['search_data123']);
						$_SESSION['search_data123'] = $data;
						return json_encode($json);
					}
					else {
						$json['Success'] = false;
						$json['Result'] = "No Serials Available";
						return json_encode($json);
					}
				}
			/*}*/
		}
		catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function PalletSearch($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['palletscanid']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			if($data['CRDatefrom'] != '')
			{
				$chkdt = $data['CRDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['CRDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['CRDateto'] != '')
			{
				$chkdt = $data['CRDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['CRDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ACDatefrom'] != '')
			{
				$chkdt = $data['ACDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ACDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ACDateto'] != '')
			{
				$chkdt = $data['ACDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ACDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['CRDateto'] == '')
			{
				$data['CRDateto1'] = date('Y-m-d H:i', strtotime($data['CRDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['CRDateto1'] = date('Y-m-d H:i', strtotime($data['CRDateto'] . ' + 1 day'));
			}
			if($data['ACDateto'] == '')
			{
				$data['ACDateto1'] = date('Y-m-d H:i', strtotime($data['ACDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['ACDateto1'] = date('Y-m-d H:i', strtotime($data['ACDateto'] . ' + 1 day'));
			}
			if($data['Currentpage'] == 0)
			{
				$sqlcount = $sqlcount."Select count(distinct(P.idPallet)) as palletcount From pallets P
				LEFT JOIN asset A  ON A.idPallet = P.idPallet
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN facility F ON F.FacilityID = P.PalletFacilityID
				LEFT JOIN pallet_items PI ON PI.palletId = P.idPallet
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
				LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
				LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
				$sqlcount = $sqlcount." where
				1 ";
				if($data['LoadID'] != '')
					$sqlcount = $sqlcount." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
				if($data['idCustomer'] != '')
				{
					$idCustomer = '';
					for($i=0;$i<count($data['idCustomer']);$i++) {
						$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
					}
					$idCustomer = rtrim($idCustomer, ",");
					$sqlcount = $sqlcount." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
				}
				if($data['CRDatefrom'] != '')
					$sqlcount = $sqlcount." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
				if($data['FacilityID'] != '')
				{
					$FacilityID = '';
					for($i=0;$i<count($data['FacilityID']);$i++) {
						$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
					}
					$FacilityID = rtrim($FacilityID, ",");
					$sqlcount = $sqlcount." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
				}
				if($data['ContainerID'] != '')
					$sqlcount = $sqlcount." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
				if($data['ACDatefrom'] != '')
					$sqlcount = $sqlcount." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
				if($data['AssetScanID'] != '')
					$sqlcount = $sqlcount." AND A.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				if($data['SerialNumber'] != '')
					$sqlcount = $sqlcount." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
				if($data['UniversalModelNumber'] != '')
					$sqlcount = $sqlcount." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
				if($data['DispositionID'] != '')
				{
					$dispositionID = '';
					for($i=0;$i<count($data['DispositionID']);$i++) {
						$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
					}
					$dispositionID = rtrim($dispositionID, ",");
					$sqlcount = $sqlcount." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
				}
				if($data['CustomerType'] != '')
				{
					$CustomerType = '';
					for($i=0;$i<count($data['CustomerType']);$i++) {
						$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
					}
					$CustomerType = rtrim($CustomerType, ",");
					$sqlcount = $sqlcount." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
				}
				if($data['binid'] != '')
					$sqlcount = $sqlcount." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
				if($data['workflow_id'] != '')
				{
					$workflow_id = '';
					for($i=0;$i<count($data['workflow_id']);$i++) {
						$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
					}
					$workflow_id = rtrim($workflow_id, ",");
					$sqlcount = $sqlcount." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
				}
				if($data['Parttype'] != '')
				{
					$Parttype = '';
					for($i=0;$i<count($data['Parttype']);$i++) {
						$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
					}
					$Parttype = rtrim($Parttype, ",");
					//$Parttype = str_replace($Parttype, '\'');
					//$Parttype = str_replace('\'',"'",$Parttype);
					$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
					$sqlcount = $sqlcount." AND A.part_type IN ( ".$Parttype.")";
				}
					/*$json['Success'] = false;
					$json['Result'] = $sqlcount;
					return json_encode($json);*/
				$querycount = mysqli_query($this->connectionlink,$sqlcount);
				$rowcount = mysqli_fetch_assoc($querycount);
			}
				$sql = "Select ";
				$sql = $sql."distinct(P.idPallet) as `Inbound Container ID`";
				if($data['PTicketid'] == 'true')
				{
					$sql = $sql.", L.LoadId as `Inbound Ticket ID`";
				}
				if($data['Psourcecus'] == 'true')
				{
					$sql = $sql.", SC.CustomerName as `Source`";
				}
				if($data['Ptotalweight'] == 'true')
				{
					$sql = $sql.", PI.totalWeight as `Total Weight`";
				}
				if($data['Pquantity'] == 'true')
				{
					$sql = $sql.", PI.quantity as `Quantity`";
				}
				if($data['Presdate'] == 'true')
				{
					$sql = $sql.", P.ReceivedDate as `Received Date`";
				}
				if($data['PTicketdes'] == 'true')
				{
					$sql = $sql.", L.LoadDescription as `Ticket Description`";
				}
				if($data['Pverified'] == 'true')
				{
					$sql = $sql.", P.Verified as `Verified`";
				}
				if($data['Pfacility'] == 'true')
				{
					$sql = $sql.", F.FacilityName as `Facility`";
				}
				$sql = $sql." From pallets P
							LEFT JOIN asset A  ON A.idPallet = P.idPallet
							LEFT JOIN users U ON U.UserId = A.CreatedBy
							LEFT JOIN facility F ON F.FacilityID = P.PalletFacilityID
							LEFT JOIN pallet_items PI ON PI.palletId = P.idPallet
							LEFT JOIN loads L ON L.LoadId = P.LoadId
							LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
							LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
							LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
							LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
							LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
							$sql = $sql." where
							1 ";
							if($data['LoadID'] != '')
								$sql = $sql." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
							if($data['idCustomer'] != '')
							{
								$idCustomer = '';
								for($i=0;$i<count($data['idCustomer']);$i++) {
									$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
								}
								$idCustomer = rtrim($idCustomer, ",");
								$sql = $sql." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
							}
							if($data['CRDatefrom'] != '')
								$sql = $sql." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
							if($data['FacilityID'] != '')
							{
								$FacilityID = '';
								for($i=0;$i<count($data['FacilityID']);$i++) {
									$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
								}
								$FacilityID = rtrim($FacilityID, ",");
								$sql = $sql." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
							}
							if($data['ContainerID'] != '')
								$sql = $sql." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
							if($data['ACDatefrom'] != '')
								$sql = $sql." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
							if($data['AssetScanID'] != '')
								$sql = $sql." AND A.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
							if($data['SerialNumber'] != '')
								$sql = $sql." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
							if($data['UniversalModelNumber'] != '')
								$sql = $sql." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
							if($data['DispositionID'] != '')
							{
								$dispositionID = '';
								for($i=0;$i<count($data['DispositionID']);$i++) {
									$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
								}
								$dispositionID = rtrim($dispositionID, ",");
								$sql = $sql." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
							}
							if($data['CustomerType'] != '')
							{
								$CustomerType = '';
								for($i=0;$i<count($data['CustomerType']);$i++) {
									$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
								}
								$CustomerType = rtrim($CustomerType, ",");
								$sql = $sql." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
							}
							if($data['binid'] != '')
								$sql = $sql." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
							if($data['workflow_id'] != '')
							{
								$workflow_id = '';
								for($i=0;$i<count($data['workflow_id']);$i++) {
									$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
								}
								$workflow_id = rtrim($workflow_id, ",");
								$sql = $sql." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}
							if($data['Parttype'] != '')
							{
								$Parttype = '';
								for($i=0;$i<count($data['Parttype']);$i++) {
									$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
								}
								$Parttype = rtrim($Parttype, ",");
								//$Parttype = str_replace($Parttype, '\'');
								//$Parttype = str_replace('\'',"'",$Parttype);
								$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
								$sql = $sql." AND A.part_type IN ( ".$Parttype.")";
							}
				$limit = 10*$data['Currentpage'];
				$sql = $sql." LIMIT ".$limit.",10";
				/*$json['Success'] = false;
				$json['Result'] = $sql;
				return json_encode($json);*/
				mysqli_query($this->connectionlink,"SET SQL_BIG_SELECTS=1");
				$query = mysqli_query($this->connectionlink,$sql);

				if(mysqli_error($this->connectionlink))
				{
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink).$sql;
					return json_encode($json);
				}
				else
				{
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$i = 0;
						while($row = mysqli_fetch_assoc($query)) {
							if($data['Pverified'] == 'true')
							{
								if($row['Verified'] == 0)
								{
									$row['Verified'] = 'No';
								}
								if($row['Verified'] == 1)
								{
									$row['Verified'] = 'Yes';
								}
							}
							if($data['Presdate'] == 'true')
							{
								$date = date("M j, Y g:i a", strtotime($row['Received Date']));
								$row['Received Date'] = $date;
							}
							$_SESSION['searchcustomer'] = '';
							if($data['CustomerID'] != '')
							{
								$_SESSION['searchcustomer'] = $data['CustomerID'];
							}
							//unset($row['AuditResultID']);
							unset($row['RefCustomerName1']);
							$result[$i] = $row;
							$resultpdf[$i] = $row;
							$i++;
						}
						/*$asset_array = array('Scan ID'=>'1' ,'Serial Number'=>'2' ,'Missing Description'=>'3' ,'Load ID'=>'4','Processed Date'=>'5','Product Class'=>'6','Received Date'=>'7','Product Category'=>'8','Asset Tag'=>'9','Configuration Note'=>'10','Customer'=>'11','Ref Customer'=>'12','Manufacturer'=>'13','Short Description'=>'14','Functional Status'=>'15','Failure Description'=>'16','Cosmetic Grade'=>'17','Cosmetic Description'=>'18','Location'=>'19','Item Sold'=>'20', 'Method of Erase'=>'21', 'Technician'=>'22', 'Facility'=>'23', 'Sale Price'=>'24', 'Slot'=>'25', 'BuildType'=>'26', 'IPAddress'=>'27','RackGrid'=>'28','FinalTkt'=>'29','Pallet ID'=>'30','User'=>'31','Model'=>'32','Additional Notes'=>'33','Erasure Date'=>'34','Mod Cosmetic Grade'=>'35','Weight'=>'36','Product Name'=>'37','Package Type'=>'38','Reporting State'=>'39','Disposition'=>'40','Custom Pallet ID'=>'41','Sale Date'=>42,'Updated Time'=>43,'Created Time'=>44,'Division'=>45,'Payment Code'=>'46','Invoice Code'=>'47','AWS ID'=>'48','Custom Pallet ID'=>'49','Shipping ID'=>'50');*/
						$json['Success'] = true;
						$json['Result'] = $result;
						$json['Result1'] = $asset;
						$json['palletcount'] = $rowcount['palletcount'];
						unset($_SESSION['search_pdata123']);
						$_SESSION['search_pdata123'] = $data;
						return json_encode($json);
					}
					else {
						$json['Success'] = false;
						$json['Result'] = "No Containers Available";
						return json_encode($json);
					}
				}
			/*}*/
		}
		catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function AssetXLS($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['assetscanid']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			if(isset($_SESSION['searchreport_query'])){
				$sql = $_SESSION['searchreport_query'];
				mysqli_query($this->connectionlink,"SET SQL_BIG_SELECTS=1");
				$query = mysqli_query($this->connectionlink,$sql);

				if(mysqli_error($this->connectionlink))
				{
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				else
				{
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$totalcountQ = mysqli_query($this->connectionlink,"select FOUND_ROWS() AS total_records");
						$totalCountRow = mysqli_fetch_assoc($totalcountQ);
						$rowcount = $totalCountRow['total_records'];
					}
				}
			}else{
				$json['Success'] = false;
				$json['Result'] = "Please run the search again";
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = $rowcount;
			return json_encode($json);
		}
		catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function PalletXLS($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['palletscanid']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			if($data['CRDatefrom'] != '')
			{
				$chkdt = $data['CRDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['CRDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['CRDateto'] != '')
			{
				$chkdt = $data['CRDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['CRDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ACDatefrom'] != '')
			{
				$chkdt = $data['ACDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ACDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ACDateto'] != '')
			{
				$chkdt = $data['ACDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ACDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['CRDateto'] == '')
			{
				$data['CRDateto1'] = date('Y-m-d H:i', strtotime($data['CRDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['CRDateto1'] = date('Y-m-d H:i', strtotime($data['CRDateto'] . ' + 1 day'));
			}
			if($data['ACDateto'] == '')
			{
				$data['ACDateto1'] = date('Y-m-d H:i', strtotime($data['ACDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['ACDateto1'] = date('Y-m-d H:i', strtotime($data['ACDateto'] . ' + 1 day'));
			}
			$sqlcount = $sqlcount."Select count(distinct(P.idPallet)) as palletcount From pallets P
			LEFT JOIN asset A  ON A.idPallet = P.idPallet
			LEFT JOIN users U ON U.UserId = A.CreatedBy
			LEFT JOIN facility F ON F.FacilityID = P.PalletFacilityID
			LEFT JOIN pallet_items PI ON PI.palletId = P.idPallet
			LEFT JOIN loads L ON L.LoadId = P.LoadId
			LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
			LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
			LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
			LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
			LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
			$sqlcount = $sqlcount." where
			1 ";
			if($data['LoadID'] != '')
				$sqlcount = $sqlcount." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
			if($data['idCustomer'] != '')
			{
				$idCustomer = '';
				for($i=0;$i<count($data['idCustomer']);$i++) {
					$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
				}
				$idCustomer = rtrim($idCustomer, ",");
				$sqlcount = $sqlcount." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
			}
			if($data['CRDatefrom'] != '')
				$sqlcount = $sqlcount." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
			if($data['FacilityID'] != '')
			{
				$FacilityID = '';
				for($i=0;$i<count($data['FacilityID']);$i++) {
					$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
				}
				$FacilityID = rtrim($FacilityID, ",");
				$sqlcount = $sqlcount." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
			}
			if($data['ContainerID'] != '')
				$sqlcount = $sqlcount." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
			if($data['ACDatefrom'] != '')
				$sqlcount = $sqlcount." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
			if($data['AssetScanID'] != '')
				$sqlcount = $sqlcount." AND A.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			if($data['SerialNumber'] != '')
				$sqlcount = $sqlcount." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			if($data['UniversalModelNumber'] != '')
				$sqlcount = $sqlcount." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
			if($data['DispositionID'] != '')
			{
				$dispositionID = '';
				for($i=0;$i<count($data['DispositionID']);$i++) {
					$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
				}
				$dispositionID = rtrim($dispositionID, ",");
				$sqlcount = $sqlcount." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
			}
			if($data['CustomerType'] != '')
			{
				$CustomerType = '';
				for($i=0;$i<count($data['CustomerType']);$i++) {
					$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
				}
				$CustomerType = rtrim($CustomerType, ",");
				$sqlcount = $sqlcount." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
			}
			if($data['binid'] != '')
				$sqlcount = $sqlcount." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
			if($data['workflow_id'] != '')
			{
				$workflow_id = '';
				for($i=0;$i<count($data['workflow_id']);$i++) {
					$workflow_id = $workflow_id.$data['workflow_id'][$i].',';
				}
				$workflow_id = rtrim($workflow_id, ",");
				$sqlcount = $sqlcount." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
			}
			if($data['Parttype'] != '')
			{
				$Parttype = '';
				for($i=0;$i<count($data['Parttype']);$i++) {
					$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
				}
				$Parttype = rtrim($Parttype, ",");
				//$Parttype = str_replace($Parttype, '\'');
				//$Parttype = str_replace('\'',"'",$Parttype);
				$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
				$sqlcount = $sqlcount." AND A.part_type IN ( ".$Parttype.")";
			}
				/*$json['Success'] = false;
				$json['Result'] = $sqlcount;
				return json_encode($json);*/
			$querycount = mysqli_query($this->connectionlink,$sqlcount);
			$rowcount = mysqli_fetch_assoc($querycount);
			$json['Success'] = true;
			$json['Result'] = $rowcount['palletcount'];
			return json_encode($json);

		}
		catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
	public function GetSourceType ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			$query = "select Cumstomertype,idCustomertype from customertype where Active = 'Active' order by Cumstomertype ASC";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Source Type available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
	public function GenerateInboundShip($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if($data['FromDate'] != '')
			{
				$chkdt = $data['FromDate'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['FromDate'] = date("Y-m-d",$newdt);
			}
			if($data['toDate'] != '')
			{
				$chkdt = $data['toDate'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['toDate'] = date("Y-m-d",$newdt);
			}
			$data['toDate'] = date('Y-m-d', strtotime($data['toDate'] . ' + 1 day'));
			$sql = "Select count(*) as counts from asset where DateCreated between '".mysqli_real_escape_string($this->connectionlink,$data['FromDate'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['toDate'])."'";
			$query = mysqli_query($this->connectionlink,$sql);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			} else {
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i = 0;
					$row = mysqli_fetch_assoc($query);
					if($row['counts'] == 0) {
						$json['Success'] = false;
						$json['Result'] = 'No Results';
						return json_encode($json);
					}
					$_SESSION['inbound_ship_FromDate'] = $data['FromDate'];
					$_SESSION['inbound_ship_toDate'] = $data['toDate'];
					$json['Success'] = true;
					$json['Result'] = $row;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'No Results';
					return json_encode($json);
				}
			}
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GenerateOnsiteBacklog($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			$sql = "Select count(*) as counts from asset where DateUpdated IS NULL";
			$query = mysqli_query($this->connectionlink,$sql);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			} else {
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i = 0;
					$row = mysqli_fetch_assoc($query);
					if($row['counts'] == 0) {
						$json['Success'] = false;
						$json['Result'] = 'No Results';
						return json_encode($json);
					}
					$json['Success'] = true;
					$json['Result'] = $row;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'No Results';
					return json_encode($json);
				}
			}
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
	public function GetWorkflowList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			$query = "select workflow_id,workflow from workflow where status='Active'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Workflows available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
	public function GetWorkflowListHistory ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			$query = "select workflow_id,workflow from workflow where status='Active' and workflow_id NOT IN (8,9)";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Workflows available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
	public function GetPartTypeList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			if(is_array($data['FacilityID'] && $data['FacilityID'] != ''))
			{
				$FacilityID = '';
				for($i=0;$i<count($data['FacilityID']);$i++) {
					$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
				}
				$FacilityID = rtrim($FacilityID, ",");
			}elseif($data['FacilityID'] != ''){
				$FacilityID = $data['FacilityID'];
			}else{
				$json['Success'] = false;
				$json['Result'] = "Please select Facility";
				return json_encode($json);
			}
			$query = "select distinct(part_type) as Parttype from catlog_creation where FacilityID in (".mysqli_real_escape_string($this->connectionlink,$FacilityID).") order by parttype ASC";
			//echo $query;exit;
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Part Types available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function AssetSearchWorkFlow_bk($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['assetscanid']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'History Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to History Search Page';
				return json_encode($json);
			}
			if($data['ARDatefrom'] != '')
			{
				$chkdt = $data['ARDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ARDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ARDateto'] != '')
			{
				$chkdt = $data['ARDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ARDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ASDatefrom'] != '')
			{
				$chkdt = $data['ASDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ASDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ASDateto'] != '')
			{
				$chkdt = $data['ASDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ASDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ARDateto'] == '')
			{
				$data['ARDateto1'] = date('Y-m-d H:i', strtotime($data['ARDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['ARDateto1'] = date('Y-m-d H:i', strtotime($data['ARDateto'] . ' + 1 day'));
			}
			if($data['ASDateto'] == '')
			{
				$data['ASDateto1'] = date('Y-m-d H:i', strtotime($data['ASDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['ASDateto1'] = date('Y-m-d H:i', strtotime($data['ASDateto'] . ' + 1 day'));
			}
			$totcount = 0;
			if($data['Currentpage'] == 0)
			{
				if($data['Workflow'] != '')
				{
					for($i=0;$i<count($data['Workflow']);$i++) {
						//$workflow_id = $workflow_id.$data['Workflow'][$i].',';
						if($data['Workflow'][$i] == 1)
						{
							$sqlcount1 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_sanitization A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.sanitization_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.sanitization_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount1 = $sqlcount1." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount1 = $sqlcount1." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount1 = $sqlcount1." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount1 = $sqlcount1." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount1 = $sqlcount1." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount1 = $sqlcount1." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount1 = $sqlcount1." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount1 = $sqlcount1." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount1 = $sqlcount1." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount1 = $sqlcount1." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount1 = $sqlcount1." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount1 = $sqlcount1." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount1 = $sqlcount1." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount1 = $sqlcount1." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount1 = $sqlcount1." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount1 = $sqlcount1." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 2)
						{
							$sqlcount2 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_failure_analysis A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.fa_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.fa_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount2 = $sqlcount2." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount2 = $sqlcount2." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount2 = $sqlcount2." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount2 = $sqlcount2." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount2 = $sqlcount2." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount2 = $sqlcount2." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount2 = $sqlcount2." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount2 = $sqlcount2." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount2 = $sqlcount2." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount2 = $sqlcount2." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount2 = $sqlcount2." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount2 = $sqlcount2." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount2 = $sqlcount2." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount2 = $sqlcount2." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount2 = $sqlcount2." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount2 = $sqlcount2." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 3)
						{
							$sqlcount3 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_rma_investigation A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.rma_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.rma_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount3 = $sqlcount3." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount3 = $sqlcount3." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount3 = $sqlcount3." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount3 = $sqlcount3." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount3 = $sqlcount3." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount3 = $sqlcount3." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount3 = $sqlcount3." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount3 = $sqlcount3." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount3 = $sqlcount3." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount3 = $sqlcount3." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount3 = $sqlcount3." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount3 = $sqlcount3." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount3 = $sqlcount3." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount3 = $sqlcount3." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount3 = $sqlcount3." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount3 = $sqlcount3." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 4)
						{
							$sqlcount4 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_harvest A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.harvest_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.harvest_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount4 = $sqlcount4." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount4 = $sqlcount4." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount4 = $sqlcount4." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount4 = $sqlcount4." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount4 = $sqlcount4." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount4 = $sqlcount4." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount4 = $sqlcount4." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount4 = $sqlcount4." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount4 = $sqlcount4." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount4 = $sqlcount4." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount4 = $sqlcount4." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount4 = $sqlcount4." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount4 = $sqlcount4." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount4 = $sqlcount4." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount4 = $sqlcount4." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount4 = $sqlcount4." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 5)
						{
							$sqlcount5 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_repair A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.repair_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.repair_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount5 = $sqlcount5." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount5 = $sqlcount5." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount5 = $sqlcount5." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount5 = $sqlcount5." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount5 = $sqlcount5." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount5 = $sqlcount5." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount5 = $sqlcount5." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount5 = $sqlcount5." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount5 = $sqlcount5." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount5 = $sqlcount5." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount5 = $sqlcount5." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount5 = $sqlcount5." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount5 = $sqlcount5." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount5 = $sqlcount5." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount5 = $sqlcount5." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount5 = $sqlcount5." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 5)
						{
							$sqlcount5 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_repair A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.repair_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.repair_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount5 = $sqlcount5." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount5 = $sqlcount5." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount5 = $sqlcount5." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount5 = $sqlcount5." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount5 = $sqlcount5." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount5 = $sqlcount5." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount5 = $sqlcount5." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount5 = $sqlcount5." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount5 = $sqlcount5." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount5 = $sqlcount5." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount5 = $sqlcount5." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount5 = $sqlcount5." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount5 = $sqlcount5." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount5 = $sqlcount5." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount5 = $sqlcount5." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount5 = $sqlcount5." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 6)
						{
							$sqlcount6 = "Select count(distinct(A.AssetScanID)) as assetcount From asset A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.FirstReceivedCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount6 = $sqlcount6." where
								A.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount6 = $sqlcount6." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount6 = $sqlcount6." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount6 = $sqlcount6." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount6 = $sqlcount6." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount6 = $sqlcount6." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount6 = $sqlcount6." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount6 = $sqlcount6." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount6 = $sqlcount6." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount6 = $sqlcount6." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount6 = $sqlcount6." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount6 = $sqlcount6." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount6 = $sqlcount6." AND A.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount6 = $sqlcount6." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount6 = $sqlcount6." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount6 = $sqlcount6." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 7)
						{
							$sqlcount7 = "Select count(distinct(A.AssetScanID)) as assetcount From shipping_container_serials A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.FirstReceivedCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = AA.input_id
								LEFT JOIN business_rule BR ON BR.rule_id = AA.rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount7 = $sqlcount7." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount7 = $sqlcount7." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount7 = $sqlcount7." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount7 = $sqlcount7." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount7 = $sqlcount7." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount7 = $sqlcount7." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount7 = $sqlcount7." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount7 = $sqlcount7." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount7 = $sqlcount7." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount7 = $sqlcount7." AND AA.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount7 = $sqlcount7." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount7 = $sqlcount7." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount7 = $sqlcount7." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount7 = $sqlcount7." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount7 = $sqlcount7." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount7 = $sqlcount7." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
					}
					$sqlfinalcount = "Select SUM(assetcount) as assetcount from (";
					if($sqlcount1 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount1.") UNION ALL ";
					}
					if($sqlcount2 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount2.") UNION ALL ";
					}
					if($sqlcount3 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount3.") UNION ALL ";
					}
					if($sqlcount4 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount4.") UNION ALL ";
					}
					if($sqlcount5 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount5.") UNION ALL ";
					}
					if($sqlcount6 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount6.") UNION ALL ";
					}
					if($sqlcount7 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount7.") UNION ALL ";
					}
					if($sqlcount1 == '' && $sqlcount2 == '' && $sqlcount3 == '' && $sqlcount4 == '' && $sqlcount5 == '' && $sqlcount6 == '' && $sqlcount7 == '')
					{
						$json['Success'] = false;
						$json['Result'] = 'Please Select atleast one Service Workflow.';
						return json_encode($json);
					}
					$sqlfinalcount = substr($sqlfinalcount, 0, -11);
					$sqlfinalcount = $sqlfinalcount.") as T";
					/*$json['Success'] = false;
					$json['Result'] = $sqlfinalcount;
					return json_encode($json);*/
					$queryfinalcount = mysqli_query($this->connectionlink,$sqlfinalcount);
					$rowfinalcount = mysqli_fetch_assoc($queryfinalcount);
				}
				else
				{
					$json['Success'] = false;
					$json['Result'] = 'Please Select atleast one Workflow.';
					return json_encode($json);
				}
			}
			if($data['Workflow'] != '')
			{
				for($i=0;$i<count($data['Workflow']);$i++) {
					if($data['Workflow'][$i] == 1)
					{
						$sql1 = "Select ";
						$sql1 = $sql1."distinct(A.AssetScanID) as `Scan ID`";
						if($data['assetsn'] == 'true')
						{
							$sql1 = $sql1.", A.SerialNumber as `SN`";
						}
						if($data['assetcreatedby'] == 'true')
						{
							$sql1 = $sql1.", U.FirstName,U.LastName";
						}
						if($data['assetworkflow'] == 'true')
						{
							$sql1 = $sql1.", 'Sanitization' as Workflow";
						}
						if($data['assetcustomid'] == 'true')
						{
							$sql1 = $sql1.", A.sanitization_custom_id as Custom_id";
						}
						if($data['assetNotesid'] == 'true')
						{
							$sql1 = $sql1.", A.sanitization_notes as `Notes`";
						}
						if($data['WorkflowInput'] == 'true')
						{
							$sql1 = $sql1.", WI.input as `Input`";
						}
						if($data['WorkflowInputType'] == 'true')
						{
							$sql1 = $sql1.", WI.input_type as `Input Type`";
						}
						if($data['transactiondate'] == 'true')
						{
							$sql1 = $sql1.", A.CreatedDate as `TransactionDate`";
						}
						if($data['assetbinid'] == 'true')
						{
							$sql1 = $sql1.", CP.BinName as `Bin ID`";
						}
						if($data['assetTicketid'] == 'true')
						{
							$sql1 = $sql1.", L.LoadId as `Inbound Ticket ID`";
						}
						if($data['assetcid'] == 'true')
						{
							$sql1 = $sql1.", P.idPallet as `Inbound Container ID`";
						}
						if($data['assetsourcecus'] == 'true')
						{
							$sql1 = $sql1.", SC.CustomerName as `Source`";
						}
						if($data['assetmpn'] == 'true')
						{
							$sql1 = $sql1.", A.UniversalModelNumber as `MPN`";
						}
						if($data['assetdisposition'] == 'true')
						{
							$sql1 = $sql1.", DI.disposition as `Disposition`";
						}
						if($data['assetfacility'] == 'true')
						{
							$sql1 = $sql1.", F.FacilityName as `Facility`";
						}
						if($data['assetcreateddate'] == 'true')
						{
							$sql1 = $sql1.", AA.DateCreated";
						}
						if($data['assetsourcetype'] == 'true')
						{
							$sql1 = $sql1.", SCT.Cumstomertype as `Source Type`";
						}
						if($data['assetouboundcontainer'] == 'true')
						{
							$sql1 = $sql1.", SCS.ShippingContainerID as `Outbound Container ID`";
						}
						if($data['assetouboundticket'] == 'true')
						{
							$sql1 = $sql1.", SCO.ShippingID as `Outbound Ticket ID`";
						}
						if($data['assetparttype'] == 'true')
						{
							$sql1 = $sql1.", AA.part_type as `Part Type`";
						}
						if($data['assetlastdate'] == 'true')
						{
							$sql1 = $sql1.", AA.ModifiedDate as `Last Touch Date`";
						}
						if($data['assetouboundRemovalType'] == 'true')
						{
							$sql1 = $sql1.", SHDI.disposition as `Outbound Removal Type`";
						}
						if($data['assetouboundDestination'] == 'true')
						{
							$sql1 = $sql1.", SHV.VendorName as `Outbound Destination`";
						}
						if($data['assetouboundFacility'] == 'true')
						{
							$sql1 = $sql1.", SHF.FacilityName as `Outbound Facility`";
						}
						if($data['assetouboundSource'] == 'true')
						{
							$sql1 = $sql1.", SC.CustomerName as `Outbound Source`";
						}
						if($data['assetouboundSourceType'] == 'true')
						{
							$sql1 = $sql1.", SCT.Cumstomertype as `Outbound Source Type`";
						}
						if($data['assetouboundMPN'] == 'true')
						{
							$sql1 = $sql1.", SCS.UniversalModelNumber as `Outbound MPN`";
						}
						if($data['assetouboundContainerType'] == 'true')
						{
							$sql1 = $sql1.", SHP.packageName as `Outbound Container Type`";
						}
						if($data['assetouboundSanitizationVerID'] == 'true')
						{
							$sql1 = $sql1.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
						}
						$sql1 = $sql1." From asset_sanitization A
									LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
									LEFT JOIN users U ON U.UserId = A.CreatedBy
									LEFT JOIN pallets P ON P.idPallet = AA.idPallet
									LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
									LEFT JOIN loads L ON L.LoadId = P.LoadId
									LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
									LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
									LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
									LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
									LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = AA.AssetScanID
									LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
									LEFT JOIN workflow_input WI ON WI.input_id = A.sanitization_input_id
									LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
									LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
									LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
									LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
									LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
									LEFT JOIN business_rule BR ON BR.rule_id = A.sanitization_rule_id
									LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
						$sql1 = $sql1." where
						AA.AccountID = '".$_SESSION['user']['AccountID']."'";
						if($data['LoadID'] != '')
							$sql1 = $sql1." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
						if($data['idCustomer'] != '')
						{
							$idCustomer = '';
							for($ci=0;$ci<count($data['idCustomer']);$ci++) {
								$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
							}
							$idCustomer = rtrim($idCustomer, ",");
							$sql1 = $sql1." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
						}
						if($data['CRDatefrom'] != '')
							$sql1 = $sql1." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
						if($data['FacilityID'] != '')
						{
							$FacilityID = '';
							for($fi=0;$fi<count($data['FacilityID']);$fi++) {
								$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
							}
							$FacilityID = rtrim($FacilityID, ",");
							$sql1 = $sql1." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
						}
						if($data['ContainerID'] != '')
							$sql1 = $sql1." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
						if($data['ACDatefrom'] != '')
							$sql1 = $sql1." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
						if($data['SerialNumber'] != '')
							$sql1 = $sql1." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
						if($data['UniversalModelNumber'] != '')
							$sql1 = $sql1." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
						if($data['DispositionID'] != '')
						{
							$dispositionID = '';
							for($di=0;$di<count($data['DispositionID']);$di++) {
								$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
							}
							$dispositionID = rtrim($dispositionID, ",");
							$sql1 = $sql1." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
						}
						if($data['CustomerType'] != '')
						{
							$CustomerType = '';
							for($ti=0;$ti<count($data['CustomerType']);$ti++) {
								$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
							}
							$CustomerType = rtrim($CustomerType, ",");
							$sql1 = $sql1." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
						}
						if($data['binid'] != '')
							$sql1 = $sql1." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
						if($data['Parttype'] != '')
						{
							$Parttype = '';
							for($pi=0;$pi<count($data['Parttype']);$pi++) {
								$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
							}
							$Parttype = rtrim($Parttype, ",");
							//$Parttype = str_replace($Parttype, '\'');
							//$Parttype = str_replace('\'',"'",$Parttype);
							$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
							$sql1 = $sql1." AND AA.part_type IN ( ".$Parttype.")";
						}
						if($data['Input'] != '')
							$sql1 = $sql1." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
						if($data['InputType'] != '')
							$sql1 = $sql1." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
						/*if($data['Workflow'] != '')
						{
							$workflow_id = '';
							for($i=0;$i<count($data['Workflow']);$i++) {
								if($data['Workflow'][$i] == 6)
								{
									$data['Workflow'][$i] = 1;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
								else if($data['Workflow'][$i] == 7)
								{
									$data['Workflow'][$i] = 6;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
							}
							$workflow_id = rtrim($workflow_id, ",");
							if($workflow_id != '')
							{
								$sql1 = $sql1." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}
						}*/
					}
					else if($data['Workflow'][$i] == 2)
					{
						$sql2 = "Select ";
						$sql2 = $sql2."distinct(A.AssetScanID) as `Scan ID`";
						if($data['assetsn'] == 'true')
						{
							$sql2 = $sql2.", A.SerialNumber as `SN`";
						}
						if($data['assetcreatedby'] == 'true')
						{
							$sql2 = $sql2.", U.FirstName,U.LastName";
						}
						if($data['assetworkflow'] == 'true')
						{
							$sql2 = $sql2.", 'Failure Analysis' as Workflow";
						}
						if($data['assetcustomid'] == 'true')
						{
							$sql2 = $sql2.", A.fa_custom_id as Custom_id";
						}
						if($data['assetNotesid'] == 'true')
						{
							$sql2 = $sql2.", A.fa_notes as `Notes`";
						}
						if($data['WorkflowInput'] == 'true')
						{
							$sql2 = $sql2.", WI.input as `Input`";
						}
						if($data['WorkflowInputType'] == 'true')
						{
							$sql2 = $sql2.", WI.input_type as `Input Type`";
						}
						if($data['transactiondate'] == 'true')
						{
							$sql2 = $sql2.", A.CreatedDate as `TransactionDate`";
						}
						if($data['assetbinid'] == 'true')
						{
							$sql2 = $sql2.", CP.BinName as `Bin ID`";
						}
						if($data['assetTicketid'] == 'true')
						{
							$sql2 = $sql2.", L.LoadId as `Inbound Ticket ID`";
						}
						if($data['assetcid'] == 'true')
						{
							$sql2 = $sql2.", P.idPallet as `Inbound Container ID`";
						}
						if($data['assetsourcecus'] == 'true')
						{
							$sql2 = $sql2.", SC.CustomerName as `Source`";
						}
						if($data['assetmpn'] == 'true')
						{
							$sql2 = $sql2.", A.UniversalModelNumber as `MPN`";
						}
						if($data['assetdisposition'] == 'true')
						{
							$sql2 = $sql2.", DI.disposition as `Disposition`";
						}
						if($data['assetfacility'] == 'true')
						{
							$sql2 = $sql2.", F.FacilityName as `Facility`";
						}
						if($data['assetcreateddate'] == 'true')
						{
							$sql2 = $sql2.", AA.DateCreated";
						}
						if($data['assetsourcetype'] == 'true')
						{
							$sql2 = $sql2.", SCT.Cumstomertype as `Source Type`";
						}
						if($data['assetouboundcontainer'] == 'true')
						{
							$sql2 = $sql2.", SCS.ShippingContainerID as `Outbound Container ID`";
						}
						if($data['assetouboundticket'] == 'true')
						{
							$sql2 = $sql2.", SCO.ShippingID as `Outbound Ticket ID`";
						}
						if($data['assetparttype'] == 'true')
						{
							$sql2 = $sql2.", AA.part_type as `Part Type`";
						}
						if($data['assetlastdate'] == 'true')
						{
							$sql2 = $sql2.", AA.ModifiedDate as `Last Touch Date`";
						}
						if($data['assetouboundRemovalType'] == 'true')
						{
							$sql2 = $sql2.", SHDI.disposition as `Outbound Removal Type`";
						}
						if($data['assetouboundDestination'] == 'true')
						{
							$sql2 = $sql2.", SHV.VendorName as `Outbound Destination`";
						}
						if($data['assetouboundFacility'] == 'true')
						{
							$sql2 = $sql2.", SHF.FacilityName as `Outbound Facility`";
						}
						if($data['assetouboundSource'] == 'true')
						{
							$sql2 = $sql2.", SC.CustomerName as `Outbound Source`";
						}
						if($data['assetouboundSourceType'] == 'true')
						{
							$sql2 = $sql2.", SCT.Cumstomertype as `Outbound Source Type`";
						}
						if($data['assetouboundMPN'] == 'true')
						{
							$sql2 = $sql2.", SCS.UniversalModelNumber as `Outbound MPN`";
						}
						if($data['assetouboundContainerType'] == 'true')
						{
							$sql2 = $sql2.", SHP.packageName as `Outbound Container Type`";
						}
						if($data['assetouboundSanitizationVerID'] == 'true')
						{
							$sql2 = $sql2.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
						}
						$sql2 = $sql2." From asset_failure_analysis A
									LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
									LEFT JOIN users U ON U.UserId = A.CreatedBy
									LEFT JOIN pallets P ON P.idPallet = AA.idPallet
									LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
									LEFT JOIN loads L ON L.LoadId = P.LoadId
									LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
									LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
									LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
									LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
									LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
									LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
									LEFT JOIN workflow_input WI ON WI.input_id = A.fa_input_id
									LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
									LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
									LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
									LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
									LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
									LEFT JOIN business_rule BR ON BR.rule_id = A.fa_rule_id
									LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
						$sql2 = $sql2." where
						AA.AccountID = '".$_SESSION['user']['AccountID']."'";
						if($data['LoadID'] != '')
							$sql2 = $sql2." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
						if($data['idCustomer'] != '')
						{
							$idCustomer = '';
							for($ci=0;$ci<count($data['idCustomer']);$ci++) {
								$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
							}
							$idCustomer = rtrim($idCustomer, ",");
							$sql2 = $sql2." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
						}
						if($data['CRDatefrom'] != '')
							$sql2 = $sql2." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
						if($data['FacilityID'] != '')
						{
							$FacilityID = '';
							for($fi=0;$fi<count($data['FacilityID']);$fi++) {
								$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
							}
							$FacilityID = rtrim($FacilityID, ",");
							$sql2 = $sql2." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
						}
						if($data['ContainerID'] != '')
							$sql2 = $sql2." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
						if($data['ACDatefrom'] != '')
							$sql2 = $sql2." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
						if($data['SerialNumber'] != '')
							$sql2 = $sql2." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
						if($data['UniversalModelNumber'] != '')
							$sql2 = $sql2." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
						if($data['DispositionID'] != '')
						{
							$dispositionID = '';
							for($di=0;$di<count($data['DispositionID']);$di++) {
								$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
							}
							$dispositionID = rtrim($dispositionID, ",");
							$sql2 = $sql2." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
						}
						if($data['CustomerType'] != '')
						{
							$CustomerType = '';
							for($ti=0;$ti<count($data['CustomerType']);$ti++) {
								$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
							}
							$CustomerType = rtrim($CustomerType, ",");
							$sql2 = $sql2." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
						}
						if($data['binid'] != '')
							$sql2 = $sql2." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
						if($data['Parttype'] != '')
						{
							$Parttype = '';
							for($pi=0;$pi<count($data['Parttype']);$pi++) {
								$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
							}
							$Parttype = rtrim($Parttype, ",");
							//$Parttype = str_replace($Parttype, '\'');
							//$Parttype = str_replace('\'',"'",$Parttype);
							$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
							$sql2 = $sql2." AND AA.part_type IN ( ".$Parttype.")";
						}
						if($data['Input'] != '')
							$sql2 = $sql2." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
						if($data['InputType'] != '')
							$sql2 = $sql2." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
						/*if($data['Workflow'] != '')
						{
							$workflow_id = '';
							for($i=0;$i<count($data['Workflow']);$i++) {
								if($data['Workflow'][$i] == 6)
								{
									$data['Workflow'][$i] = 1;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
								else if($data['Workflow'][$i] == 7)
								{
									$data['Workflow'][$i] = 6;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
							}
							$workflow_id = rtrim($workflow_id, ",");
							if($workflow_id != '')
							{
								$sql2 = $sql2." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}
						}*/
					}
					else if($data['Workflow'][$i] == 3)
					{
						$sql3 = "Select ";
						$sql3 = $sql3."distinct(A.AssetScanID) as `Scan ID`";
						if($data['assetsn'] == 'true')
						{
							$sql3 = $sql3.", A.SerialNumber as `SN`";
						}
						if($data['assetcreatedby'] == 'true')
						{
							$sql3 = $sql3.", U.FirstName,U.LastName";
						}
						if($data['assetworkflow'] == 'true')
						{
							$sql3 = $sql3.", 'RMA Investigation' as Workflow";
						}
						if($data['assetcustomid'] == 'true')
						{
							$sql3 = $sql3.", A.rma_custom_id as Custom_id";
						}
						if($data['assetNotesid'] == 'true')
						{
							$sql3 = $sql3.", A.rma_notes as `Notes`";
						}
						if($data['WorkflowInput'] == 'true')
						{
							$sql3 = $sql3.", WI.input as `Input`";
						}
						if($data['WorkflowInputType'] == 'true')
						{
							$sql3 = $sql3.", WI.input_type as `Input Type`";
						}
						if($data['transactiondate'] == 'true')
						{
							$sql3 = $sql3.", A.CreatedDate as `TransactionDate`";
						}
						if($data['assetbinid'] == 'true')
						{
							$sql3 = $sql3.", CP.BinName as `Bin ID`";
						}
						if($data['assetTicketid'] == 'true')
						{
							$sql3 = $sql3.", L.LoadId as `Inbound Ticket ID`";
						}
						if($data['assetcid'] == 'true')
						{
							$sql3 = $sql3.", P.idPallet as `Inbound Container ID`";
						}
						if($data['assetsourcecus'] == 'true')
						{
							$sql3 = $sql3.", SC.CustomerName as `Source`";
						}
						if($data['assetmpn'] == 'true')
						{
							$sql3 = $sql3.", A.UniversalModelNumber as `MPN`";
						}
						if($data['assetdisposition'] == 'true')
						{
							$sql3 = $sql3.", DI.disposition as `Disposition`";
						}
						if($data['assetfacility'] == 'true')
						{
							$sql3 = $sql3.", F.FacilityName as `Facility`";
						}
						if($data['assetcreateddate'] == 'true')
						{
							$sql3 = $sql3.", AA.DateCreated";
						}
						if($data['assetsourcetype'] == 'true')
						{
							$sql3 = $sql3.", SCT.Cumstomertype as `Source Type`";
						}
						if($data['assetouboundcontainer'] == 'true')
						{
							$sql3 = $sql3.", SCS.ShippingContainerID as `Outbound Container ID`";
						}
						if($data['assetouboundticket'] == 'true')
						{
							$sql3 = $sql3.", SCO.ShippingID as `Outbound Ticket ID`";
						}
						if($data['assetparttype'] == 'true')
						{
							$sql3 = $sql3.", AA.part_type as `Part Type`";
						}
						if($data['assetlastdate'] == 'true')
						{
							$sql3 = $sql3.", AA.DateUpdated as `Last Touch Date`";
						}
						if($data['assetouboundRemovalType'] == 'true')
						{
							$sql3 = $sql3.", SHDI.disposition as `Outbound Removal Type`";
						}
						if($data['assetouboundDestination'] == 'true')
						{
							$sql3 = $sql3.", SHV.VendorName as `Outbound Destination`";
						}
						if($data['assetouboundFacility'] == 'true')
						{
							$sql3 = $sql3.", SHF.FacilityName as `Outbound Facility`";
						}
						if($data['assetouboundSource'] == 'true')
						{
							$sql3 = $sql3.", SC.CustomerName as `Outbound Source`";
						}
						if($data['assetouboundSourceType'] == 'true')
						{
							$sql3 = $sql3.", SCT.Cumstomertype as `Outbound Source Type`";
						}
						if($data['assetouboundMPN'] == 'true')
						{
							$sql3 = $sql3.", SCS.UniversalModelNumber as `Outbound MPN`";
						}
						if($data['assetouboundContainerType'] == 'true')
						{
							$sql3 = $sql3.", SHP.packageName as `Outbound Container Type`";
						}
						if($data['assetouboundSanitizationVerID'] == 'true')
						{
							$sql3 = $sql3.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
						}
						$sql3 = $sql3." From asset_rma_investigation A
									LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
									LEFT JOIN users U ON U.UserId = A.CreatedBy
									LEFT JOIN pallets P ON P.idPallet = AA.idPallet
									LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
									LEFT JOIN loads L ON L.LoadId = P.LoadId
									LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
									LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
									LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
									LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
									LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
									LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
									LEFT JOIN workflow_input WI ON WI.input_id = A.rma_input_id
									LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
									LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
									LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
									LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
									LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
									LEFT JOIN business_rule BR ON BR.rule_id = A.rma_rule_id
									LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
						$sql3 = $sql3." where
						AA.AccountID = '".$_SESSION['user']['AccountID']."'";
						if($data['LoadID'] != '')
							$sql3 = $sql3." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
						if($data['idCustomer'] != '')
						{
							$idCustomer = '';
							for($ci=0;$ci<count($data['idCustomer']);$ci++) {
								$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
							}
							$idCustomer = rtrim($idCustomer, ",");
							$sql3 = $sql3." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
						}
						if($data['CRDatefrom'] != '')
							$sql3 = $sql3." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
						if($data['FacilityID'] != '')
						{
							$FacilityID = '';
							for($fi=0;$fi<count($data['FacilityID']);$fi++) {
								$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
							}
							$FacilityID = rtrim($FacilityID, ",");
							$sql3 = $sql3." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
						}
						if($data['ContainerID'] != '')
							$sql3 = $sql3." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
						if($data['ACDatefrom'] != '')
							$sql3 = $sql3." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
						if($data['SerialNumber'] != '')
							$sql3 = $sql3." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
						if($data['UniversalModelNumber'] != '')
							$sql3 = $sql3." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
						if($data['DispositionID'] != '')
						{
							$dispositionID = '';
							for($di=0;$di<count($data['DispositionID']);$di++) {
								$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
							}
							$dispositionID = rtrim($dispositionID, ",");
							$sql3 = $sql3." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
						}
						if($data['CustomerType'] != '')
						{
							$CustomerType = '';
							for($ti=0;$ti<count($data['CustomerType']);$ti++) {
								$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
							}
							$CustomerType = rtrim($CustomerType, ",");
							$sql3 = $sql3." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
						}
						if($data['binid'] != '')
							$sql3 = $sql3." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
						if($data['Parttype'] != '')
						{
							$Parttype = '';
							for($pi=0;$pi<count($data['Parttype']);$pi++) {
								$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
							}
							$Parttype = rtrim($Parttype, ",");
							//$Parttype = str_replace($Parttype, '\'');
							//$Parttype = str_replace('\'',"'",$Parttype);
							$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
							$sql3 = $sql3." AND AA.part_type IN ( ".$Parttype.")";
						}
						if($data['Input'] != '')
							$sql3 = $sql3." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
						if($data['InputType'] != '')
							$sql3 = $sql3." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
						/*if($data['Workflow'] != '')
						{
							$workflow_id = '';
							for($i=0;$i<count($data['Workflow']);$i++) {
								if($data['Workflow'][$i] == 6)
								{
									$data['Workflow'][$i] = 1;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
								else if($data['Workflow'][$i] == 7)
								{
									$data['Workflow'][$i] = 6;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
							}
							$workflow_id = rtrim($workflow_id, ",");
							if($workflow_id != '')
							{
								$sql3 = $sql3." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}
						}*/
					}
					else if($data['Workflow'][$i] == 4)
					{
						$sql4 = "Select ";
						$sql4 = $sql4."distinct(A.AssetScanID) as `Scan ID`";
						if($data['assetsn'] == 'true')
						{
							$sql4 = $sql4.", A.SerialNumber as `SN`";
						}
						if($data['assetcreatedby'] == 'true')
						{
							$sql4 = $sql4.", U.FirstName,U.LastName";
						}
						if($data['assetworkflow'] == 'true')
						{
							$sql4 = $sql4.", 'Harvest' as Workflow";
						}
						if($data['assetcustomid'] == 'true')
						{
							$sql4 = $sql4.", A.harvest_custom_id as Custom_id";
						}
						if($data['assetNotesid'] == 'true')
						{
							$sql4 = $sql4.", A.harvest_notes as `Notes`";
						}
						if($data['WorkflowInput'] == 'true')
						{
							$sql4 = $sql4.", WI.input as `Input`";
						}
						if($data['WorkflowInputType'] == 'true')
						{
							$sql4 = $sql4.", WI.input_type as `Input Type`";
						}
						if($data['transactiondate'] == 'true')
						{
							$sql4 = $sql4.", A.CreatedDate as `TransactionDate`";
						}
						if($data['assetbinid'] == 'true')
						{
							$sql4 = $sql4.", CP.BinName as `Bin ID`";
						}
						if($data['assetTicketid'] == 'true')
						{
							$sql4 = $sql4.", L.LoadId as `Inbound Ticket ID`";
						}
						if($data['assetcid'] == 'true')
						{
							$sql4 = $sql4.", P.idPallet as `Inbound Container ID`";
						}
						if($data['assetsourcecus'] == 'true')
						{
							$sql4 = $sql4.", SC.CustomerName as `Source`";
						}
						if($data['assetmpn'] == 'true')
						{
							$sql4 = $sql4.", A.UniversalModelNumber as `MPN`";
						}
						if($data['assetdisposition'] == 'true')
						{
							$sql4 = $sql4.", DI.disposition as `Disposition`";
						}
						if($data['assetfacility'] == 'true')
						{
							$sql4 = $sql4.", F.FacilityName as `Facility`";
						}
						if($data['assetcreateddate'] == 'true')
						{
							$sql4 = $sql4.", AA.DateCreated";
						}
						if($data['assetsourcetype'] == 'true')
						{
							$sql4 = $sql4.", SCT.Cumstomertype as `Source Type`";
						}
						if($data['assetouboundcontainer'] == 'true')
						{
							$sql4 = $sql4.", SCS.ShippingContainerID as `Outbound Container ID`";
						}
						if($data['assetouboundticket'] == 'true')
						{
							$sql4 = $sql4.", SCO.ShippingID as `Outbound Ticket ID`";
						}
						if($data['assetparttype'] == 'true')
						{
							$sql4 = $sql4.", AA.part_type as `Part Type`";
						}
						if($data['assetlastdate'] == 'true')
						{
							$sql4 = $sql4.", AA.ModifiedDate as `Last Touch Date`";
						}
						if($data['assetouboundRemovalType'] == 'true')
						{
							$sql4 = $sql4.", SHDI.disposition as `Outbound Removal Type`";
						}
						if($data['assetouboundDestination'] == 'true')
						{
							$sql4 = $sql4.", SHV.VendorName as `Outbound Destination`";
						}
						if($data['assetouboundFacility'] == 'true')
						{
							$sql4 = $sql4.", SHF.FacilityName as `Outbound Facility`";
						}
						if($data['assetouboundSource'] == 'true')
						{
							$sql4 = $sql4.", SC.CustomerName as `Outbound Source`";
						}
						if($data['assetouboundSourceType'] == 'true')
						{
							$sql4 = $sql4.", SCT.Cumstomertype as `Outbound Source Type`";
						}
						if($data['assetouboundMPN'] == 'true')
						{
							$sql4 = $sql4.", SCS.UniversalModelNumber as `Outbound MPN`";
						}
						if($data['assetouboundContainerType'] == 'true')
						{
							$sql4 = $sql4.", SHP.packageName as `Outbound Container Type`";
						}
						if($data['assetouboundSanitizationVerID'] == 'true')
						{
							$sql4 = $sql4.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
						}
						$sql4 = $sql4." From asset_harvest A
									LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
									LEFT JOIN users U ON U.UserId = A.CreatedBy
									LEFT JOIN pallets P ON P.idPallet = AA.idPallet
									LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
									LEFT JOIN loads L ON L.LoadId = P.LoadId
									LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
									LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
									LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
									LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
									LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
									LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
									LEFT JOIN workflow_input WI ON WI.input_id = A.harvest_input_id
									LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
									LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
									LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
									LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
									LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
									LEFT JOIN business_rule BR ON BR.rule_id = A.harvest_rule_id
									LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
						$sql4 = $sql4." where
						AA.AccountID = '".$_SESSION['user']['AccountID']."'";
						if($data['LoadID'] != '')
							$sql4 = $sql4." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
						if($data['idCustomer'] != '')
						{
							$idCustomer = '';
							for($ci=0;$ci<count($data['idCustomer']);$ci++) {
								$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
							}
							$idCustomer = rtrim($idCustomer, ",");
							$sql4 = $sql4." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
						}
						if($data['CRDatefrom'] != '')
							$sql4 = $sql4." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
						if($data['FacilityID'] != '')
						{
							$FacilityID = '';
							for($fi=0;$fi<count($data['FacilityID']);$fi++) {
								$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
							}
							$FacilityID = rtrim($FacilityID, ",");
							$sql4 = $sql4." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
						}
						if($data['ContainerID'] != '')
							$sql4 = $sql4." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
						if($data['ACDatefrom'] != '')
							$sql4 = $sql4." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
						if($data['SerialNumber'] != '')
							$sql4 = $sql4." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
						if($data['UniversalModelNumber'] != '')
							$sql4 = $sql4." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
						if($data['DispositionID'] != '')
						{
							$dispositionID = '';
							for($di=0;$di<count($data['DispositionID']);$di++) {
								$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
							}
							$dispositionID = rtrim($dispositionID, ",");
							$sql4 = $sql4." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
						}
						if($data['CustomerType'] != '')
						{
							$CustomerType = '';
							for($ti=0;$ti<count($data['CustomerType']);$ti++) {
								$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
							}
							$CustomerType = rtrim($CustomerType, ",");
							$sql4 = $sql4." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
						}
						if($data['binid'] != '')
							$sql4 = $sql4." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
						if($data['Parttype'] != '')
						{
							$Parttype = '';
							for($pi=0;$pi<count($data['Parttype']);$pi++) {
								$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
							}
							$Parttype = rtrim($Parttype, ",");
							//$Parttype = str_replace($Parttype, '\'');
							//$Parttype = str_replace('\'',"'",$Parttype);
							$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
							$sql4 = $sql4." AND AA.part_type IN ( ".$Parttype.")";
						}
						if($data['Input'] != '')
							$sql4 = $sql4." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
						if($data['InputType'] != '')
							$sql4 = $sql4." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
						/*if($data['Workflow'] != '')
						{
							$workflow_id = '';
							for($i=0;$i<count($data['Workflow']);$i++) {
								if($data['Workflow'][$i] == 6)
								{
									$data['Workflow'][$i] = 1;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
								else if($data['Workflow'][$i] == 7)
								{
									$data['Workflow'][$i] = 6;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
							}
							$workflow_id = rtrim($workflow_id, ",");
							if($workflow_id != '')
							{
								$sql4 = $sql4." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}
						}*/
					}
					else if($data['Workflow'][$i] == 5)
					{
						$sql5 = "Select ";
						$sql5 = $sql5."distinct(A.AssetScanID) as `Scan ID`";
						if($data['assetsn'] == 'true')
						{
							$sql5 = $sql5.", A.SerialNumber as `SN`";
						}
						if($data['assetcreatedby'] == 'true')
						{
							$sql5 = $sql5.", U.FirstName,U.LastName";
						}
						if($data['assetworkflow'] == 'true')
						{
							$sql5 = $sql5.", 'Repair' as Workflow";
						}
						if($data['assetcustomid'] == 'true')
						{
							$sql5 = $sql5.", A.repair_custom_id as Custom_id";
						}
						if($data['assetNotesid'] == 'true')
						{
							$sql5 = $sql5.", A.repair_notes as `Notes`";
						}
						if($data['WorkflowInput'] == 'true')
						{
							$sql5 = $sql5.", WI.input as `Input`";
						}
						if($data['WorkflowInputType'] == 'true')
						{
							$sql5 = $sql5.", WI.input_type as `Input Type`";
						}
						if($data['transactiondate'] == 'true')
						{
							$sql5 = $sql5.", A.CreatedDate as `TransactionDate`";
						}
						if($data['assetbinid'] == 'true')
						{
							$sql5 = $sql5.", CP.BinName as `Bin ID`";
						}
						if($data['assetTicketid'] == 'true')
						{
							$sql5 = $sql5.", L.LoadId as `Inbound Ticket ID`";
						}
						if($data['assetcid'] == 'true')
						{
							$sql5 = $sql5.", P.idPallet as `Inbound Container ID`";
						}
						if($data['assetsourcecus'] == 'true')
						{
							$sql5 = $sql5.", SC.CustomerName as `Source`";
						}
						if($data['assetmpn'] == 'true')
						{
							$sql5 = $sql5.", A.UniversalModelNumber as `MPN`";
						}
						if($data['assetdisposition'] == 'true')
						{
							$sql5 = $sql5.", DI.disposition as `Disposition`";
						}
						if($data['assetfacility'] == 'true')
						{
							$sql5 = $sql5.", F.FacilityName as `Facility`";
						}
						if($data['assetcreateddate'] == 'true')
						{
							$sql5 = $sql5.", AA.DateCreated";
						}
						if($data['assetsourcetype'] == 'true')
						{
							$sql5 = $sql5.", SCT.Cumstomertype as `Source Type`";
						}
						if($data['assetouboundcontainer'] == 'true')
						{
							$sql5 = $sql5.", SCS.ShippingContainerID as `Outbound Container ID`";
						}
						if($data['assetouboundticket'] == 'true')
						{
							$sql5 = $sql5.", SCO.ShippingID as `Outbound Ticket ID`";
						}
						if($data['assetparttype'] == 'true')
						{
							$sql5 = $sql5.", AA.part_type as `Part Type`";
						}
						if($data['assetlastdate'] == 'true')
						{
							$sql5 = $sql5.", AA.ModifiedDate as `Last Touch Date`";
						}
						if($data['assetouboundRemovalType'] == 'true')
						{
							$sql5 = $sql5.", SHDI.disposition as `Outbound Removal Type`";
						}
						if($data['assetouboundDestination'] == 'true')
						{
							$sql5 = $sql5.", SHV.VendorName as `Outbound Destination`";
						}
						if($data['assetouboundFacility'] == 'true')
						{
							$sql5 = $sql5.", SHF.FacilityName as `Outbound Facility`";
						}
						if($data['assetouboundSource'] == 'true')
						{
							$sql5 = $sql5.", SC.CustomerName as `Outbound Source`";
						}
						if($data['assetouboundSourceType'] == 'true')
						{
							$sql5 = $sql5.", SCT.Cumstomertype as `Outbound Source Type`";
						}
						if($data['assetouboundMPN'] == 'true')
						{
							$sql5 = $sql5.", SCS.UniversalModelNumber as `Outbound MPN`";
						}
						if($data['assetouboundContainerType'] == 'true')
						{
							$sql5 = $sql5.", SHP.packageName as `Outbound Container Type`";
						}
						if($data['assetouboundSanitizationVerID'] == 'true')
						{
							$sql5 = $sql5.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
						}
						$sql5 = $sql5." From asset_repair A
									LEFT JOIN asset AA ON AA.SerialNumber = A.SerialNumber
									LEFT JOIN users U ON U.UserId = A.CreatedBy
									LEFT JOIN pallets P ON P.idPallet = AA.idPallet
									LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
									LEFT JOIN loads L ON L.LoadId = P.LoadId
									LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
									LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
									LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
									LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
									LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
									LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
									LEFT JOIN workflow_input WI ON WI.input_id = A.repair_input_id
									LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
									LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
									LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
									LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
									LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
									LEFT JOIN business_rule BR ON BR.rule_id = A.repair_rule_id
									LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
						$sql5 = $sql5." where
						AA.AccountID = '".$_SESSION['user']['AccountID']."'";
						if($data['LoadID'] != '')
							$sql5 = $sql5." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
						if($data['idCustomer'] != '')
						{
							$idCustomer = '';
							for($ci=0;$ci<count($data['idCustomer']);$ci++) {
								$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
							}
							$idCustomer = rtrim($idCustomer, ",");
							$sql5 = $sql5." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
						}
						if($data['CRDatefrom'] != '')
							$sql5 = $sql5." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
						if($data['FacilityID'] != '')
						{
							$FacilityID = '';
							for($fi=0;$fi<count($data['FacilityID']);$fi++) {
								$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
							}
							$FacilityID = rtrim($FacilityID, ",");
							$sql5 = $sql5." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
						}
						if($data['ContainerID'] != '')
							$sql5 = $sql5." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
						if($data['ACDatefrom'] != '')
							$sql5 = $sql5." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
						if($data['SerialNumber'] != '')
							$sql5 = $sql5." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
						if($data['UniversalModelNumber'] != '')
							$sql5 = $sql5." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
						if($data['DispositionID'] != '')
						{
							$dispositionID = '';
							for($di=0;$di<count($data['DispositionID']);$di++) {
								$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
							}
							$dispositionID = rtrim($dispositionID, ",");
							$sql5 = $sql5." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
						}
						if($data['CustomerType'] != '')
						{
							$CustomerType = '';
							for($ti=0;$ti<count($data['CustomerType']);$ti++) {
								$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
							}
							$CustomerType = rtrim($CustomerType, ",");
							$sql5 = $sql5." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
						}
						if($data['binid'] != '')
							$sql5 = $sql5." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
						if($data['Parttype'] != '')
						{
							$Parttype = '';
							for($pi=0;$pi<count($data['Parttype']);$pi++) {
								$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
							}
							$Parttype = rtrim($Parttype, ",");
							//$Parttype = str_replace($Parttype, '\'');
							//$Parttype = str_replace('\'',"'",$Parttype);
							$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
							$sql5 = $sql5." AND AA.part_type IN ( ".$Parttype.")";
						}
						if($data['Input'] != '')
							$sql5 = $sql5." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
						if($data['InputType'] != '')
							$sql5 = $sql5." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
						/*if($data['Workflow'] != '')
						{
							$workflow_id = '';
							for($i=0;$i<count($data['Workflow']);$i++) {
								if($data['Workflow'][$i] == 6)
								{
									$data['Workflow'][$i] = 1;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
								else if($data['Workflow'][$i] == 7)
								{
									$data['Workflow'][$i] = 6;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
							}
							$workflow_id = rtrim($workflow_id, ",");
							if($workflow_id != '')
							{
								$sql5 = $sql5." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}
						}*/
					}
					else if($data['Workflow'][$i] == 6)
					{
						$sql6 = "Select ";
						$sql6 = $sql6."distinct(A.AssetScanID) as `Scan ID`";
						if($data['assetsn'] == 'true')
						{
							$sql6 = $sql6.", A.SerialNumber as `SN`";
						}
						if($data['assetcreatedby'] == 'true')
						{
							$sql6 = $sql6.", U.FirstName,U.LastName";
						}
						if($data['assetworkflow'] == 'true')
						{
							$sql6 = $sql6.", 'Receive' as Workflow";
						}
						if($data['assetcustomid'] == 'true')
						{
							$sql6 = $sql6.", A.custom_id as Custom_id";
						}
						if($data['assetNotesid'] == 'true')
						{
							$sql6 = $sql6.", A.receive_notes as `Notes`";
						}
						if($data['WorkflowInput'] == 'true')
						{
							$sql6 = $sql6.", WI.input as `Input`";
						}
						if($data['WorkflowInputType'] == 'true')
						{
							$sql6 = $sql6.", WI.input_type as `Input Type`";
						}
						if($data['transactiondate'] == 'true')
						{
							$sql6 = $sql6.", A.DateCreated as `TransactionDate`";
						}
						if($data['assetbinid'] == 'true')
						{
							$sql6 = $sql6.", CP.BinName as `Bin ID`";
						}
						if($data['assetTicketid'] == 'true')
						{
							$sql6 = $sql6.", L.LoadId as `Inbound Ticket ID`";
						}
						if($data['assetcid'] == 'true')
						{
							$sql6 = $sql6.", P.idPallet as `Inbound Container ID`";
						}
						if($data['assetsourcecus'] == 'true')
						{
							$sql6 = $sql6.", SC.CustomerName as `Source`";
						}
						if($data['assetmpn'] == 'true')
						{
							$sql6 = $sql6.", A.UniversalModelNumber as `MPN`";
						}
						if($data['assetdisposition'] == 'true')
						{
							$sql6 = $sql6.", DI.disposition as `Disposition`";
						}
						if($data['assetfacility'] == 'true')
						{
							$sql6 = $sql6.", F.FacilityName as `Facility`";
						}
						if($data['assetcreateddate'] == 'true')
						{
							$sql6 = $sql6.", A.DateCreated";
						}
						if($data['assetsourcetype'] == 'true')
						{
							$sql6 = $sql6.", SCT.Cumstomertype as `Source Type`";
						}
						if($data['assetouboundcontainer'] == 'true')
						{
							$sql6 = $sql6.", SCS.ShippingContainerID as `Outbound Container ID`";
						}
						if($data['assetouboundticket'] == 'true')
						{
							$sql6 = $sql6.", SCO.ShippingID as `Outbound Ticket ID`";
						}
						if($data['assetparttype'] == 'true')
						{
							$sql6 = $sql6.", A.part_type as `Part Type`";
						}
						if($data['assetlastdate'] == 'true')
						{
							$sql6 = $sql6.", A.ModifiedDate as `Last Touch Date`";
						}
						if($data['assetouboundRemovalType'] == 'true')
						{
							$sql6 = $sql6.", SHDI.disposition as `Outbound Removal Type`";
						}
						if($data['assetouboundDestination'] == 'true')
						{
							$sql6 = $sql6.", SHV.VendorName as `Outbound Destination`";
						}
						if($data['assetouboundFacility'] == 'true')
						{
							$sql6 = $sql6.", SHF.FacilityName as `Outbound Facility`";
						}
						if($data['assetouboundSource'] == 'true')
						{
							$sql6 = $sql6.", SC.CustomerName as `Outbound Source`";
						}
						if($data['assetouboundSourceType'] == 'true')
						{
							$sql6 = $sql6.", SCT.Cumstomertype as `Outbound Source Type`";
						}
						if($data['assetouboundMPN'] == 'true')
						{
							$sql6 = $sql6.", SCS.UniversalModelNumber as `Outbound MPN`";
						}
						if($data['assetouboundContainerType'] == 'true')
						{
							$sql6 = $sql6.", SHP.packageName as `Outbound Container Type`";
						}
						if($data['assetouboundSanitizationVerID'] == 'true')
						{
							$sql6 = $sql6.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
						}
						$sql6 = $sql6." From asset A
									LEFT JOIN users U ON U.UserId = A.CreatedBy
									LEFT JOIN pallets P ON P.idPallet = A.idPallet
									LEFT JOIN facility F ON F.FacilityID = A.FacilityID
									LEFT JOIN loads L ON L.LoadId = P.LoadId
									LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
									LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
									LEFT JOIN custompallet CP ON CP.CustomPalletID = A.FirstReceivedCustomPalletID
									LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
									LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
									LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
									LEFT JOIN workflow_input WI ON WI.input_id = A.input_id
									LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
									LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
									LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
									LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
									LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
									LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
									LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
						$sql6 = $sql6." where
						A.AccountID = '".$_SESSION['user']['AccountID']."'";
						if($data['LoadID'] != '')
							$sql6 = $sql6." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
						if($data['idCustomer'] != '')
						{
							$idCustomer = '';
							for($ci=0;$ci<count($data['idCustomer']);$ci++) {
								$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
							}
							$idCustomer = rtrim($idCustomer, ",");
							$sql6 = $sql6." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
						}
						if($data['CRDatefrom'] != '')
							$sql6 = $sql6." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
						if($data['FacilityID'] != '')
						{
							$FacilityID = '';
							for($fi=0;$fi<count($data['FacilityID']);$fi++) {
								$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
							}
							$FacilityID = rtrim($FacilityID, ",");
							$sql6 = $sql6." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
						}
						if($data['ContainerID'] != '')
							$sql6 = $sql6." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
						if($data['ACDatefrom'] != '')
							$sql6 = $sql6." AND A.DateCreated between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
						if($data['SerialNumber'] != '')
							$sql6 = $sql6." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
						if($data['UniversalModelNumber'] != '')
							$sql6 = $sql6." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
						if($data['DispositionID'] != '')
						{
							$dispositionID = '';
							for($di=0;$di<count($data['DispositionID']);$di++) {
								$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
							}
							$dispositionID = rtrim($dispositionID, ",");
							$sql6 = $sql6." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
						}
						if($data['CustomerType'] != '')
						{
							$CustomerType = '';
							for($ti=0;$ti<count($data['CustomerType']);$ti++) {
								$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
							}
							$CustomerType = rtrim($CustomerType, ",");
							$sql6 = $sql6." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
						}
						if($data['binid'] != '')
							$sql6 = $sql6." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
						if($data['Parttype'] != '')
						{
							$Parttype = '';
							for($pi=0;$pi<count($data['Parttype']);$pi++) {
								$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
							}
							$Parttype = rtrim($Parttype, ",");
							//$Parttype = str_replace($Parttype, '\'');
							//$Parttype = str_replace('\'',"'",$Parttype);
							$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
							$sql6 = $sql6." AND A.part_type IN ( ".$Parttype.")";
						}
						if($data['Input'] != '')
							$sql6 = $sql6." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
						if($data['InputType'] != '')
							$sql6 = $sql6." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
						/*if($data['Workflow'] != '')
						{
							$workflow_id = '';
							for($i=0;$i<count($data['Workflow']);$i++) {
								if($data['Workflow'][$i] == 6)
								{
									$data['Workflow'][$i] = 1;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
								else if($data['Workflow'][$i] == 7)
								{
									$data['Workflow'][$i] = 6;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
							}
							$workflow_id = rtrim($workflow_id, ",");
							if($workflow_id != '')
							{
								$sql6 = $sql6." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}
						}*/
					}
					else if($data['Workflow'][$i] == 7)
					{
						$sql7 = "Select ";
						$sql7 = $sql7."distinct(A.AssetScanID) as `Scan ID`";
						if($data['assetsn'] == 'true')
						{
							$sql7 = $sql7.", A.SerialNumber as `SN`";
						}
						if($data['assetcreatedby'] == 'true')
						{
							$sql7 = $sql7.", U.FirstName,U.LastName";
						}
						if($data['assetworkflow'] == 'true')
						{
							$sql7 = $sql7.", 'Removal' as Workflow";
						}
						if($data['assetcustomid'] == 'true')
						{
							$sql7 = $sql7.", AA.custom_id as Custom_id";
						}
						if($data['assetNotesid'] == 'true')
						{
							$sql7 = $sql7.", A.Notes as `Notes`";
						}
						if($data['WorkflowInput'] == 'true')
						{
							$sql7 = $sql7.", WI.input as `Input`";
						}
						if($data['WorkflowInputType'] == 'true')
						{
							$sql7 = $sql7.", WI.input_type as `Input Type`";
						}
						if($data['transactiondate'] == 'true')
						{
							$sql7 = $sql7.", A.CreatedDate as `TransactionDate`";
						}
						if($data['assetbinid'] == 'true')
						{
							$sql7 = $sql7.", CP.BinName as `Bin ID`";
						}
						if($data['assetTicketid'] == 'true')
						{
							$sql7 = $sql7.", L.LoadId as `Inbound Ticket ID`";
						}
						if($data['assetcid'] == 'true')
						{
							$sql7 = $sql7.", P.idPallet as `Inbound Container ID`";
						}
						if($data['assetsourcecus'] == 'true')
						{
							$sql7 = $sql7.", SC.CustomerName as `Source`";
						}
						if($data['assetmpn'] == 'true')
						{
							$sql7 = $sql7.", A.UniversalModelNumber as `MPN`";
						}
						if($data['assetdisposition'] == 'true')
						{
							$sql7 = $sql7.", DI.disposition as `Disposition`";
						}
						if($data['assetfacility'] == 'true')
						{
							$sql7 = $sql7.", F.FacilityName as `Facility`";
						}
						if($data['assetcreateddate'] == 'true')
						{
							$sql7 = $sql7.", AA.DateCreated";
						}
						if($data['assetsourcetype'] == 'true')
						{
							$sql7 = $sql7.", SCT.Cumstomertype as `Source Type`";
						}
						if($data['assetouboundcontainer'] == 'true')
						{
							$sql7 = $sql7.", SCS.ShippingContainerID as `Outbound Container ID`";
						}
						if($data['assetouboundticket'] == 'true')
						{
							$sql7 = $sql7.", SCO.ShippingID as `Outbound Ticket ID`";
						}
						if($data['assetparttype'] == 'true')
						{
							$sql7 = $sql7.", AA.part_type as `Part Type`";
						}
						if($data['assetlastdate'] == 'true')
						{
							$sql7 = $sql7.", AA.ModifiedDate as `Last Touch Date`";
						}
						if($data['assetouboundRemovalType'] == 'true')
						{
							$sql7 = $sql7.", SHDI.disposition as `Outbound Removal Type`";
						}
						if($data['assetouboundDestination'] == 'true')
						{
							$sql7 = $sql7.", SHV.VendorName as `Outbound Destination`";
						}
						if($data['assetouboundFacility'] == 'true')
						{
							$sql7 = $sql7.", SHF.FacilityName as `Outbound Facility`";
						}
						if($data['assetouboundSource'] == 'true')
						{
							$sql7 = $sql7.", SC.CustomerName as `Outbound Source`";
						}
						if($data['assetouboundSourceType'] == 'true')
						{
							$sql7 = $sql7.", SCT.Cumstomertype as `Outbound Source Type`";
						}
						if($data['assetouboundMPN'] == 'true')
						{
							$sql7 = $sql7.", SCS.UniversalModelNumber as `Outbound MPN`";
						}
						if($data['assetouboundContainerType'] == 'true')
						{
							$sql7 = $sql7.", SHP.packageName as `Outbound Container Type`";
						}
						if($data['assetouboundSanitizationVerID'] == 'true')
						{
							$sql7 = $sql7.", SCS.SanitizationVerificationID as `Outbound Sanitization Verification ID`";
						}
						$sql7 = $sql7." From shipping_container_serials A
									LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
									LEFT JOIN users U ON U.UserId = A.CreatedBy
									LEFT JOIN pallets P ON P.idPallet = AA.idPallet
									LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
									LEFT JOIN loads L ON L.LoadId = P.LoadId
									LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
									LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
									LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.FirstReceivedCustomPalletID
									LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
									LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
									LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
									LEFT JOIN workflow_input WI ON WI.input_id = AA.input_id
									LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
									LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
									LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
									LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
									LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
									LEFT JOIN business_rule BR ON BR.rule_id = AA.rule_id
									LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
						$sql7 = $sql7." where
						AA.AccountID = '".$_SESSION['user']['AccountID']."'";
						if($data['LoadID'] != '')
							$sql7 = $sql7." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
						if($data['idCustomer'] != '')
						{
							$idCustomer = '';
							for($ci=0;$ci<count($data['idCustomer']);$ci++) {
								$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
							}
							$idCustomer = rtrim($idCustomer, ",");
							$sql7 = $sql7." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
						}
						if($data['CRDatefrom'] != '')
							$sql7 = $sql7." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
						if($data['FacilityID'] != '')
						{
							$FacilityID = '';
							for($fi=0;$fi<count($data['FacilityID']);$fi++) {
								$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
							}
							$FacilityID = rtrim($FacilityID, ",");
							$sql7 = $sql7." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
						}
						if($data['ContainerID'] != '')
							$sql7 = $sql7." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
						if($data['ACDatefrom'] != '')
							$sql7 = $sql7." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
						if($data['SerialNumber'] != '')
							$sql7 = $sql7." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
						if($data['UniversalModelNumber'] != '')
							$sql7 = $sql7." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
						if($data['DispositionID'] != '')
						{
							$dispositionID = '';
							for($di=0;$di<count($data['DispositionID']);$di++) {
								$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
							}
							$dispositionID = rtrim($dispositionID, ",");
							$sql7 = $sql7." AND AA.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
						}
						if($data['CustomerType'] != '')
						{
							$CustomerType = '';
							for($ti=0;$ti<count($data['CustomerType']);$ti++) {
								$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
							}
							$CustomerType = rtrim($CustomerType, ",");
							$sql7 = $sql7." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
						}
						if($data['binid'] != '')
							$sql7 = $sql7." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";
						if($data['Parttype'] != '')
						{
							$Parttype = '';
							for($pi=0;$pi<count($data['Parttype']);$pi++) {
								$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
							}
							$Parttype = rtrim($Parttype, ",");
							//$Parttype = str_replace($Parttype, '\'');
							//$Parttype = str_replace('\'',"'",$Parttype);
							$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
							$sql7 = $sql7." AND AA.part_type IN ( ".$Parttype.")";
						}
						if($data['Input'] != '')
							$sql7 = $sql7." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
						if($data['InputType'] != '')
							$sql7 = $sql7." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
						/*if($data['Workflow'] != '')
						{
							$workflow_id = '';
							for($i=0;$i<count($data['Workflow']);$i++) {
								if($data['Workflow'][$i] == 6)
								{
									$data['Workflow'][$i] = 1;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
								else if($data['Workflow'][$i] == 7)
								{
									$data['Workflow'][$i] = 6;
									$workflow_id = $workflow_id.$data['Workflow'][$i].',';
								}
							}
							$workflow_id = rtrim($workflow_id, ",");
							if($workflow_id != '')
							{
								$sql7 = $sql7." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
							}
						}*/
					}
				}
				$sqlfinal = "Select * from (";
				if($sql1 != '')
				{
					$sqlfinal = $sqlfinal."(".$sql1.") UNION ALL ";
				}
				if($sql2 != '')
				{
					$sqlfinal = $sqlfinal."(".$sql2.") UNION ALL ";
				}
				if($sql3 != '')
				{
					$sqlfinal = $sqlfinal."(".$sql3.") UNION ALL ";
				}
				if($sql4 != '')
				{
					$sqlfinal = $sqlfinal."(".$sql4.") UNION ALL ";
				}
				if($sql5 != '')
				{
					$sqlfinal = $sqlfinal."(".$sql5.") UNION ALL ";
				}
				if($sql6 != '')
				{
					$sqlfinal = $sqlfinal."(".$sql6.") UNION ALL ";
				}
				if($sql7 != '')
				{
					$sqlfinal = $sqlfinal."(".$sql7.") UNION ALL ";
				}
				if($sql1 == '' && $sql2 == '' && $sql3 == '' && $sql4 == '' && $sql5 == '' && $sql6 == '' && $sql7 == '')
				{
					$json['Success'] = false;
					$json['Result'] = 'Please Select atleast one Service Workflow.';
					return json_encode($json);
				}
				$sqlfinal = substr($sqlfinal, 0, -11);
				$sqlfinal = $sqlfinal.") as T";
				$limit = 10*$data['Currentpage'];
				$sqlfinal = $sqlfinal." LIMIT ".$limit.",10";
				mysqli_query($this->connectionlink,"SET SQL_BIG_SELECTS=1");
				/*$json['Success'] = false;
				$json['Result'] = $sqlfinal;
				return json_encode($json);*/
				$query = mysqli_query($this->connectionlink,$sqlfinal);
				if(mysqli_error($this->connectionlink))
				{
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				else
				{
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$i = 0;
						while($row = mysqli_fetch_assoc($query)) {
							$_SESSION['searchcustomer'] = '';
							if($data['CustomerID'] != '')
							{
								$_SESSION['searchcustomer'] = $data['CustomerID'];
							}
							if($data['assetcreatedby'] == 'true')
							{
								$row['Created By'] = $row['FirstName']." ".$row['LastName'];
								$row['Created By'] = strtolower($row['Created By']);
							}
							if($data['transactiondate'] == 'true')
							{
								$date1 = explode(" ",$row['TransactionDate']);
								$date2 = explode("-",$date1[0]);
								$date = $date2[1]."/".$date2[2]."/".$date2[0];
								$date = date("M j, Y g:i a", strtotime($row['TransactionDate']));
								//$time = date("g:i a", strtotime($row['DateCreated']));
								//$row['Created Date'] = $date." ".$time;
								$row['Transaction Date'] = $date;
								unset($row['TransactionDate']);
							}
							if($data['assetscanid'] != 'true')
							{
								unset($row['Scan ID']);
							}
							//unset($row['AuditResultID']);
							unset($row['RefCustomerName1']);
							unset($row['FirstName']);
							unset($row['LastName']);
							$result[$i] = $row;
							$resultpdf[$i] = $row;
							$i++;
						}
						$json['Success'] = true;
						$json['Result'] = $result;
						$json['Result1'] = $asset;
						$json['assetcount'] = $rowfinalcount['assetcount'];
						unset($_SESSION['search_data1234']);
						$_SESSION['search_data1234'] = $data;
						return json_encode($json);
					}
					else {
						$json['Success'] = false;
						$json['Result'] = "No Serials Available";
						return json_encode($json);
					}
				}
			}
			else
			{
				$json['Success'] = false;
				$json['Result'] = 'Please Select atleast one Workflow.';
				return json_encode($json);
			}
		}
		catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function AssetWorkflowXLS($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['assetscanid']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'History Search')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Search Page';
				return json_encode($json);
			}
			if($data['CRDatefrom'] != '')
			{
				$chkdt = $data['CRDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['CRDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['CRDateto'] != '')
			{
				$chkdt = $data['CRDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['CRDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ACDatefrom'] != '')
			{
				$chkdt = $data['ACDatefrom'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ACDatefrom'] = date("Y-m-d H:i",$newdt);
			}
			if($data['ACDateto'] != '')
			{
				$chkdt = $data['ACDateto'];
				$chkdtarr=explode("GMT",$chkdt);
				$newdt= strtotime($chkdtarr[0]);
				$data['ACDateto'] = date("Y-m-d H:i",$newdt);
			}
			if($data['CRDateto'] == '')
			{
				$data['CRDateto1'] = date('Y-m-d H:i', strtotime($data['CRDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['CRDateto1'] = date('Y-m-d H:i', strtotime($data['CRDateto'] . ' + 1 day'));
			}
			if($data['ACDateto'] == '')
			{
				$data['ACDateto1'] = date('Y-m-d H:i', strtotime($data['ACDatefrom'] . ' + 1 day'));
			}
			else
			{
				$data['ACDateto1'] = date('Y-m-d H:i', strtotime($data['ACDateto'] . ' + 1 day'));
			}
			if($data['Workflow'] != '')
				{
					for($i=0;$i<count($data['Workflow']);$i++) {
						//$workflow_id = $workflow_id.$data['Workflow'][$i].',';
						if($data['Workflow'][$i] == 1)
						{
							$sqlcount1 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_sanitization A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.sanitization_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.sanitization_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount1 = $sqlcount1." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount1 = $sqlcount1." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount1 = $sqlcount1." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount1 = $sqlcount1." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount1 = $sqlcount1." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount1 = $sqlcount1." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount1 = $sqlcount1." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount1 = $sqlcount1." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount1 = $sqlcount1." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount1 = $sqlcount1." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount1 = $sqlcount1." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount1 = $sqlcount1." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount1 = $sqlcount1." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount1 = $sqlcount1." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount1 = $sqlcount1." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount1 = $sqlcount1." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 2)
						{
							$sqlcount2 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_failure_analysis A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.fa_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.fa_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount2 = $sqlcount2." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount2 = $sqlcount2." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount2 = $sqlcount2." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount2 = $sqlcount2." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount2 = $sqlcount2." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount2 = $sqlcount2." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount2 = $sqlcount2." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount2 = $sqlcount2." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount2 = $sqlcount2." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount2 = $sqlcount2." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount2 = $sqlcount2." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount2 = $sqlcount2." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount2 = $sqlcount2." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount2 = $sqlcount2." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount2 = $sqlcount2." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount2 = $sqlcount2." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 3)
						{
							$sqlcount3 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_rma_investigation A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.rma_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.rma_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount3 = $sqlcount3." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount3 = $sqlcount3." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount3 = $sqlcount3." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount3 = $sqlcount3." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount3 = $sqlcount3." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount3 = $sqlcount3." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount3 = $sqlcount3." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount3 = $sqlcount3." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount3 = $sqlcount3." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount3 = $sqlcount3." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount3 = $sqlcount3." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount3 = $sqlcount3." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount3 = $sqlcount3." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount3 = $sqlcount3." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount3 = $sqlcount3." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount3 = $sqlcount3." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 4)
						{
							$sqlcount4 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_harvest A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.harvest_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.harvest_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount4 = $sqlcount4." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount4 = $sqlcount4." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount4 = $sqlcount4." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount4 = $sqlcount4." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount4 = $sqlcount4." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount4 = $sqlcount4." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount4 = $sqlcount4." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount4 = $sqlcount4." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount4 = $sqlcount4." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount4 = $sqlcount4." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount4 = $sqlcount4." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount4 = $sqlcount4." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount4 = $sqlcount4." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount4 = $sqlcount4." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount4 = $sqlcount4." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount4 = $sqlcount4." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 5)
						{
							$sqlcount5 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_repair A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.repair_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.repair_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount5 = $sqlcount5." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount5 = $sqlcount5." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount5 = $sqlcount5." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount5 = $sqlcount5." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount5 = $sqlcount5." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount5 = $sqlcount5." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount5 = $sqlcount5." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount5 = $sqlcount5." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount5 = $sqlcount5." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount5 = $sqlcount5." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount5 = $sqlcount5." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount5 = $sqlcount5." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount5 = $sqlcount5." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount5 = $sqlcount5." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount5 = $sqlcount5." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount5 = $sqlcount5." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 5)
						{
							$sqlcount5 = "Select count(distinct(A.AssetScanID)) as assetcount From asset_repair A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.ToCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.repair_input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.repair_rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount5 = $sqlcount5." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount5 = $sqlcount5." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount5 = $sqlcount5." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount5 = $sqlcount5." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount5 = $sqlcount5." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount5 = $sqlcount5." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount5 = $sqlcount5." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount5 = $sqlcount5." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount5 = $sqlcount5." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount5 = $sqlcount5." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount5 = $sqlcount5." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount5 = $sqlcount5." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount5 = $sqlcount5." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount5 = $sqlcount5." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount5 = $sqlcount5." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount5 = $sqlcount5." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 6)
						{
							$sqlcount6 = "Select count(distinct(A.AssetScanID)) as assetcount From asset A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN pallets P ON P.idPallet = A.idPallet
								LEFT JOIN facility F ON F.FacilityID = A.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = A.FirstReceivedCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = A.input_id
								LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount6 = $sqlcount6." where
								A.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount6 = $sqlcount6." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount6 = $sqlcount6." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount6 = $sqlcount6." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount6 = $sqlcount6." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount6 = $sqlcount6." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount6 = $sqlcount6." AND A.DateCreated between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount6 = $sqlcount6." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount6 = $sqlcount6." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount6 = $sqlcount6." AND A.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount6 = $sqlcount6." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount6 = $sqlcount6." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount6 = $sqlcount6." AND A.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount6 = $sqlcount6." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount6 = $sqlcount6." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount6 = $sqlcount6." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
						else if($data['Workflow'][$i] == 7)
						{
							$sqlcount7 = "Select count(distinct(A.AssetScanID)) as assetcount From shipping_container_serials A
								LEFT JOIN users U ON U.UserId = A.CreatedBy
								LEFT JOIN asset AA ON AA.AssetScanID = A.AssetScanID
								LEFT JOIN pallets P ON P.idPallet = AA.idPallet
								LEFT JOIN facility F ON F.FacilityID = AA.FacilityID
								LEFT JOIN loads L ON L.LoadId = P.LoadId
								LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
								LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.FirstReceivedCustomPalletID
								LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
								LEFT JOIN workflow_input WI ON WI.input_id = AA.input_id
								LEFT JOIN business_rule BR ON BR.rule_id = AA.rule_id
								LEFT JOIN workflow BRW ON BRW.workflow_id = BR.workflow_id";
								$sqlcount7 = $sqlcount7." where
								AA.AccountID = '".$_SESSION['user']['AccountID']."'";
								if($data['LoadID'] != '')
									$sqlcount7 = $sqlcount7." AND L.LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['LoadID'])."'";
									if($data['idCustomer'] != '')
									{
										$idCustomer = '';
										for($ci=0;$ci<count($data['idCustomer']);$ci++) {
											$idCustomer = $idCustomer.$data['idCustomer'][$ci].',';
										}
										$idCustomer = rtrim($idCustomer, ",");
										$sqlcount7 = $sqlcount7." AND SC.CustomerID IN ( ".mysqli_real_escape_string($this->connectionlink,$idCustomer).")";
									}
									if($data['CRDatefrom'] != '')
										$sqlcount7 = $sqlcount7." AND P.ReceivedDate between '".mysqli_real_escape_string($this->connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['CRDateto1'])."'";
									if($data['FacilityID'] != '')
									{
										$FacilityID = '';
										for($fi=0;$fi<count($data['FacilityID']);$fi++) {
											$FacilityID = $FacilityID.$data['FacilityID'][$fi].',';
										}
										$FacilityID = rtrim($FacilityID, ",");
										$sqlcount7 = $sqlcount7." AND F.FacilityID IN ( ".mysqli_real_escape_string($this->connectionlink,$FacilityID).")";
									}
									if($data['ContainerID'] != '')
										$sqlcount7 = $sqlcount7." AND P.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerID'])."'";
									if($data['ACDatefrom'] != '')
										$sqlcount7 = $sqlcount7." AND A.CreatedDate between '".mysqli_real_escape_string($this->connectionlink,$data['ACDatefrom'])."' and '".mysqli_real_escape_string($this->connectionlink,$data['ACDateto1'])."'";
									if($data['SerialNumber'] != '')
										$sqlcount7 = $sqlcount7." AND A.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
									if($data['UniversalModelNumber'] != '')
										$sqlcount7 = $sqlcount7." AND A.UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
									if($data['DispositionID'] != '')
									{
										$dispositionID = '';
										for($di=0;$di<count($data['DispositionID']);$di++) {
											$dispositionID = $dispositionID.$data['DispositionID'][$di].',';
										}
										$dispositionID = rtrim($dispositionID, ",");
										$sqlcount7 = $sqlcount7." AND AA.disposition_id IN ( ".mysqli_real_escape_string($this->connectionlink,$dispositionID).")";
									}
									if($data['CustomerType'] != '')
									{
										$CustomerType = '';
										for($ti=0;$ti<count($data['CustomerType']);$ti++) {
											$CustomerType = $CustomerType.$data['CustomerType'][$ti].',';
										}
										$CustomerType = rtrim($CustomerType, ",");
										$sqlcount7 = $sqlcount7." AND SC.CustomerType IN ( ".mysqli_real_escape_string($this->connectionlink,$CustomerType).")";
									}
									if($data['binid'] != '')
										$sqlcount7 = $sqlcount7." AND CP.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['binid'])."'";

									if($data['Parttype'] != '')
									{
										$Parttype = '';
										for($pi=0;$pi<count($data['Parttype']);$pi++) {
											$Parttype = $Parttype."'".$data['Parttype'][$pi]."',";
										}
										$Parttype = rtrim($Parttype, ",");
										//$Parttype = str_replace($Parttype, '\'');
										//$Parttype = str_replace('\'',"'",$Parttype);
										$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
										$sqlcount7 = $sqlcount7." AND AA.part_type IN ( ".$Parttype.")";
									}
									if($data['Input'] != '')
										$sqlcount7 = $sqlcount7." AND WI.input = '".mysqli_real_escape_string($this->connectionlink,$data['Input'])."'";
									if($data['InputType'] != '')
										$sqlcount7 = $sqlcount7." AND WI.input_type = '".mysqli_real_escape_string($this->connectionlink,$data['InputType'])."'";
									/*if($data['Workflow'] != '')
									{
										$workflow_id = '';
										for($i=0;$i<count($data['Workflow']);$i++) {
											if($data['Workflow'][$i] == 6)
											{
												$data['Workflow'][$i] = 1;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
											else if($data['Workflow'][$i] == 7)
											{
												$data['Workflow'][$i] = 6;
												$workflow_id = $workflow_id.$data['Workflow'][$i].',';
											}
										}
										$workflow_id = rtrim($workflow_id, ",");
										if($workflow_id != '')
										{
											$sqlcount7 = $sqlcount7." AND BRW.workflow_id IN ( ".mysqli_real_escape_string($this->connectionlink,$workflow_id).")";
										}
									}*/
									/*$json['Success'] = false;
									$json['Result'] = $sqlcount;
									return json_encode($json);
								$querycount = mysqli_query($this->connectionlink,$sqlcount);
								$rowcount = mysqli_fetch_assoc($querycount);
								$totcount = $totcount+$rowcount['assetcount'];*/
						}
					}
					$sqlfinalcount = "Select SUM(assetcount) as assetcount from (";
					if($sqlcount1 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount1.") UNION ALL ";
					}
					if($sqlcount2 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount2.") UNION ALL ";
					}
					if($sqlcount3 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount3.") UNION ALL ";
					}
					if($sqlcount4 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount4.") UNION ALL ";
					}
					if($sqlcount5 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount5.") UNION ALL ";
					}
					if($sqlcount6 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount6.") UNION ALL ";
					}
					if($sqlcount7 != '')
					{
						$sqlfinalcount = $sqlfinalcount."(".$sqlcount7.") UNION ALL ";
					}
					$sqlfinalcount = substr($sqlfinalcount, 0, -11);
					$sqlfinalcount = $sqlfinalcount.") as T";
					/*$json['Success'] = false;
					$json['Result'] = $sqlfinalcount;
					return json_encode($json);*/
					$queryfinalcount = mysqli_query($this->connectionlink,$sqlfinalcount);
					$rowfinalcount = mysqli_fetch_assoc($queryfinalcount);
				}
			/*$json['Success'] = false;
			$json['Result'] = $sqlcount;
			return json_encode($json);*/
			$json['Success'] = true;
			$json['Result'] = $rowfinalcount['assetcount'];
			return json_encode($json);
		}
		catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function Getrepairdispositions($data)
  {
    	//echo "<pre>";print_r($data);exit;

        $StartDate = date("Y-m-d");
        $EndDate = date('Y-m-d', strtotime($StartDate . ' + 1 day'));
        $sqlcount = "Select count(*) as assetcount,disposition from asset WHERE DateCreated Between '".$StartDate."' and '".$EndDate."'
        Group By disposition";
        $querycount = mysqli_query($this->connectionlink,$sqlcount);
        if(mysqli_affected_rows($this->connectionlink) > 0) {
            $i = 0;
					$totcount = 0;
					while($rowcount = mysqli_fetch_assoc($querycount)) {
						$result[$i] = $rowcount;
						$totcount = $totcount+$rowcount['assetcount'];
						$i++;
						$rowcount1['assetcount'] = $totcount;
						$rowcount1['disposition'] = 'Grand Total';
					}
					$result[$i] = $rowcount1;
				}
				else {
					$json['Success'] = false;
					$json['Result'] = $sqlcount;
					return json_encode($json);
				}
        $json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
    }

		public function GenerateLiveSearchReport($data)
	  {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['assetscanid']
			);
			try {
				if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Live Search')) {
					$json['Success'] = false;
					$json['Result'] = 'No Access to Search Page';
					return json_encode($json);
				}
				unset($_SESSION['livesearchreport_data']);
				$_SESSION['livesearchreport_data'] = $data;
				$json['Success'] = true;
				return json_encode($json);
			} catch (Exception $e) {
				$json['Success'] = false;
				$json['Result'] = $e->getMessage();
				return json_encode($json);
			}
		}

		public function GenerateWorkflowSearchReport($data)
	  {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['assetscanid']
			);
			try {
				if(! $this->isPermitted($_SESSION['user']['ProfileID'],'History Search')) {
					$json['Success'] = false;
					$json['Result'] = 'No Access to History Search Page';
					return json_encode($json);
				}
				unset($_SESSION['workflowsearchreport_data']);
				$_SESSION['workflowsearchreport_data'] = $data;
				$json['Success'] = true;
				return json_encode($json);
			} catch (Exception $e) {
				$json['Success'] = false;
				$json['Result'] = $e->getMessage();
				return json_encode($json);
			}
		}

}
?>
