(function () {
    'use strict';

    angular.module('app').controller("sanitization", function ($scope,$http,$filter,$upload,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Sanitization',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });


        $scope.PartTypes = [];
        $scope.COOList = [];


        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetPartTypesAndCOO',
            success: function(data){
                if(data.Success) {
                    if(data.PartTypes) {
                        $scope.PartTypes = data.PartTypes;
                    }
                    if(data.COOList) {
                        $scope.COOList = data.COOList;
                    }
                } else {
                    $scope.PartTypes = [];
                    $scope.COOList = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.asset = {'SanitizationMedia' : [{'media_out_sn' : 'n/a','media_out_mpn' : 'n/a','media_bin_id' : 'n/a','media_in_sn' : 'n/a','media_in_mpn' : 'n/a'}],'SanitizationIn' : [{'media_in_sn' : 'n/a','media_in_mpn' : 'n/a'}],'sanitization_custom_id' : 'n/a','sanitization_notes' : 'n/a','sanitization_verification_id' : 'n/a','sanitization_seal_id' : 'n/a'};
        $scope.CustomPalletID = '';
        $scope.InputResults = [];
        $scope.Stations = [];
        $scope.StationCustomPallets = [];
        $scope.DefaultInputID = '';
        $scope.Assets = [];
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetInputResults&Workflow=Sanitization&workflow_id=3',
            success: function(data){
                if(data.Success) {
                    $scope.InputResults = data.Result;
                    if(data.Default) {
                        $scope.asset.sanitization_input_id = data.Default;
                        $scope.DefaultInputID = data.Default;
                    }
                } else {
                    $scope.InputResults = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });


        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilityStations&Workflow=Sanitization&workflow_id=3',
            success: function(data){
                if(data.Success) {
                    $scope.Stations = data.Result;
                } else {
                    $scope.Stations = [];
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        if($stateParams.AssetScanID) {

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'audit/includes/sanitization_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetSanitizationAssetDetails&AssetScanID='+$stateParams.AssetScanID,
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        if(data.Result.BinName) {
                            //$scope.BinName = data.Result.BinName;
                            $scope.asset.AssetScanID = data.Result.AssetScanID; 
                            $scope.asset.SerialNumber = data.Result.SerialNumber; 
                            $scope.GetCurrentTime($scope.asset,'serial_scan_time');
                            //$scope.GetCustomPalletDetails();                                                                                  
                            $scope.asset.UniversalModelNumber = data.Result.UniversalModelNumber;

                            $scope.asset.parttypeid = data.Result.parttypeid;
                            $scope.asset.COOID = data.Result.COOID;

                            $scope.GetCurrentTime($scope.asset,'part_type_scan_time');
                            $scope.GetCurrentTime($scope.asset,'coo_scan_time');
                            //$scope.ApplyBusinessRule();
                        }

                        if(data.Result.media_parttypeid > 0) {

                            $scope.asset.media_parttypeid = data.Result.media_parttypeid;
                            $scope.asset.media_input_id = data.Result.media_input_id;

                            if($scope.asset.SanitizationMedia.length > 0) {
                                for(var i=0;i<$scope.asset.SanitizationMedia.length;i++) {
                                    $scope.asset.SanitizationMedia[i]['parttypeid'] = data.Result.media_parttypeid;
                                    $scope.asset.SanitizationMedia[i]['input_id'] = data.Result.media_input_id;

                                    $scope.GetCurrentTime($scope.asset.SanitizationMedia[i],'part_type_scan_time');
                                    $scope.GetCurrentTime($scope.asset.SanitizationMedia[i],'result_scan_time');
                                    $scope.GetSubComponentDisposition($scope.asset.SanitizationMedia[i]);
                                }
                            }

                        } else {
                            $scope.asset.media_parttypeid = '';
                            $scope.asset.media_input_id = '';
                        }
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                        
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });

        }

        $scope.GetStationCustomPallets = function () {
            if($scope.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetStationCustomPallets&SiteID='+$scope.SiteID+'&Workflow=Sanitization&workflow_id=3&CustomPalletID='+$scope.CustomPalletID,
                    success: function(data){
                        if(data.Success) {
                            $scope.StationCustomPallets = data.Result;
                            if($scope.asset.SerialNumber != '' && $scope.asset.UniversalModelNumber != '') {
                                $scope.ApplyBusinessRule();
                            }
                        } else {
                            $scope.StationCustomPallets = [];
                            $scope.SiteID = '';
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.StationCustomPallets = [];
            }
        };

        $scope.MapCustomPalletToDisposition = function (item) {
            if($scope.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MapCustomPalletToDisposition&SiteID='+$scope.SiteID+'&Workflow=Sanitization&workflow_id=3'+'&'+$.param(item)+'&SourceCustomPalletID='+$scope.CustomPalletID,
                    success: function(data) {
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            item.CustomPalletID = data.CustomPalletID;
                            item.AssetsCount = data.AssetsCount;
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            item.BinName = '';
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };

        $scope.GetCustomPalletDetails = function () {
            if($scope.BinName) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/sanitization_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetCustomPalletDetails&BinName='+$scope.BinName+'&SiteID='+$scope.SiteID,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.CustomPalletID = data.CustomPalletID;
                            if($scope.asset.SerialNumber && !$scope.asset.AssetScanID) {
                                $scope.GetMPNFromSerialSanitization();
                            }
                            //$scope.GetSanitizationAssets();
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.CustomPalletID = '';
                            $scope.BinName = '';
                           // $scope.GetSanitizationAssets();
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.CustomPalletID = '';
            }
        };



        $scope.GetMPNPartTypeDetails = function (asset) {
            if(asset.UniversalModelNumber != '') {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/rma_investigation_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetMPNPartTypeDetails&UniversalModelNumber='+asset.UniversalModelNumber,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            asset.parttypeid = data.parttypeid;
                            asset.COOID = data.COOID;
                            $scope.ApplyBusinessRule();
                        } else {
                            asset.parttypeid = '';
                            asset.COOID = '';                      
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );                            
                        }                        
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });


            }
        };



       /* $scope.GetSanitizationAssets = function () {
            if($scope.CustomPalletID > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/sanitization_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetSanitizationAssets&CustomPalletID='+$scope.CustomPalletID,
                    success: function(data) {
                        if(data.Success) {
                            $scope.Assets = data.Result;
                        } else {
                            $scope.Assets = [];
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.Assets = [];
            }
        };*/

         $scope.busy = false;
        $scope.AssetsSanizedPanel = [];
        $scope.Assets = [];
       // $scope.pagedItems = [];

        //Start Pagination Logic
        $scope.itemsPerPage = 20;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            //if($scope.CustomPalletID > 0)  {
            if($scope.SiteID > 0)  {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/sanitization_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetSanitizationAssets&limit='+$scope.itemsPerPage+'&CustomPalletID='+$scope.CustomPalletID+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text))+'&SiteID='+$scope.SiteID,
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {

                            $scope.Assets = data.Result;
                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            // $mdToast.show (
                            //     $mdToast.simple()
                            //         .content(data.Result)
                            //         .action('OK')
                            //         .position('right')
                            //         .hideDelay(0)
                            //         .toastClass('md-toast-danger md-block')
                            // );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'audit/includes/sanitization_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetSanitizationAssets&limit='+$scope.itemsPerPage+'&CustomPalletID='+$scope.CustomPalletID+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text))+'&SiteID='+$scope.SiteID,
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.Assets = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        // $mdToast.show (
                        //     $mdToast.simple()
                        //         .content(data.Result)
                        //         .action('OK')
                        //         .position('right')
                        //         .hideDelay(0)
                        //         .toastClass('md-toast-danger md-block')
                        // );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        //End Pagination Logic


        $scope.disposition_color = '';
        $scope.sub_disposition_color = '';
        $scope.Sub_BinName = 'n/a';
        $scope.ApplyBusinessRule = function () {
            if($scope.asset.sanitization_input_id > 0) {
                $scope.asset.input_id = $scope.asset.sanitization_input_id;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ApplyBusinessRule&workflow_id=3&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID,
                    success: function(data){
                        if(data.Success) {
                            if (data.ExactMPN) {
                                $scope.asset.UniversalModelNumber = data.ExactMPN;
                            }
                            $scope.asset.disposition_id = data.Result.disposition_id;
                            $scope.asset.disposition = data.Result.disposition;
                            $scope.asset.sanitization_rule_id = data.Result.rule_id;
                            if(data.CustomPalletID) {
                                $scope.asset.CustomPalletID = data.CustomPalletID;
                                $scope.asset.BinName = data.BinName;
                                $scope.GetCurrentTime($scope.asset,'bin_scan_time');
                            } else {
                                $scope.asset.CustomPalletID = '';
                                $scope.asset.BinName = '';
                            }
                            $scope.asset.sub_disposition_id = data.Result.sub_disposition_id;
                            $scope.asset.sub_disposition = data.Result.sub_disposition;
                            $scope.asset.rule_description = data.Result.rule_description;
                            $scope.asset.rule_id_text  = data.Result.rule_id_text;
                            if(data.MPN) {
                                if(data.MPN.CSPLINK != '') {                                                       
                                    $scope.asset.CSPArray = (data.MPN.CSPLINK).split(',');
                                } else {
                                    $scope.asset.CSPArray = [];
                                }
                                if(data.MPN.nd_sanitization_type != '') {
                                    $scope.asset.nd_sanitization_type = data.MPN.nd_sanitization_type;
                                }
                            }                                                       
                            // if(data.MPN) { 
                            //     $scope.asset.nd_sanitization_type = data.MPN.nd_sanitization_type;
                            //     $scope.asset.file_url = data.MPN.nd_sanitization_type_file_url;
                            //     $scope.nd_sanitization_type_array = $scope.asset.nd_sanitization_type.split(",");
                            // }                            
                            $scope.disposition_color = data.Result.color_code;
                            $scope.sub_disposition_color = data.Result.sub_color_code;

                            if(data.Sub_CustomPalletID > 0) {
                                $scope.Sub_BinName = data.Sub_BinName;
                                if($scope.asset.SanitizationMedia.length > 0) {
                                    for(var i=0;i<$scope.asset.SanitizationMedia.length;i++) {
                                        //$scope.asset.SanitizationMedia[i]['media_bin_id'] = data.Sub_BinName;
                                        //$scope.asset.SanitizationMedia[i]['CustomPalletID'] = data.Sub_CustomPalletID;
                                    }
                                }
                            }

                            //$window.document.getElementById('sanitization_controller_login_id').focus();
                            $window.document.getElementById('sanitization_verification_id').select();
                        } else {
                            if (data.ExactMPN) {
                                $scope.asset.UniversalModelNumber = data.ExactMPN;
                            }
                            $scope.asset.disposition_id = '';
                            $scope.asset.disposition = '';
                            $scope.disposition_color = '';
                            $scope.asset.CustomPalletID = '';
                            $scope.asset.BinName = '';
                            $scope.asset.sanitization_rule_id = '';
                            $scope.asset.sub_disposition_id = '';
                            $scope.asset.sub_disposition = '';
                            $scope.sub_disposition_color = '';
                            $scope.asset.rule_description = '';
                            $scope.asset.rule_id_text  = '';
                            $scope.Sub_BinName = 'n/a';
                            if($scope.asset.SanitizationMedia.length > 0) {
                                for(var i=0;i<$scope.asset.SanitizationMedia.length;i++) {
                                    //$scope.asset.SanitizationMedia[i]['media_bin_id'] = '';
                                }
                            }
                            //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            if(data.MPN) {
                                if(data.MPN.CSPLINK != '') {                                                       
                                    $scope.asset.CSPArray = (data.MPN.CSPLINK).split(',');
                                } else {
                                    $scope.asset.CSPArray = [];
                                }
                                if(data.MPN.nd_sanitization_type != '') {
                                    $scope.asset.nd_sanitization_type = data.MPN.nd_sanitization_type;
                                }
                            }
                            //$scope.asset.nd_sanitization_type = '';
                            //$scope.asset.file_url = '';
                            //$scope.nd_sanitization_type_array = [];
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };


        $scope.GetmpnmediaDetails = function (media) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'audit/includes/sanitization_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetmpnmediaDetails&SerialNumber='+media.media_in_sn,
                success: function(data){
                    if(data.Success) {
                        //Start check If Serial already in the list
                        var exists = false;
                        for(var i=0;i<$scope.asset.SanitizationIn.length;i++) {
                            //if($scope.asset.SanitizationIn[i]['InventoryID'] == data.Result.InventoryID) {
                            if($scope.asset.SanitizationIn[i]['AssetScanID'] == data.Result.AssetScanID) {
                                exists = true;
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content('Serial already in the list')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                        }
                        //End check If Serial already in the list
                        if(! exists ) {
                            media.media_in_mpn = data.Result.UniversalModelNumber;
                            //media.InventoryID = data.Result.InventoryID;
                            media.AssetScanID = data.Result.AssetScanID;
                            media.media_in_CustomPalletID = data.Result.CustomPalletID;
                            $scope.GetCurrentTime(media,'ingested_mpn_scan_time');
                        }
                    } else {
                        media.media_in_mpn = '';
                        //media.InventoryID = '';
                        media.AssetScanID = '';
                        media.media_in_CustomPalletID = '';
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.SNSerialChanged = function (media) {
            media.media_in_mpn = '';
            //media.InventoryID = '';
            media.AssetScanID = '';
            media.media_in_CustomPalletID = '';
        };

         $scope.GetmpnDetails = function () {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'audit/includes/sanitization_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetmpnDetails&SerialNumber='+$scope.asset.SerialNumber+'&CustomPalletID='+$scope.CustomPalletID,
                success: function(data){
                    if(data.Success) {
                        $scope.media.UniversalModelNumber = data.media.UniversalModelNumber;
                        } else {
                        $scope.media.UniversalModelNumber = '';
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.mediaSerialChanged = function () {
            $scope.media.UniversalModelNumber = '';

        };


        $scope.GetMPNFromSerialSanitization = function () {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'audit/includes/sanitization_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetMPNFromSerialSanitization&SerialNumber='+$scope.asset.SerialNumber+'&CustomPalletID='+$scope.CustomPalletID,
                success: function(data){
                    if(data.Success) {
                        $scope.asset.UniversalModelNumber = data.Result.UniversalModelNumber;
                        $scope.GetCurrentTime($scope.asset,'mpn_scan_time');

                        $scope.asset.parttypeid = data.Result.parttypeid;
                        $scope.asset.COOID = data.Result.COOID;

                        $scope.GetCurrentTime($scope.asset,'part_type_scan_time');
                        $scope.GetCurrentTime($scope.asset,'coo_scan_time');

                        $scope.asset.nd_sanitization_type = data.Result.nd_sanitization_type;
                        $scope.asset.AssetScanID = data.Result.AssetScanID;
                        $scope.asset.file_url = data.Result.file_url;

                        if(data.Result.CSPLINK != '') {                                                       
                            $scope.asset.CSPArray = (data.Result.CSPLINK).split(',');
                        } else {
                            $scope.asset.CSPArray = [];
                        }
                        $scope.ApplyBusinessRule();
                    } else {
                        $scope.asset.UniversalModelNumber = '';
                        $scope.asset.parttypeid = '';
                        $scope.asset.COOID = '';
                        
                        $scope.asset.nd_sanitization_type = '';
                        $scope.nd_sanitization_type_array = [];
                        $scope.asset.CSPArray = [];
                        $scope.asset.AssetScanID = '';
                        $scope.asset.sub_disposition = '';
                        $scope.asset.sub_disposition_id = '';
                        $scope.sub_disposition_color = '';
                        $scope.asset.rule_description = '';
                        $scope.asset.rule_id_text  = '';
                        $scope.asset.file_url = '';
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.SerialChanged = function () {
            $scope.asset.AssetScanID = '';
            $scope.asset.UniversalModelNumber = '';
            $scope.asset.nd_sanitization_type = '';
            $scope.nd_sanitization_type_array = [];
            $scope.asset.disposition_id = '';
            $scope.asset.disposition = '';
            $scope.disposition_color = '';
            $scope.asset.CustomPalletID = '';
            $scope.asset.BinName = '';
            $scope.asset.sanitization_rule_id = '';
            $scope.asset.sub_disposition = '';
            $scope.asset.sub_disposition_id = '';
            $scope.sub_disposition_color = '';
            $scope.asset.rule_description = '';
            $scope.asset.rule_id_text  = '';
            $scope.asset.file_url = '';

            $scope.Sub_BinName = 'n/a';
            if($scope.asset.SanitizationMedia.length > 0) {
                for(var i=0;i<$scope.asset.SanitizationMedia.length;i++) {
                    //$scope.asset.SanitizationMedia[i]['media_bin_id'] = '';
                }
            }
        };

        $scope.MPNChanged = function () {
            $scope.asset.nd_sanitization_type = '';
            $scope.nd_sanitization_type_array = []; 
            $scope.asset.CSPArray = [];
            $scope.asset.disposition_id = '';
            $scope.asset.disposition = '';
            $scope.disposition_color = '';
            $scope.asset.CustomPalletID = '';
            $scope.asset.BinName = '';
            $scope.asset.sanitization_rule_id = '';
            $scope.asset.sub_disposition = '';
            $scope.asset.sub_disposition_id = '';
            $scope.sub_disposition_color = '';
            $scope.asset.rule_description = '';
            $scope.asset.rule_id_text  = '';
            $scope.asset.file_url = '';
            $scope.asset.sanitization_custom_id = 'n/a';
            $scope.Sub_BinName = 'n/a';
            if($scope.asset.SanitizationMedia.length > 0) {
                for(var i=0;i<$scope.asset.SanitizationMedia.length;i++) {
                    //$scope.asset.SanitizationMedia[i]['media_bin_id'] = '';
                }
            }
        };

        $scope.AddSanitizationMedia = function () {
            //$scope.asset.SanitizationMedia.push({'media_out_sn' : 'n/a','media_out_mpn' : 'n/a','media_bin_id' : 'n/a','media_in_sn' : 'n/a','media_in_mpn' : 'n/a'});
            //$scope.asset.SanitizationMedia.push({'media_out_sn' : 'n/a','media_out_mpn' : 'n/a','media_bin_id' : $scope.Sub_BinName,'media_in_sn' : 'n/a','media_in_mpn' : 'n/a'});
            $scope.asset.SanitizationMedia.push({'media_out_sn' : 'n/a','media_out_mpn' : 'n/a','media_bin_id' : 'n/a','media_in_sn' : 'n/a','media_in_mpn' : 'n/a','parttypeid': $scope.asset.media_parttypeid,'input_id': $scope.asset.media_input_id});

            $scope.GetCurrentTime($scope.asset.SanitizationMedia[$scope.asset.SanitizationMedia.length - 1],'part_type_scan_time');
            $scope.GetCurrentTime($scope.asset.SanitizationMedia[$scope.asset.SanitizationMedia.length - 1],'result_scan_time');
            $scope.GetSubComponentDisposition($scope.asset.SanitizationMedia[$scope.asset.SanitizationMedia.length - 1]);
        };

        $scope.RemoveSanitizationMedia = function (ev,ind) {

            var confirm = $mdDialog.confirm()
            .title('Are you sure, You want to Delete ?')
            .content('')
            .ariaLabel('Lucky day')
            .targetEvent(ev)
            .ok('Delete')
            .cancel('Cancel');
            $mdDialog.show(confirm).then(function() {
                $scope.asset.SanitizationMedia.splice(ind,1);
            }, function() {
            });

        };


        $scope.AddSanitizationIn = function () {
            $scope.asset.SanitizationIn.push({'media_in_sn' : 'n/a','media_in_mpn' : 'n/a'});
        };

        $scope.RemoveSanitizationIn = function (ev,ind) {

            var confirm = $mdDialog.confirm()
            .title('Are you sure, You want to Delete ?')
            .content('')
            .ariaLabel('Lucky day')
            .targetEvent(ev)
            .ok('Delete')
            .cancel('Cancel');
            $mdDialog.show(confirm).then(function() {
                $scope.asset.SanitizationIn.splice(ind,1);
            }, function() {
            });

        };


        $scope.UpdateAssetSanitization = function (ev) {
            if(!$scope.asset.PasswordVerified) {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            } else if($scope.asset.CloseBin) {

                var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Empty BIN ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Empty BIN')
                .cancel('No');
                $mdDialog.show(confirm).then(function() {
                    $scope.asset.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'audit/includes/sanitization_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateAssetSanitization&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID+'&FromCustomPalletID='+$scope.CustomPalletID+'&CloseBin=1',
                        success: function(data){
                            if(data.Success) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                window.location = "#!/PendingSanitization";
                                return;
                                if(data.Asset) {
                                    if($scope.StationCustomPallets.length > 0) {
                                        for(var i=0;i<$scope.StationCustomPallets.length;i++) {
                                            if($scope.StationCustomPallets[i].CustomPalletID == data.Asset.CustomPalletID) {
                                                $scope.StationCustomPallets[i].AssetsCount = data.Asset.AssetsCount;
                                            }
                                        }
                                    }
                                    if($scope.Assets.length == 0) {
                                        $scope.CallServerFunction(0);
                                    } else {
                                        $scope.Assets.unshift(data.Asset);
                                    }
                                }
                                if(data.CloseMessage) {
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.CloseMessage)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-info md-block')
                                    );
                                }
                                if(data.BinClosed == '1') {
                                    $scope.CustomPalletID = '';
                                    $scope.BinName = '';
                                    $scope.Assets = [];
                                }
                                $scope.ClearAsset();
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }, function() {
                    $scope.asset.CloseBin = false;
                    $scope.asset.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'audit/includes/sanitization_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateAssetSanitization&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID+'&FromCustomPalletID='+$scope.CustomPalletID,
                        success: function(data){
                            if(data.Success) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                window.location = "#!/PendingSanitization";
                                return;
                                if(data.Asset) {
                                    if($scope.StationCustomPallets.length > 0) {
                                        for(var i=0;i<$scope.StationCustomPallets.length;i++) {
                                            if($scope.StationCustomPallets[i].CustomPalletID == data.Asset.CustomPalletID) {
                                                $scope.StationCustomPallets[i].AssetsCount = data.Asset.AssetsCount;
                                            }
                                        }
                                    }
                                    if($scope.Assets.length == 0) {
                                        $scope.CallServerFunction(0);
                                    } else {
                                        $scope.Assets.unshift(data.Asset);
                                    }
                                }

                                if(data.CloseMessage) {
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.CloseMessage)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-info md-block')
                                    );
                                }

                                $scope.ClearAsset();
                                $window.document.getElementById('SerialNumber').focus();
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }
                    });

                });

            } else {
                $scope.asset.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/sanitization_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=UpdateAssetSanitization&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID+'&FromCustomPalletID='+$scope.CustomPalletID,
                    success: function(data){
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            window.location = "#!/PendingSanitization";
                            return;
                            if(data.Asset) {
                                if($scope.StationCustomPallets.length > 0) {
                                    for(var i=0;i<$scope.StationCustomPallets.length;i++) {
                                        if($scope.StationCustomPallets[i].CustomPalletID == data.Asset.CustomPalletID) {
                                            $scope.StationCustomPallets[i].AssetsCount = data.Asset.AssetsCount;
                                        }
                                    }
                                }
                                if($scope.Assets.length == 0) {
                                    $scope.CallServerFunction(0);
                                } else {
                                    $scope.Assets.unshift(data.Asset);
                                }
                            }
                            if(data.CloseMessage) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.CloseMessage)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-info md-block')
                                );
                            }
                            $scope.ClearAsset();
                            $window.document.getElementById('SerialNumber').focus();
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.asset.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.asset.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            }
        };

        $scope.ClearAsset = function () {
            if($scope.CopyCustomID) {
                var custom_id = $scope.asset.sanitization_custom_id;
                $scope.asset = {'sanitization_input_id' : $scope.DefaultInputID,'SanitizationMedia' : [{'media_out_sn' : 'n/a','media_out_mpn' : 'n/a','media_bin_id' : 'n/a','media_in_sn' : 'n/a','media_in_mpn' : 'n/a'}],'SanitizationIn': [{'media_in_sn' : 'n/a','media_in_mpn' : 'n/a'}],'sanitization_custom_id' : custom_id,'sanitization_notes' : 'n/a','sanitization_verification_id' : 'n/a','sanitization_seal_id' : 'n/a'};
            } else {
                $scope.asset = {'sanitization_input_id' : $scope.DefaultInputID,'SanitizationMedia' : [{'media_out_sn' : 'n/a','media_out_mpn' : 'n/a','media_bin_id' : 'n/a','media_in_sn' : 'n/a','media_in_mpn' : 'n/a'}],'SanitizationIn': [{'media_in_sn' : 'n/a','media_in_mpn' : 'n/a'}],'sanitization_custom_id' : 'n/a','sanitization_notes' : 'n/a','sanitization_verification_id' : 'n/a','sanitization_seal_id' : 'n/a'};
            }
        };

        $scope.GenerateSubComponentSerialNumber = function (page,media,ind) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'audit/includes/audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GenerateSubComponentSerialNumber&Page='+page+'&Workflow=Sanitization',
                success: function(data){
                    if(data.Success) {
                        media.media_out_sn = data.Result;
                        //window.location.href = '../label/master/examples/media_label.php?id='+data.Result;
                        window.open('../label/master/examples/media_label.php?id='+data.Result, '_blank');
                        $window.document.getElementById("media_out_mpn"+ind).select();
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };


        $scope.GenerateSeal = function (asset) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'audit/includes/audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GenerateSubComponentSerialNumber&Workflow=Sanitization',
                success: function(data){
                    if(data.Success) {
                        asset.sanitization_seal_id = data.Result;
                        window.open('../label/master/examples/seal_label.php?id='+data.Result, '_blank');
                        //window.location.href = '../label/master/examples/media_label.php?id='+data.Result;
                        //window.open('../label/master/examples/media_label.php?id='+data.Result, '_blank');

                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };



        function SanitizationTPVRController($scope,$mdDialog,$mdToast,$window) {
          $window.document.getElementById('scan_for_save').focus();
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails1);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails1);
            };
        }
        function afterShowAnimation1 () {            
            $window.document.getElementById("controller_password").focus();
        }
        $scope.confirmDetails1 = {};
        $scope.ValidateSanitizationControllerPopup = function (ev) {
            $scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: SanitizationTPVRController,
                templateUrl: 'password1.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation1,
                clickOutsideToClose:true
            })
            .then(function(confirmDetails1) {
                if(confirmDetails1) {                
                    $rootScope.$broadcast('preloader:active');
                    $scope.confirmDetails1 = confirmDetails1;
                    jQuery.ajax({
                        url: host+'audit/includes/audit_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateSanitizationController&UserName='+$scope.asset.sanitization_controller_login_id+'&Password='+$scope.confirmDetails1.Password,
                        success: function(data){
                            if(data.Success) {
                                $scope.asset.PasswordVerified = true;
                                //$window.document.getElementById('scan_for_save').focus();
                                //$window.document.getElementById('media_out_sn0').focus();
                                $window.document.getElementById('media_out_sn0').select();
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                                $scope.asset.PasswordVerified = false;
                            }
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $scope.asset.PasswordVerified = false;
                            $rootScope.$broadcast('preloader:hide');
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });
                } else {
                    
                }

            }, function(confirmDetails1) {
                $scope.confirmDetails1 = confirmDetails1;
            });
        };

        $scope.SelectNextField = function(id) {
            $window.document.getElementById(id).select();
        };





        function MoveBinTPVRController($scope,$mdDialog,CurrentPallet,$mdToast,$window) {
            $scope.CurrentPallet = CurrentPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };
            $scope.focusNextField = function (id) {
                $window.document.getElementById(id).focus();                
            };

            $scope.MoveBinToNewLocationGroup = function (ev) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MoveBinToNewLocationGroup&'+$.param($scope.confirmDetails)+'&'+$.param($scope.CurrentPallet),
                    success: function(data) {
                        if(data.Success) {
                            // $mdToast.show (
                            //     $mdToast.simple()
                            //     .content(data.Result)
                            //     .action('OK')
                            //     .position('right')
                            //     .hideDelay(0)
                            //     .toastClass('md-toast-success md-block')
                            // );
                            
                            var message = 'Location Group Updated : Sub Location (<b>'+data.newLocationName+'</b>) from  Location Group (<b>'+data.GroupName+'</b>) has been assigned to BIN (<b>'+$scope.CurrentPallet.BinName+'</b>)';
    
                            $mdToast.show({
                                template: `
                                    <md-toast class="md-toast-success md-block">
                                        <span class="md-toast-text" flex>${message}</span>
                                        <md-button class="md-highlight" ng-click="closeToast()">OK</md-button>
                                    </md-toast>
                                `,
                                controller: function($scope, $mdToast) {
                                    $scope.closeToast = function() {
                                        $mdToast.hide();
                                    };
                                },
                                hideDelay: 0,
                                position: 'right',
                                toastClass: 'md-toast-success md-block'
                            });
                            
                            $scope.hide();
                        } else {                                                        
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
                
            };


            function LocationChange1(text) {
                $scope.confirmDetails.group = text;
            }
    
            function selectedLocationChange1(item) {
                if (item) {
                    if (item.value) {
                        $scope.confirmDetails.group = item.value;
                    } else {
                        $scope.confirmDetails.group = '';
                    }
                } else {
                    $scope.confirmDetails.group = '';
                }
            }
    
            $scope.queryLocationSearch1 = queryLocationSearch1;
            $scope.LocationChange1 = LocationChange1;
            $scope.selectedLocationChange1 = selectedLocationChange1;
            function queryLocationSearch1(query) {
                if (query) {
                    if (query != '' && query != 'undefined') {                    
                        return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&LocationType=WIP')
                            .then(function (res) {
                                if (res.data.Success == true) {
                                    if (res.data.Result.length > 0) {
                                        var result_array = [];
                                        for (var i = 0; i < res.data.Result.length; i++) {
                                            result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                        }
                                        return result_array;
                                    } else {
                                        return [];
                                    }
                                } else {
                                    return [];
                                }
                            });
                    } else {
                        return [];
                    }
                } else {
                    return [];
                }
            }


        }

        $scope.CurrentPallet = {};
        $scope.confirmDetails = {};
        function afterShowAnimation () {            
            $window.document.getElementById("AuditController").focus();            
        }

        $scope.MoveBinToStationLocationGroup = function(bin,SiteID,ev) {
            bin.SiteID = SiteID;
             //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: MoveBinTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation,
                clickOutsideToClose:true,
                resolve: {
                    CurrentPallet: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails) {  
                $scope.GetStationCustomPallets();
            }, function(confirmDetails) {
                $scope.confirmDetails = confirmDetails;
            });

        };




        //Start TPVR for Consolidation

        function ConsolidationTPVRController($scope,$mdDialog,CurrentCustomPalletPallet,$mdToast,$window) {
            $scope.CurrentCustomPalletPallet = CurrentCustomPalletPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails3);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails3);
            };
            $scope.focusNextField = function (id) {
                $window.document.getElementById(id).focus();                
            };
  
            $scope.ConsolidateBin = function (ev) {
              console.log($scope.confirmDetails4);
              console.log($scope.CurrentCustomPalletPallet);
              $rootScope.$broadcast('preloader:active');
  
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=ConsolidateBin&FromBinName='+$scope.CurrentCustomPalletPallet.BinName+'&ToBinName='+$scope.confirmDetails4.ToBinName+'&FromCustomPalletID='+$scope.CurrentCustomPalletPallet.CustomPalletID+'&ToShipmentContainer='+$scope.confirmDetails4.ToShipmentContainer,
                  success: function(data) {
                      $rootScope.$broadcast('preloader:hide');
                      if(data.Success) {
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-success md-block')
                          );      
                          $scope.hide();
                      } else {                                                        
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }                    
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      //alert(data.Result);
                      //alert("3");
                      $scope.error = data;
                      initSessionTime(); $scope.$apply();
                  }
              });
  
            };
            $scope.MoveBinToNewLocationGroup = function (ev) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MoveBinToNewLocationGroup&'+$.param($scope.confirmDetails3)+'&'+$.param($scope.CurrentPallet),
                    success: function(data) {
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                            );      
                            $scope.hide();
                        } else {                                                        
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
                
            };
          }
  
        $scope.CurrentCustomPalletPallet = {};
        $scope.confirmDetails4 = {};
        function afterShowAnimation14 () {            
          $window.document.getElementById("newbin").focus();            
        }
  
        $scope.ConsolidateBin = function(bin,SiteID,ev) {
          console.log(bin);
            bin.SiteID = SiteID;
            //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: ConsolidationTPVRController,
                templateUrl: 'password4.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation14,
                clickOutsideToClose:true,
                resolve: {
                    CurrentCustomPalletPallet: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails4) {  
                $scope.GetStationCustomPallets();
            }, function(confirmDetails4) {
                $scope.confirmDetails4 = confirmDetails4;
            });
  
        };
  
        //End TPVR for Consolidation


        $scope.GetSubComponentDisposition = function (media) {
            if(media.parttypeid > 0 && media.input_id > 0) {
                console.log('Called');
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetSubComponentDisposition&media_parttypeid='+media.parttypeid+'&media_input_id='+media.input_id+'&workflow_id=3&AssetScanID='+$scope.asset.AssetScanID+'&SiteID='+$scope.SiteID,
                    success: function(data){
                        if(data.Success) {
                           media.sub_disposition = data.sub_disposition_id;
                           media.sub_disposition_color = data.sub_color_code;
                           media.sub_disposition_id = data.sub_disposition;                           
                           media.CustomPalletID = data.Sub_CustomPalletID;
                           media.media_bin_id = data.Sub_BinName;

                           $scope.GetCurrentTime(media,'bin_scan_time');
                        } else {                            
                            media.sub_disposition = '';
                            media.sub_disposition_color = '';
                            media.sub_disposition_id = '';
                            media.CustomPalletID = '';
                            media.media_bin_id = '';
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );                            
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            }

        };
    });

})();
