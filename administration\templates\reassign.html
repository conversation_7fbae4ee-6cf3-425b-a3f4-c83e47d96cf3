
<div class="row page" data-ng-controller="ReAssign">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Re-Assign Serial</span>
                        <div flex></div>
                           <!--   <a href="#!/ReassignList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i> Back To List
                            </a> -->
                    </div>
                </md-toolbar>

                <div class="row">
                    <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">

                        <!--1st row start-->
                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block includedsearch">
                                    <label>Scan Serial Number</label>
                                    <input type="text" name="searchSerialNO" ng-model="searchSerialNO" id="searchSerialNO"  ng-maxlength="100" required ng-enter="GetAssetFromScanSN()" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" type="button" aria-label="searchSerialNO" ng-click="GetAssetFromScanSN()" ng-disabled="!searchSerialNO">
                                    <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                    </md-button>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.searchSerialNO.$error" multiple ng-if='material_signup_form.searchSerialNO.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                           <!--  {{reassigndetails.MediaSerialNumber}} -->
                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.ScanSNCB"
                                    ng-disabled ="reassign.DisplayStatusCB" ng-change="EnableSave(reassign.ScanSNCB,'NewSerialNumber')"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Scan New Serial Number</label>
                                    <input type="text" name="ScanNSN"  ng-model="reassign['ScanNSN']" ng-disabled ="!reassign.ScanSNCB" ng-maxlength="100" ng-change="GetCurrentTime(reassign,'origin_serial_scan_time');"/>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.ScanNSN.$error" multiple ng-if='material_signup_form.ScanNSN.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <a href="{{host}}tracking/#!/Tracking?SerialNO={{searchSerialNO}}"  target="_blank" class="text-success" style="display: flex;"><i class="mr-5 material-icons">open_in_new</i> <strong>Tracking</strong></a>
                            </div>

                        </div>
                        <!--1st row Close-->

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display MPN</label>
                                   <input type="text" name="DisplayMPN"  ng-model="reassigndetails['UniversalModelNumber']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayMPNCB" ng-disabled ="!reassigndetails.UniversalModelNumber || reassign.ScanSNCB || reassign.DisplayStatusCB" ng-change="EnableSave(reassign.DisplayMPNCB)"></md-checkbox>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block includedsearch">
                                    <label>MPN</label>
                                    <input name="MPN" ng-model="reassign.MPN" ng-disabled ="!reassign.DisplayMPNCB" ng-maxlength="100" ng-minlength="3" ng-enter="MPNValidate()" id="" ng-change="PartsRecovery.ValidMPN = false;GetCurrentTime(reassign,'origin_mpn_scan_time');"/>
                                    <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!reassign.MPN"  ng-click="MPNValidate();">
                                    <md-icon md-svg-src="../assets/images/search.svg" style="display:inline-block; cursor: pointer;"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>
                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display COO</label>
                                    <input type="text" name="COO" ng-model="reassigndetails['COO']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayCOOCB" ng-disabled ="reassign.ScanSNCB || reassign.DisplayStatusCB" ng-change="EnableSave(reassign.DisplayCOOCB)"></md-checkbox>
                                </md-input-container>
                            </div>



                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Select New COO</label>
                                        <md-select name="COOID" ng-model="reassign.COOID" ng-disabled ="!reassign.DisplayCOOCB" aria-label="select" ng-change="GetCurrentTime(reassign,'origin_coo_scan_time');">
                                            <md-option ng-repeat="coo in COOList" value="{{coo.COOID}}"> {{coo.COO}} </md-option>
                                        </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.COOID.$error" multiple ng-if='material_signup_form.COOID.$dirty'>

                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Part Type</label>
                                    <input type="text" name="parttype"  ng-model="reassigndetails['parttype']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayPartTypeCB" ng-disabled ="reassign.ScanSNCB || reassign.DisplayStatusCB" ng-change="EnableSave(reassign.DisplayPartTypeCB)"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Select New Part Type</label>
                                        <md-select name="parttypeid" ng-model="reassign.parttypeid" ng-disabled ="!reassign.DisplayPartTypeCB" aria-label="select" ng-change="GetCurrentTime(reassign,'origin_parttype_scan_time');">
                                            <md-option ng-repeat="parttype in PartTypelist" value="{{parttype.parttypeid}}"> {{parttype.parttype}} </md-option>
                                        </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.parttypeid.$error" multiple ng-if='material_signup_form.parttypeid.$dirty'>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Status</label>
                                    <input type="text" name="StatusName"  ng-model="reassigndetails['Status']" data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayStatusCB" ng-disabled ="!reassigndetails.Status || reassign.ScanSNCB" ng-change="EnableSave(reassign.DisplayStatusCB,'DisplayStatusCB')"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3"> 
                                <md-input-container class="md-block">  
                                    <label>Select New Status</label>
                                    <md-select name="StatusID" ng-model="reassign.StatusID" ng-disabled="!reassign.DisplayStatusCB" aria-label="select">
                                        <md-option value="1">Active</md-option>
                                        <md-option value="13">Inactive</md-option>       
                                    </md-select>   
                                    <div ng-messages="material_signup_form.StatusID.$error" multiple ng-if="material_signup_form.StatusID.$dirty">
                                    </div>                                             
                                </md-input-container>
                            </div>
                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Facility</label>
                                    <input type="text" name="FacilityName"  ng-model="reassigndetails['FacilityName']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayFacilityCB" ng-disabled ="!reassigndetails.FacilityName || reassign.ScanSNCB || reassign.DisplayStatusCB" ng-change="EnableSave(reassign.DisplayFacilityCB)"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Select New Facility</label>
                                    <md-select name="FacilityID" ng-model="reassign.FacilityID" ng-disabled ="!reassign.DisplayFacilityCB" aria-label="select">
                                        <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Disposition</label>
                                    <input type="text" name="disposition"  ng-model="reassigndetails['disposition']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayDispositionCB" ng-disabled ="!reassigndetails.disposition || reassign.ScanSNCB || reassign.DisplayStatusCB" ng-change="EnableSave(reassign.DisplayDispositionCB)"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Select New Disposition</label>
                                        <md-select name="disposition_id" ng-model="reassign.DispositionID" ng-disabled ="!reassign.DisplayDispositionCB" aria-label="select" ng-change="GetDispositionOverride(reassigndetails.disposition_id,reassign.DispositionID);GetCurrentTime(reassign,'origin_disposition_scan_time');">
                                            <md-option ng-repeat="dis_position in disposition" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}}</md-option>
                                        </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.DispositionID.$error" multiple ng-if='material_signup_form.DispositionID.$dirty'>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-switch class="text-success" ng-model="reassign.DispositionOverrideEligibility" ng-disabled = "!reassign.ScanSNCB || reassign.DisplayStatusCB"><strong> Disposition Override Eligibility</strong></md-switch>
                            </div>

                        </div>

                        <div class="col-md-12">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Reason for Override</label>
                                     <md-select name="OverrideReason" ng-model="reassign.DispositionOverrideReason" aria-label="select" ng-disabled ="!reassign.DispositionOverrideEligibility" ng-change="GetOverrideReason(reassign.DispositionOverrideReason)">
                                          <md-option ng-repeat="Override_Reason in OverrideReasonCode" value="{{Override_Reason.OverrideReason}}"> {{Override_Reason.OverrideReason}}</md-option>
                                      </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.DispositionOverrideReason.$error" multiple ng-if='material_signup_form.DispositionOverrideReason.$dirty'>
                                        </div>
                                    </div>
                                    <span ng-show="reassign.DispositionOverrideEligibility" ng-if="reassign.DispositionOverrideReason <=0" style="color: red;">
                                          Reason for Override are required!
                                      </span>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <!-- <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayDispositionCB"></md-checkbox>
                                </md-input-container> -->
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Scan New Bin ID</label>
                                    <!-- (!reassign.FacilityID && !reassign.DispositionID && !reassign.StatusID) || !reassign.DispositionOverrideEligibility -->
                                    <input type="text" name="DispositionBin" ng-model="reassign['DispositionBin']" ng-disabled ="!isBinRequired" ng-required="isBinRequired && reassign.StatusID == '1'"
                                    ng-change="GetCurrentTime(reassign,'origin_bin_scan_time');"/>

                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.DispositionBin.$error" multiple ng-if='material_signup_form.DispositionBin.$dirty'>
                                            <!-- <div ng-message="required">This is required.</div> -->
                                        </div>
                                    </div>
                                    <span ng-show="reassign.StatusID == '1' || reassign.DispositionOverrideReason" style="color: red;">
                                          Bin required!
                                      </span>
                                </md-input-container>
                            </div>

                        </div>


                        <div class="col-md-12">
                            <div class="col-md-8">
                                <md-input-container class="md-block">
                                    <label>Notes</label>
                                    <input type="text" name="NotesRequired"  ng-model="reassign['NotesRequired']" ng-disabled ="!reassign.DispositionOverrideReason || !isNotesRequired" ng-required="isNotesRequired"/>
                                    <div class="error-space">
                                      <span ng-show="isNotesRequired && !reassign.Notes" style="color: red;">
                                          Notes are required!
                                      </span>
                                    </div>
                                </md-input-container>
                            </div>
                        </div>




                        <div class="col-md-12">
                            <div class="col-md-12 btns-row">
                                <md-button class="md-button md-raised btn-w-md  md-default" ng-click="CancelReassign()">
                                    Cancel
                                </md-button>
                            </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || reassign.busy || disableSave" ng-click="CreateReassign()">
                                <span ng-show="! reassign.busy">Save</span>
                                <span ng-show="reassign.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                                <a href="{{host}}label/master/examples/ReassignSerialNumber.php?id={{searchSerialNO}}" target="_blank">
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                    data-ng-disabled="material_signup_form.$invalid || reassign.busy" >
                                    <span ng-show="! reassign.busy">Print</span>
                                    <span ng-show="reassign.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                    </md-button>
                                </a>
                            </div>
                        </div>

                    </form>
                </div>
            </md-card>
            <md-card class="no-margin-h" ng-show = "lastrecord !='' ">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Last Transaction:</span>
                    </div>
                </md-toolbar>
                <div class="col-md-12">
                 <table class="table">
                        <thead>
                            <tr>
                                <th>SN</th>
                                <th>MPN</th>
                                <th>COO</th>
                                <th>Part Type</th>
                                <th>Disposition</th>
                                <th>Facility</th>
                                <th>Bin Name</th>
                                <th>Status</th>
                                <th>Print</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{lastrecord.SerialNumber}}</td>
                                <td>{{lastrecord.UniversalModelNumber}}</td>
                                <td>{{lastrecord.COO}}</td>
                                <td>{{lastrecord.parttype}}</td>
                                <td>{{lastrecord.disposition}}</td>
                                <td>{{lastrecord.FacilityName}}</td>
                                <td>{{lastrecord.BinName}}</td>
                                <td>{{lastrecord.Status}}</td>
                                <td md-cell class="actionicons" style="min-width: 60px;" ng-show = "lastrecord !='' ">
                                    <a href="{{host}}label/master/examples/ReassignSerialNumber.php?id={{lastrecord.SerialNumber}}" target="_blank">
                                    <i class="material-icons print" role="img" aria-label="print">print</i>
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </md-card>
        </article>
    </div>
</div>
