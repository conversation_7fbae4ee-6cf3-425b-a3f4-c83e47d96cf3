
<div class="row page" data-ng-controller="bin">
    <style type="text/css">
        .autocomplete md-autocomplete .md-whiteframe-z1{ border-bottom:none !important;}
        .autocomplete {padding: 0px 0px 0px 0px !important;}
    </style>
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Bin</span>
                        <div flex></div>
                            <a href="#!/BinList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i>Back to Bin List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                
                                    <label>Facility</label>   
                                    <md-select name="FacilityID" ng-model="cpalletForm.FacilityID" required aria-label="select" ng-disabled="cpalletForm.CustomPalletID" ng-change="GetBinTypes()">
                                        <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                    
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>    
                                    </div> 
                                </md-input-container>
                            </div>    
                            <!-- <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Bin Type</label>                                       
                                    <md-select name="BinTypeID" id="BinTypeID" ng-model="cpalletForm.BinTypeID" aria-label="select" ng-disabled="cpalletForm.CustomPalletID" required > 
                                        <md-option ng-repeat="bt in BinTypes" value="{{bt.BinTypeID}}"> {{bt.BinType}} </md-option>                                    
                                    </md-select>
                                </md-input-container>
                            </div>                        -->
                            <div class="col-md-3" ng-if="cpalletForm.LocationName == '' || cpalletForm.LocationName == NULL">                                        
                                <!-- <span ng-show="cpalletForm.LocationName != ''">{{cpalletForm.LocationName}}</span> -->                        
                                <div class="autocomplete insideuse" ng-show="cpalletForm.LocationName == '' || cpalletForm.LocationName == NULL">
                                    <md-input-container class="md-block">                                    
                                        <md-autocomplete flex  required style="margin-bottom:0px !important; margin-top:0px !important; padding-top: 0px !important;"
                                            md-input-name="location"                                        
                                            md-input-maxlength="100" 
                                            ng-disabled="cpalletForm.FacilityID == 0 || !cpalletForm.FacilityID"
                                            md-no-cache="noCache"    
                                            md-search-text-change="LocationChange(cpalletForm.group)"      
                                            md-search-text="cpalletForm.group"                                  
                                            md-items="item in queryLocationSearch(cpalletForm.group)"
                                            md-item-text="item.GroupName"
                                            md-selected-item-change="selectedLocationChange(item)"
                                            md-min-length="0"
                                            ng-model-options='{ debounce: 1000 }'
                                            placeholder="Search Location Group">
                                            <md-item-template>
                                                <span md-highlight-text="cpalletForm.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                            </md-item-template>
                                            <md-not-found>
                                                No Records matching "{{cpalletForm.group}}" were found.                                    
                                            </md-not-found>
                                            <div ng-messages="material_signup_form.group.$error" ng-if="material_signup_form.group.$touched">
                                                <div ng-message="required">No Records matching.</div>                                            
                                            </div>
                                        </md-autocomplete>
                                    </md-input-container>
                                    
                                </div>                                            
                            </div>

                            <div class="col-md-3" ng-show="cpalletForm.LocationName != '' && cpalletForm.LocationName">
                                <md-input-container class="md-block">
                                    <label>Location Name</label>
                                    <input type="text" name="LocationName"  ng-model="cpalletForm['LocationName']" data-ng-disabled="true"  />
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>

                            <!--<div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Location Type</label>
                                    <md-select name="LocationType" ng-model="cpalletForm.LocationType" required aria-label="select">
                                        <md-option value="Physical"> Physical </md-option>
                                        <md-option value="Logical"> Logical </md-option>
                                        <md-option value="Physical/Logical"> Physical/Logical </md-option>
                                    </md-select>   
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.LocationType.$error" multiple ng-if='material_signup_form.LocationType.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>-->
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Room</label>
                                    <md-select name="Room" ng-model="cpalletForm.Room" required aria-label="select">
                                        <md-option value="PARTS"> PARTS </md-option>
                                        <md-option value="PRESTAGE_AREA"> PRESTAGE_AREA </md-option>
                                        <md-option value="TOA"> TOA </md-option>

                                    </md-select>   
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.Room.$error" multiple ng-if='material_signup_form.Room.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div>    
                                    </div>                                         
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Serialization</label>
                                    <md-select name="Serialization" ng-model="cpalletForm.Serialization" required aria-label="select">
                                        <md-option value="Yes"> Yes </md-option>
                                        <md-option value="No"> No </md-option>
                                    </md-select> 
                                    <div class="error-space">  
                                        <div ng-messages="material_signup_form.Serialization.$error" multiple ng-if='material_signup_form.Serialization.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div>
                                    </div>                                             
                                </md-input-container>
                            </div>
                           <!--  <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Workflow</label>   
                                    <md-select name="workflow_id" ng-model="cpalletForm.workflow_id" required aria-label="select" ng-change="GetWorkflow()">
                                        <md-option ng-repeat="work_flow in workflow" value="{{work_flow.workflow_id}}"> {{work_flow.workflow}} </md-option>
                                    
                                    </md-select>  
                                    <div class="error-space"> 
                                        <div ng-messages="material_signup_form.workflow_id.$error" multiple ng-if='material_signup_form.workflow_id.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>  
                                    </div>
                                </md-input-container>
                            </div> -->
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Phycical / Logical</label>
                                    <md-select name="BinType" ng-model="cpalletForm.BinType" required aria-label="select">
                                        <md-option value="Physical"> Physical </md-option>
                                        <md-option value="Logical"> Logical </md-option>
                                    </md-select>   
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.BinType.$error" multiple ng-if='material_signup_form.BinType.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div>   
                                    </div>                                          
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="cpalletForm.AcceptAllDisposition" aria-label="AcceptAllDisposition" ng-true-value="'1'" ng-false-value="'0'" class="md-primary" ng-disabled="cpalletForm.CustomPalletID"> Accept All Disposition </md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-6" ng-if="cpalletForm.AcceptAllDisposition == 0">
                                <md-input-container class="md-block">
                                    <label>Disposition</label>   
                                    <md-select name="disposition_id" ng-model="cpalletForm.disposition_id" aria-label="select" ng-change="GetDisposition()" ng-disabled="cpalletForm.CustomPalletID">
                                        <md-option ng-repeat="dis_position in disposition" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}} <span style="color:red;" ng-show="dis_position.sub_disposition > 0">(Sub Disposition)</span> </md-option>
                                    </md-select>
                                    <div class="error-space">
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="Description"  ng-model="cpalletForm['Description']"  required ng-maxlength="250" />
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.Description.$error" multiple ng-if='material_signup_form.Description.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 250.</div>                           
                                        </div>
                                    </div>
                                </md-input-container>

                            </div>
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Bin Name</label>
                                    <input name="BinName" ng-model="cpalletForm.BinName" value="" required />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.BinName.$error" multiple ng-if='material_signup_form.BinName.$dirty'>
                                            <div ng-message="required">Bin Name is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-6" ng-if="! cpalletForm.CustomPalletID">
                                <md-input-container class="md-block">
                                    <label>Parent Bin</label>
                                    <md-autocomplete
                                        ng-disabled="false"
                                        md-no-cache="false"
                                        md-selected-item="selectedParentBin"
                                        md-search-text-change="parentBinChange(parentBinSearchText)"
                                        md-search-text="parentBinSearchText"
                                        md-selected-item-change="selectedParentBinChange(item)"
                                        md-items="item in queryParentBinSearch(parentBinSearchText)"
                                        md-item-text="item.BinName"
                                        md-min-length="0"
                                        placeholder="Search Parent Bin">
                                        <md-item-template>
                                            <span md-highlight-text="parentBinSearchText" md-highlight-flags="^i">{{item.BinName}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            No bins found matching "{{parentBinSearchText}}".
                                        </md-not-found>
                                    </md-autocomplete>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3" >
                                <md-input-container class="md-block">
                                    <label>Maximum Assets</label>
                                    <input type="number"required name="MaximumAssets"  ng-model="cpalletForm['MaximumAssets']" ng-min="1" ng-max="10000" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.MaximumAssets.$error" multiple ng-if='material_signup_form.MaximumAssets.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="min">Minimum 1.</div>
                                            <div ng-message="max">Maximul 10000.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Status</label>   
                                    <md-select name="StatusID" ng-model="cpalletForm.StatusID" required aria-label="select" ng-change="GetStatus()">
                                        <md-option ng-repeat="sta_tus in status" value="{{sta_tus.StatusID}}"> {{sta_tus.Status}} </md-option>
                                    
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.StatusID.$error" multiple ng-if='material_signup_form.StatusID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>     
                                    </div>
                                </md-input-container>
                            </div>  
                            <!-- <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Sub Component Based</label>
                                    <md-select name="InventoryBased" ng-model="cpalletForm.InventoryBased" required aria-label="select" ng-disabled="cpalletForm.CustomPalletID">
                                        <md-option value="1"> Yes </md-option>
                                        <md-option value="0"> No </md-option>
                                    </md-select>  
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.InventoryBased.$error" multiple ng-if='material_signup_form.InventoryBased.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div>
                                    </div>
                                </md-input-container>
                            </div> -->
                            


                            <div class="col-md-3" ng-if=" cpalletForm.CustomPalletID">
                                <md-input-container class="md-block">
                                    <label>Mobility Name</label>
                                    <input type="text" name="MobilityName"  ng-model="cpalletForm['MobilityName']"  ng-maxlength="100" />
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>
                            
                            <div class="col-md-12 btns-row">
                                <a href="#!/BinList" style="text-decoration: none;">
                                <md-button class="md-button md-raised btn-w-md  md-default">
                                    Cancel
                                </md-button>
                            </a>
                               <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || cpalletForm.busy" ng-click="savebincreation()">
                                <span ng-show="! cpalletForm.busy">Save</span>
                                <span ng-show="cpalletForm.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>