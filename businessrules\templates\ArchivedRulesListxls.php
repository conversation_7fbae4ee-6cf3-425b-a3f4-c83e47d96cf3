<?php
session_start();
include_once("../../config.php");
$data = $_SESSION['ArchivedRuleListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "ArchivedBusinessRulesList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$datatoday = array('Generated Date',$today);
$datahead = array('Archived Business Rules List');
// Header for archived rules: Version Name, Priority, Rule Name, Description, Customer, Facility, Workflow, Part Type, Rule Summary, Disposition, Rule ID, Source Type, Material Type, Created Date, Created By, Updated Date, Updated By
$header = array('Version Name','Priority','Rule Name','Description','Customer','Facility','Workflow','Part Type','Rule Summary','Disposition','Rule ID','Source Type','Material Type','Created Date','Created By','Updated Date','Updated By');

// Query for archived rules only
$query = "SELECT r.*, f.FacilityName, d.disposition, v.version_name, v.current_version, v.status as VersionStatus,
          w.workflow, ct.Cumstomertype as SourceTypeName, mt.MaterialType as MaterialTypeName,
          cu.FirstName as CreatedByFirstName, cu.LastName as CreatedByLastName,
          uu.FirstName as UpdatedByFirstName, uu.LastName as UpdatedByLastName
          FROM business_rule r
          LEFT JOIN facility f ON r.FacilityID = f.FacilityID
          LEFT JOIN disposition d ON d.disposition_id = r.disposition_id
          LEFT JOIN business_rule_versions v ON r.version_id = v.version_id
          LEFT JOIN workflow w ON r.workflow_id = w.workflow_id
          LEFT JOIN customertype ct ON r.idCustomertype = ct.idCustomertype
          LEFT JOIN material_types mt ON r.MaterialType = mt.MaterialTypeID
          LEFT JOIN users cu ON r.created_by = cu.UserId
          LEFT JOIN users uu ON r.updated_by = uu.UserId
          WHERE r.status = 'Archived'";

// Apply filters if provided
if($data[0] && count($data[0]) > 0) {
    foreach ($data[0] as $key => $value) {
        if($value != '') {
            if($key == 'rule_name') {
                $query = $query . " AND r.rule_name like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
            }
            if($key == 'rule_description') {
                $query = $query . " AND r.rule_description like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
            }
            if($key == 'priority') {
                $query = $query . " AND r.priority like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
            }
            if($key == 'FacilityName') {
                if(strtolower($value) == 'all') {
                    $query = $query . " AND (f.FacilityName LIKE '%".mysqli_real_escape_string($connectionlink, $value)."%' OR r.FacilityID = 'all' OR r.FacilityID = 'All') ";
                } else {
                    $query = $query . " AND (f.FacilityName LIKE '%".mysqli_real_escape_string($connectionlink, $value)."%' OR r.FacilityID = 'all' OR r.FacilityID = 'All') ";
                }
            }
            if($key == 'disposition') {
                $query = $query . " AND d.disposition like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
            }
            if($key == 'rule_summary') {
                $query = $query . " AND r.rule_summary like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
            }
            if($key == 'version_name') {
                $query = $query . " AND v.version_name like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
            }
            if($key == 'workflow') {
                if(strtolower($value) == 'all') {
                    $query = $query . " AND (w.workflow LIKE '".mysqli_real_escape_string($connectionlink, $value)."%' OR r.workflow_id = 'all' OR r.workflow_id = 'All') ";
                } else {
                    $query = $query . " AND (w.workflow LIKE '".mysqli_real_escape_string($connectionlink, $value)."%' OR r.workflow_id = 'all' OR r.workflow_id = 'All') ";
                }
            }
            if($key == 'part_types') {
                if(strtolower($value) == 'all') {
                    $query = $query . " AND (r.part_types = 'all' OR r.part_types = 'All') ";
                } else {
                    $query = $query . " AND (r.part_types LIKE '%".mysqli_real_escape_string($connectionlink, $value)."%' OR r.part_types = 'all' OR r.part_types = 'All') ";
                }
            }
            if($key == 'SourceTypeName') {
                if(strtolower($value) == 'all') {
                    $query = $query . " AND (ct.Cumstomertype LIKE '%".mysqli_real_escape_string($connectionlink, $value)."%' OR r.idCustomertype = 'all' OR r.idCustomertype = 'All') ";
                } else {
                    $query = $query . " AND (ct.Cumstomertype LIKE '%".mysqli_real_escape_string($connectionlink, $value)."%' OR r.idCustomertype = 'all' OR r.idCustomertype = 'All') ";
                }
            }
            if($key == 'MaterialTypeName') {
                if(strtolower($value) == 'all') {
                    $query = $query . " AND (r.MaterialType = 'all' OR r.MaterialType = 'All') ";
                } else {
                    $query = $query . " AND (r.MaterialType LIKE '%".mysqli_real_escape_string($connectionlink, $value)."%' OR r.MaterialType = 'all' OR r.MaterialType = 'All') ";
                }
            }
            if($key == 'CustomerName') {
                if(strtolower($value) == 'all') {
                    $query = $query . " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All') ";
                } else {
                    // Handle Customer filtering for both single and comma-separated Customer IDs
                    // First, get Customer IDs that match the customer name
                    $customerIdQuery = "SELECT GROUP_CONCAT(AWSCustomerID) as customer_ids FROM aws_customers WHERE Customer LIKE '%".mysqli_real_escape_string($connectionlink,$value)."%'";
                    $customerIdResult = mysqli_query($connectionlink, $customerIdQuery);
                    $customerIds = '';
                    if($customerIdResult && mysqli_num_rows($customerIdResult) > 0) {
                        $customerIdRow = mysqli_fetch_assoc($customerIdResult);
                        $customerIds = $customerIdRow['customer_ids'];
                    }

                    if(!empty($customerIds)) {
                        $customerIdArray = explode(',', $customerIds);
                        $customerConditions = array();
                        $customerConditions[] = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";

                        foreach($customerIdArray as $customerId) {
                            $customerId = trim($customerId);
                            if(!empty($customerId)) {
                                $customerConditions[] = "r.AWSCustomerID = '".mysqli_real_escape_string($connectionlink, $customerId)."'";
                                $customerConditions[] = "FIND_IN_SET('".mysqli_real_escape_string($connectionlink, $customerId)."', r.AWSCustomerID) > 0";
                            }
                        }
                        $query = $query . " AND (" . implode(' OR ', $customerConditions) . ") ";
                    } else {
                        // No matching customer found, only show global rules
                        $query = $query . " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All') ";
                    }
                }
            }
            if($key == 'version_name') {
                $query = $query . " AND v.version_name like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
            }
            if($key == 'rule_id_text') {
                $query = $query . " AND r.rule_id like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
            }
            if($key == 'created_date') {
                $query = $query . " AND DATE(r.created_date) like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
            }
            if($key == 'created_by') {
                $query = $query . " AND (CONCAT(cu.FirstName, ' ', cu.LastName) like '%".mysqli_real_escape_string($connectionlink,$value)."%' OR cu.FirstName like '%".mysqli_real_escape_string($connectionlink,$value)."%' OR cu.LastName like '%".mysqli_real_escape_string($connectionlink,$value)."%') ";
            }
            if($key == 'updated_date') {
                $query = $query . " AND DATE(r.updated_date) like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
            }
            if($key == 'updated_by') {
                $query = $query . " AND (CONCAT(uu.FirstName, ' ', uu.LastName) like '%".mysqli_real_escape_string($connectionlink,$value)."%' OR uu.FirstName like '%".mysqli_real_escape_string($connectionlink,$value)."%' OR uu.LastName like '%".mysqli_real_escape_string($connectionlink,$value)."%') ";
            }
        }
    }
}

// Apply sorting
if($data['OrderBy'] != '') {
    if($data['OrderByType'] == 'asc') {
        $order_by_type = 'asc';
    } else {
        $order_by_type = 'desc';
    }

    if($data['OrderBy'] == 'CustomerName') {
        $query = $query . " order by r.AWSCustomerID ".$order_by_type." ";
    } else if($data['OrderBy'] == 'FacilityName') {
        $query = $query . " order by f.FacilityName ".$order_by_type." ";
    } else if($data['OrderBy'] == 'workflow') {
        $query = $query . " order by w.workflow ".$order_by_type." ";
    } else if($data['OrderBy'] == 'part_types') {
        $query = $query . " order by r.part_types ".$order_by_type." ";
    } else if($data['OrderBy'] == 'rule_name') {
        $query = $query . " order by r.rule_name ".$order_by_type." ";
    } else if($data['OrderBy'] == 'rule_description') {
        $query = $query . " order by r.rule_description ".$order_by_type." ";
    } else if($data['OrderBy'] == 'priority') {
        $query = $query . " order by r.priority ".$order_by_type." ";
    } else if($data['OrderBy'] == 'disposition') {
        $query = $query . " order by d.disposition ".$order_by_type." ";
    } else if($data['OrderBy'] == 'rule_summary') {
        $query = $query . " order by r.rule_summary ".$order_by_type." ";
    } else if($data['OrderBy'] == 'SourceTypeName') {
        $query = $query . " order by ct.Cumstomertype ".$order_by_type." ";
    } else if($data['OrderBy'] == 'MaterialTypeName') {
        $query = $query . " order by mt.MaterialType ".$order_by_type." ";
    } else if($data['OrderBy'] == 'version_name') {
        $query = $query . " order by v.version_name ".$order_by_type." ";
    } else if($data['OrderBy'] == 'rule_id') {
        $query = $query . " order by r.rule_id ".$order_by_type." ";
    } else if($data['OrderBy'] == 'created_date') {
        $query = $query . " order by r.created_date ".$order_by_type." ";
    } else if($data['OrderBy'] == 'created_by') {
        $query = $query . " order by CONCAT(cu.FirstName, ' ', cu.LastName) ".$order_by_type." ";
    } else if($data['OrderBy'] == 'updated_date') {
        $query = $query . " order by r.updated_date ".$order_by_type." ";
    } else if($data['OrderBy'] == 'updated_by') {
        $query = $query . " order by CONCAT(uu.FirstName, ' ', uu.LastName) ".$order_by_type." ";
    }
} else {
    // Default ordering: by created date descending
    $query = $query . " order by r.created_date DESC ";
}

$sql = mysqli_query($connectionlink,$query);
if(mysqli_error($connectionlink)) {
    echo mysqli_error($connectionlink);
}

while($row = mysqli_fetch_assoc($sql))
{
    // Handle "All" values and comma-separated Customer IDs for display
    if($row['AWSCustomerID'] == 'all' || $row['AWSCustomerID'] == 'All') {
        $customer = 'All';
    } else if(strpos($row['AWSCustomerID'], ',') !== false) {
        // Handle comma-separated Customer IDs - convert to customer names
        $customerIds = explode(',', $row['AWSCustomerID']);
        $customerNames = array();
        foreach($customerIds as $customerId) {
            $customerId = trim($customerId);
            if(!empty($customerId)) {
                // Query to get customer name for each ID
                $customerQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($connectionlink, $customerId)."'";
                $customerResult = mysqli_query($connectionlink, $customerQuery);
                if($customerResult && mysqli_num_rows($customerResult) > 0) {
                    $customerRow = mysqli_fetch_assoc($customerResult);
                    $customerNames[] = $customerRow['Customer'];
                } else {
                    $customerNames[] = $customerId; // Fallback to ID if name not found
                }
            }
        }
        $customer = implode(', ', $customerNames);
    } else {
        // Single Customer ID - need to get customer name
        if($row['AWSCustomerID'] && $row['AWSCustomerID'] != '0') {
            $customerQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($connectionlink, $row['AWSCustomerID'])."'";
            $customerResult = mysqli_query($connectionlink, $customerQuery);
            if($customerResult && mysqli_num_rows($customerResult) > 0) {
                $customerRow = mysqli_fetch_assoc($customerResult);
                $customer = $customerRow['Customer'];
            } else {
                $customer = $row['AWSCustomerID']; // Fallback to ID if name not found
            }
        } else {
            $customer = $row['AWSCustomerID'];
        }
    }
    
    $facility = ($row['FacilityID'] == 'all' || $row['FacilityID'] == 'All') ? 'All' : $row['FacilityName'];
    $workflow = ($row['workflow_id'] == 'all' || $row['workflow_id'] == 'All') ? 'All' : $row['workflow'];
    $sourceType = ($row['idCustomertype'] == 'all' || $row['idCustomertype'] == 'All') ? 'All' : $row['SourceTypeName'];
    $materialType = ($row['MaterialType'] == 'all' || $row['MaterialType'] == 'All') ? 'All' : $row['MaterialTypeName'];

    // Format user names
    $createdBy = '';
    if($row['CreatedByFirstName'] && $row['CreatedByLastName']) {
        $createdBy = $row['CreatedByFirstName'] . ' ' . $row['CreatedByLastName'];
    } else {
        $createdBy = $row['created_by'] ?: '';
    }

    $updatedBy = '';
    if($row['UpdatedByFirstName'] && $row['UpdatedByLastName']) {
        $updatedBy = $row['UpdatedByFirstName'] . ' ' . $row['UpdatedByLastName'];
    } else {
        $updatedBy = $row['updated_by'] ?: '';
    }

    // Format dates
    $createdDate = $row['created_date'] ? date('m/d/Y H:i', strtotime($row['created_date'])) : '';
    $updatedDate = $row['updated_date'] ? date('m/d/Y H:i', strtotime($row['updated_date'])) : '';

    // Row order: Version Name, Priority, Rule Name, Description, Customer, Facility, Workflow, Part Type, Rule Summary, Disposition, Rule ID, Source Type, Material Type, Created Date, Created By, Updated Date, Updated By
    $row2  = array($row['version_name'],$row['priority'],$row['rule_name'],$row['rule_description'],$customer,$facility,$workflow,$row['part_types'],$row['rule_summary'],$row['disposition'],$row['rule_id'],$sourceType,$materialType,$createdDate,$createdBy,$updatedDate,$updatedBy);
    $rows[] = $row2;
}

$sheet_name = 'ArchivedBusinessRules';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?>
