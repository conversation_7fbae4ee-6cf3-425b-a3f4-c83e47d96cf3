
<div class="row page" data-ng-controller="ReDisposition">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Re-Disposition Serial</span>
                        <div flex></div>
                            <!-- <a href="#!/RedispositionList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i> Back To List
                            </a> -->
                    </div>
                </md-toolbar>

                <div class="row">
                    <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">

                        <!--1st row start-->
                        <div class="col-md-12">
                            <div class="col-md-3">
                                <md-input-container class="md-block includedsearch">
                                    <label>Scan Serial Number</label>
                                    <input type="text" name="searchSerialNO" ng-model="searchSerialNO" id="searchSerialNO"  ng-maxlength="100" ng-enter="GetAssetFromScanSN()" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" type="button" aria-label="searchSerialNO" ng-click="GetAssetFromScanSN();" ng-disabled="!searchSerialNO">
                                    <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                    </md-button>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.searchSerialNO.$error" multiple ng-if='material_signup_form.searchSerialNO.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center"></div>

                            <div class="col-md-4">
                                <a href="{{host}}/tracking/#!/Tracking?SerialNO={{searchSerialNO}}" target="_blank" ng-click="GetSNType()" class="text-success" style="display: flex;"><i class="mr-5 material-icons">open_in_new</i> <strong>Tracking</strong></a>
                            </div>

                        </div>
                        <!--1st row Close-->

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display MPN</label>
                                   <input type="text" name="DisplayMPN"  ng-model="reassigndetails['UniversalModelNumber']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayMPNCB" ng-disabled ="!reassigndetails.UniversalModelNumber" ng-change="EnableSave(reassign.DisplayMPNCB,'UniversalModelNumber')"></md-checkbox>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block includedsearch">
                                    <label>Scan New MPN</label>
                                    <input name="MPN" ng-model="reassign.MPN" ng-disabled ="!reassign.DisplayMPNCB" ng-maxlength="100" ng-minlength="3" ng-enter="MPNValidate()" id="" ng-change="PartsRecovery.ValidMPN = false;GetCurrentTime(reassign,'origin_mpn_scan_time');"/>
                                    <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!reassign.MPN"  ng-click="MPNValidate();">
                                    <md-icon md-svg-src="../assets/images/search.svg" style="display:inline-block; cursor: pointer;"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>
                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Part Type</label>
                                    <input type="text" name="parttype"  ng-model="reassigndetails['parttype']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayPartTypeCB" ng-disabled ="!reassigndetails.parttype" ng-change="EnableSave(reassign.DisplayPartTypeCB,'PartType')"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Select New Part Type</label>
                                        <md-select name="parttypeid" ng-model="reassign.parttypeid" aria-label="select" ng-disabled ="!reassign.DisplayPartTypeCB" ng-change="GetCurrentTime(reassign,'origin_parttype_scan_time');">
                                            <md-option ng-repeat="parttype in PartTypelist" value="{{parttype.parttypeid}}"> {{parttype.parttype}} </md-option>
                                        </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.parttypeid.$error" multiple ng-if='material_signup_form.parttypeid.$dirty'>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display COO</label>
                                    <input type="text" name="COO"  ng-model="reassigndetails['COO']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                             <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayCOOCB" ng-disabled ="!reassigndetails.COO" ng-change="EnableSave(reassign.DisplayCOOCB,'COO')"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Select New COO</label>
                                        <md-select name="COOID" ng-model="reassign.COOID" aria-label="select" ng-disabled ="!reassign.DisplayCOOCB" ng-change="GetCurrentTime(reassign,'origin_coo_scan_time');">
                                            <md-option ng-repeat="coo in COOList" value="{{coo.COOID}}"> {{coo.COO}} </md-option>
                                        </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.COOID.$error" multiple ng-if='material_signup_form.COOID.$dirty'>

                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                        </div>


                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Last Evaluation Result</label>
                                    <input type="text" name="input"  ng-model="reassigndetails['input']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayLastEvalutionResultCB" ng-disabled ="!reassigndetails.input" ng-change="EnableSave(reassign.DisplayLastEvalutionResultCB,'EvaluationResult')"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Select New Evaluation Result</label>
                                        <md-select name="EvaluationResultID" ng-model="reassign.EvaluationResultID" aria-label="select" ng-disabled ="!reassign.DisplayLastEvalutionResultCB" ng-change="GetDispositionByBRE();GetCurrentTime(reassign,'origin_evaluation_scan_time');">">
                                            <md-option ng-repeat="NewEvaluationResult in EvaluationResult" value="{{NewEvaluationResult.input_id}}"> {{NewEvaluationResult.input}} </md-option>
                                        </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.EvaluationResultID.$error" multiple ng-if='material_signup_form.EvaluationResultID.$dirty'>

                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Current Disposition</label>
                                    <input type="text" name="disposition"  ng-model="reassigndetails['disposition']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayDispositionCB" ng-disabled ="!reassigndetails.disposition" ng-change="EnableSave(reassign.DisplayDispositionCB,'Disposition')"></md-checkbox>
                                </md-input-container>
                            </div>

                            <!-- <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Latest Disposition</label>
                                    <input type="text" name="disposition_id"  ng-model="reassign['DispositionID']" ng-disabled ="!reassign.DisplayDispositionCB"/>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div> -->

                            <div class="col-md-3">
                                <!--<md-input-container class="md-block">
                                    <label>Select Latest Disposition</label>
                                         <md-select name="disposition_id" ng-model="reassign.DispositionID" aria-label="select" ng-disabled ="!reassign.DisplayDispositionCB">
                                            <md-option ng-repeat="dis_position in disposition" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}}</md-option>
                                        </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.DispositionID.$error" multiple ng-if='material_signup_form.DispositionID.$dirty'>
                                        </div>
                                    </div>
                                </md-input-container>-->
                                <md-input-container class="md-block">
                                    <label>Latest Disposition</label>
                                    <input type="text" name="Disposition" ng-model="reassign.Disposition"  data-ng-disabled="true" ng-change="GetCurrentTime(reassign,'origin_disposition_scan_time');">
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>


                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Last Workflow</label>
                                    <input type="text" name="workflow"  ng-model="reassigndetails['workflow']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <!-- <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayLastWorkflowCB"></md-checkbox>
                                </md-input-container> -->
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Scan New Bin ID</label>
                                    <input type="text" name="DispositionBin" ng-model="reassign['DispositionBin']" ng-disabled ="!reassign.Changethebin && !reassign.Disposition" ng-required="isBinRequired" ng-change="GetCurrentTime(reassign,'origin_bin_scan_time');"/>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.DispositionBin.$error" multiple ng-if='material_signup_form.DispositionBin.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                        </div>

                        <div class="col-md-12">
                            <div class="col-md-12">
                                <md-switch class="text-success" ng-model="reassign.Changethebin" ng-disabled="!reassign.DisplayDispositionCB"><strong> Change the bin if disposition is same?</strong></md-switch>
                            </div>
                        </div>


                        <div class="col-md-12">
                            <div class="col-md-12 btns-row">

                                <md-button class="md-button md-raised btn-w-md  md-default" ng-click="CancelReDisposition()">
                                    Cancel
                                </md-button>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || reassign.busy || disableSave" ng-click="CreateReDisposition()">
                                <span ng-show="! reassign.busy">Save</span>
                                <span ng-show="reassign.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                                <a href="{{host}}label/master/examples/ReassignSerialNumber.php?id={{searchSerialNO}}" target="_blank">
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                    data-ng-disabled="material_signup_form.$invalid || reassign.busy" >
                                    <span ng-show="! reassign.busy">Print</span>
                                    <span ng-show="reassign.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                    </md-button>
                                </a>
                            </div>
                        </div>



                    </form>
                </div>
            </md-card>

           <md-card class="no-margin-h" ng-show = "lastrecord !='' ">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Last Transaction:</span>
                    </div>
                </md-toolbar>
                <div class="col-md-12">
                 <table class="table">
                        <thead>
                            <tr>
                                <th>SN</th>
                                <th>MPN</th>
                                <th>COO</th>
                                <th>Part Type</th>
                                <th>Evalution Result</th>
                                <th>Disposition</th>
                                <th>Bin Name</th>
                                <th>workflow</th>
                                <th>Print</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{lastrecord.SerialNumber}}</td>
                                <td>{{lastrecord.UniversalModelNumber}}</td>
                                <td>{{lastrecord.COO}}</td>
                                <td>{{lastrecord.parttype}}</td>
                                <td>{{lastrecord.input}}</td>
                                <td>{{lastrecord.disposition}}</td>
                                <td>{{lastrecord.BinName}}</td>
                                <td>{{lastrecord.workflow}}</td>
                                <td md-cell class="actionicons" style="min-width: 60px;" ng-show = "lastrecord !='' ">
                                    <a href="{{host}}label/master/examples/ReassignSerialNumber.php?id={{lastrecord.SerialNumber}}" target="_blank">
                                    <i class="material-icons print" role="img" aria-label="print">print</i>
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </md-card>
        </article>
    </div>
</div>
